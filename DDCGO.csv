Include;ConceptName;Definition;Description
1;Game;Specifies a video game.;The Game class represents a video game entity within the ontology. It serves as the central hub that connects various facets of a game, such as visuals, audio, narrative, game design, level design, and gameplay. This class encapsulates the essence of a video game, providing a foundation for detailing all its integral components. Through properties like hasVisuals, hasAudio, and hasNarrative, the Game class links to other classes that define specific aspects of the game experience.
1;Visuals;Visuals contain any visual output of a game, ranging from photorealistic to caricaturized or abstract visuals.;The Visuals class encompasses all the visual elements of a game, including graphics, animations, and visual effects. This class captures the aesthetic essence of the game, whether it portrays realistic landscapes, stylized characters, or abstract art forms. By connecting to the Game class via the hasVisuals property, it specifies the visual characteristics that define the game's appearance and visual storytelling.
1;Audio;Audio includes background music such as a fully orchestrated soundtrack, sound effects, rewarding sounds, and voice-acted dialogue.;The Audio class covers all auditory components of a game. This includes the musical score that sets the mood, sound effects that provide feedback, and voice acting that brings characters to life. Linked to the Game class through the hasAudio property, it highlights how sound enhances the gaming experience by adding depth and immersion.
1;Narrative;Narrative contains the interactive story of a game, which makes up the game's plot.;The Narrative class represents the storyline and plot elements within a game. It includes the characters, events, and dialogues that engage the player in the game's universe. Through the hasNarrative property, it connects to the Game class, emphasizing the importance of storytelling in creating an immersive and emotionally compelling experience.
1;GameDesign;Game design contains all the game's mechanics that define the game's rules, providing structures and frames for play (e.g., winning and losing conditions) and actions available to the player.;The GameDesign class focuses on the underlying mechanics and rules that govern gameplay. It outlines how players interact with the game world, the challenges they face, and the goals they strive to achieve. By linking to the Game class via the hasGameDesign property, it details the structural framework that shapes the player's experience and defines what makes the game engaging and challenging.
1;LevelDesign;Level design includes the architecture of the spatial navigation of levels, determining how the player can progress from one point in the game to another.;The LevelDesign class pertains to the creation of game environments and levels. It involves the layout of spaces, placement of obstacles, and pathways that guide player progression. Connected to the Game class through the hasLevelDesign property, it highlights how thoughtful level design enhances exploration, pacing, and the overall enjoyment of the game.
1;Gameplay;Gameplay consists of the player's strategies while playing a game.;The Gameplay class represents the interactive experience from the player's perspective. It encompasses the strategies, decision-making processes, and play styles that players adopt. Through the hasGameplay property, it links to the Game class, illustrating how player interaction with game mechanics leads to a unique and personal experience for each individual.
1;hasVisuals;Specifies the visuals of a game.;The hasVisuals property connects a Game to its Visuals, indicating the visual content that defines the game's aesthetic. This relationship showcases how visuals contribute to the game's identity and player immersion, whether through stunning graphics or distinctive art styles.
1;hasAudio;Specifies the music and sound of a game.;The hasAudio property links a Game to its Audio, detailing the auditory elements that enhance the gameplay experience. This includes everything from ambient sounds and musical scores to character voices, all of which contribute to the game's atmosphere and emotional impact.
1;hasNarrative;Specifies the story and plot of a game.;The hasNarrative property associates a Game with its Narrative, outlining the storylines, character arcs, and plot developments that engage players. This connection underscores the role of storytelling in creating meaningful and memorable gaming experiences.
1;hasGameDesign;Specifies the rules and mechanics of a game.;The hasGameDesign property links a Game to its GameDesign, highlighting the mechanics that define how the game is played. This includes rules, controls, objectives, and other elements that shape the player's interaction with the game world.
1;hasLevelDesign;Specifies the levels of a game.;The hasLevelDesign property connects a Game to its LevelDesign, focusing on the spatial and environmental aspects of the game. This relationship emphasizes how level design affects gameplay dynamics, exploration, and the player's journey through the game.
1;hasGameplay;Specifies the gameplay of a game.;The hasGameplay property links a Game to its Gameplay, emphasizing the player's experience and interaction with the game mechanics. It reflects how the game engages players, challenges them, and allows them to develop strategies and skills.