Include;ConceptName;Description;Definition;Instances;;InstanceAttributes;TexName
;Action;An action is esentially an agent's response to tasks. Whereby tasks are created to be met or reached, an action is the atomic concept for achieving tasks. In the context of this document, an action is the building block of a process, and agents' ability to act towards its environment in general. Every action can be used to fulfill at least one task.;An action is the building block of agents' activities.;Attack, PickItem, GoToLocation, BrewPotion, MakeItem;;;Action
;Agent;An agent in the context of this document is a piece of software that can interact with its environment, act upon it, and, in case of an intelligent agent, reason upon their accessible knowledge. Indeed, an agent is \textit{anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.} \cite{Russell2010AImodern} In the organisational context of this document, a software agent is essentially a model of a real-life person.;A piece of software that can act upon its environment and perceive it.;;;;Agent
;Artefact;An artefact is, as of yet, a somewhat undefined concept, in the context of specifying its domain. Essentially, an artefact can be anything that is not classified using the other classes of this ontology. Furthermore, an artefact can be phisically representative (e.g. a chair), or an unphisical concept (e.g. knowledge). Artefacts therefore represent various concepts that the agents can interact with, or that affect the given environment or the given system, i.e. objects forming the environment.;An artefact is an otherwise unclassified element of an organisation system.;;;;Artefact
;Behaviour;In the context of this document, a behaviour is a set of actions or operations that an agent can perform in response to its environment or internal states. Behaviours are essential components that define how an agent acts and reacts, allowing it to achieve its objectives and respond to changes. Moreover, a behaviour is, in this context, a way of implementing a specific action and is that which an agent can actually run in runtime.;A behaviour is an implemented action, or a part of one, that defines how an agent interacts with its environment or internal states.;;;;
;Knowledge Artefact;A knowledge artefact is a piece of knowledge, or a set of knowledge terms available to agents within the system or within the \ac{IVE}. Depending on the wanted level of abstraction, a knowledge artefact may represent a database containing various pieces of knowledge accessible by sets of agents, or individual pieces of knowledge. In the terms of rather undefined artefact class, knowledge artefacts are yet to be perfected in the context of knowledge representation and their suitability for representing knowledge of \iac{IVE} or \iac{MAS}. Nonetheless, the artefact usually considered to be a knowledge artefact is an ontology.;Knowledge artefact is a piece of knowledge of an agent or an organisation.;organisational culture rulebook;;isAccessibleTo;KnowledgeArtefact
;Individual Knowledge Artefact;An individual knowledge artefact is a piece of knowledge or information that is specific to an individual agent. It represents the knowledge that an agent possesses individually, which may not be shared with other agents in the system.;An individual knowledge artefact is knowledge specific to an individual agent.;;;;
;Organisational Knowledge Artefact;An organisational knowledge artefact is a piece of knowledge or information that is shared among agents within an organisation. It represents the collective knowledge accessible to agents, facilitating collaboration and consistent understanding across the organisation.;An organisational knowledge artefact is knowledge shared among agents within an organisation.;;;;
;Software Artefact;A software artefact is a type of artefact that refers to software components, modules, or systems within the organisation or outside of one. These can include software applications, libraries, or any digital resources that agents can interact with or use to perform actions.;A software artefact is a software component or resource that agents can interact with.;;;;
;Artefact Resource;An artefact resource is a resource associated with an artefact that agents can utilise or access. It can be any supportive element that enhances the functionality of an artefact or provides additional capabilities to agents when interacting with the artefact.;An artefact resource is a resource associated with an artefact that agents can utilise.;;;;
;Objective;An objective is more general than a goal, although their definitions are rather similar. Fulfilling several goals can lead an organisational unit towards fulfilling a set objective. Thus, an objective is more suitable in the context of strategic planning, while a goal is more suitably used in the context of short-term planning.;An objective is a high-level goal the be met, suitable for the context of strategic planning.;LearnSpell, FindDragonEgg, Brew Hatching Potion;;triggers, hasCriteriaOfOrganizing, isAchievedBy;Objective
;Plan;A plan is a finite set of actions that leads to a specified goal. An optimal plan cannot be made shorter if the same goal is retained in the process. The plan concept is especially useful when observing \ac{BDI} agents, since it is driven by agents' desires and intentions.;A plan is a finite set of actions that leads to a specified goal.;How to solve the Quest for the DragonEgg;;;Plan
;Role;In the context of this document, a role is defined as a set of normative rules that are applicable to a particular part of the given organisation. Such normative rules are parts of the organisation's normative system, and can be grouped by specific criteria, thus forming roles. Roles are played by agents. When an agent plays a role, the role's constraints are applied to them, therefore constraining their possible actions, their perceivable goals, and their possibilities in general.;A role is a set of norms with a common denominator.;Wizard, Warrior, Ranged, Rogue;;isRoleIn, isRoleOf;Role
;Strategy;A strategy is, in the context of planning and shared organisational values, a long-term objective that is specified mosotly as a vision. It may consist of a number of objectives, quests, and similar. Strategy is therefore tentative in the context of plans of achieving it, but is versatile in terms of temporal likeness to change. Since it represents a long-term planning concept, a strategy is the main driving force of strategic alliances as agent coalitions meant to provide long-term suport to its members.;\textit{Strategy defines the long term objectives of an organization, action plans for their realization as well as tools on how to measure success.} \cite{Schatten2016roadmapIoE,Schatten2014towardsLSMAS};;;;Strategy
;Workspace;A workspace is the complete environment of a given system, including all the agents, artefacts, etc. What sets the concept of a workspace apart from the concept of an environment is the extent of the involved concepts, i.e. a workspace contains all the elements of an organisation and the whole system, while environment comprises only the elements that are external to the given organisation. It is worth noting that elements of the environment are an integral part of the whole system, since the life and activities of the given organisation are influenced by them.;A workspace is the union of all the elements of a system, including agents, artefacts, etc.;;;;Workspace
1;can play role;Associates an Agent with the Role(s) it can play within the organisation. Domain: Agent. Range: Role.;;;;;
1;is part of role;Indicates that a Role is a part of another Role, establishing hierarchical relationships between roles. Domain: Role. Range: Role.;;;;;
1;provides behaviour;Associates a Role with the Behaviour(s) it provides, defining the actions or activities an agent can perform when playing that role. Domain: Role. Range: Behaviour.;;;;;
1;can access artefact;Indicates that an Agent has access to a particular Artefact within the environment. Domain: Agent. Range: Artefact.;;;;;
1;has URI;Assigns a Uniform Resource Identifier (URI) to an Artefact for identification or access purposes. Domain: Artefact. Range: string (URI).;;;;;
1;has name;Assigns a human-readable name to an entity within the ontology. Domain: Any entity. Range: string.;;;;;
1;is before state;Defines the transition from one State Behaviour to another in a finite state machine, indicating the sequence of states. Domain: State Behaviour. Range: State Behaviour.;;;;;
1;has initial state;Associates a Finite State Machine Behaviour with its initial State Behaviour(s). Domain: Finite State Machine Behaviour. Range: State Behaviour.;;;;;
1;has final state;Associates a Finite State Machine Behaviour with its final State Behaviour(s). Domain: Finite State Machine Behaviour. Range: State Behaviour.;;;;;
1;has action;Associates a Process with the Action(s) that compose it. Domain: Process. Range: Action.;;;;;
1;has objective;Associates an Action with the Objective(s) it aims to achieve. Domain: Action. Range: Objective.;;;;;
1;has behaviour;Associates an Action with the Behaviour(s) required to perform it. Domain: Action. Range: Behaviour.;;;;;