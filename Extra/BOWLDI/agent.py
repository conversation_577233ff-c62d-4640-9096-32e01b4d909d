import asyncio
import time
import spade
from pprint import pprint
from spade_bdi.bdi import BDIAgent
from bowldi import BOW<PERSON>IConverter
import agentspeak

async def main():
    a = BDIAgent("alice@localhost", "password", "empty.asl")

    await a.start()
    await asyncio.sleep(1)

    converter = BOWLDIConverter(
        input_data_path="onto_example.rdf",
    )
    
    converter.add_beliefs_to_agent(a.bdi_agent)
    
    beliefs_dump = []
    
    for beliefs in a.bdi_agent.beliefs.values():
        for belief in beliefs:
            beliefs_dump.append(agentspeak.asl_repr(belief))

    beliefs_dump = "\n".join(beliefs_dump)

    BOWLDIConverter(
        input_data=beliefs_dump,
        output_data_path="output.owl"
    )
    
    await asyncio.sleep(1)
    await a.stop()

if __name__ == "__main__":
    spade.run(main())
