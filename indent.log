INFO:  latexindent version 3.23.6, 2024-01-17, a script to indent .tex files
       latexindent lives here: /usr/share/texlive/texmf-dist/scripts/latexindent/
       Thu May 29 18:11:35 2025
       Filename: /home/<USER>/Documents/Projekti FOI/MAGO/Deliverables/Phase 2/Sections/__latexindent_temp_GIVEn Conceptualisation.tex
INFO:  Processing switches:
       -y|--yaml: YAML settings specified via command line
       -c|--cruft: cruft directory
INFO:  Directory for backup files and /home/<USER>/Documents/Projekti FOI/MAGO//indent.log:
       /home/<USER>/Documents/Projekti FOI/MAGO/
INFO:  YAML settings read: defaultSettings.yaml
       Reading defaultSettings.yaml from /usr/share/texlive/texmf-dist/scripts/latexindent/defaultSettings.yaml
INFO:  YAML reading settings
       Home directory is /home/<USER>
       latexindent.pl didn't find indentconfig.yaml or .indentconfig.yaml
       see all possible locations: https://latexindentpl.readthedocs.io/en/latest/sec-appendices.html#indentconfig-options)
INFO:  YAML settings read: -y switch
       YAML setting: defaultIndent:'    '
       single-quoted string found in -y switch: '    ', substitute to     
       Updating mainSettings with defaultIndent:     
INFO:  Phase 1: searching for objects
INFO:  Phase 2: finding surrounding indentation
INFO:  Phase 3: indenting objects
INFO:  Phase 4: final indentation check
INFO:  Output routine:
       Not outputting to file; see -w and -o switches for more options.
       --------------
INFO:  Please direct all communication/issues to:
        https://github.com/cmhughes/latexindent.pl
