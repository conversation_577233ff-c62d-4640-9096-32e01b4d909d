Include;ConceptName;Description;Definition;Instances;TexName
1;Action;An action is essentially an agent's response to tasks. Whereby tasks are created to be met or reached, an action is the atomic concept for achieving tasks. In the context of this document, an action is the building block of a process, and agents' ability to act towards its environment in general. Every action can be used to fulfill at least one task.;An action is the building block of agents' activities.;Attack, PickItem, GoToLocation, BrewPotion, MakeItem;Action
1;Agent;An agent in the context of this document is a piece of software that can interact with its environment, act upon it, and, in case of an intelligent agent, reason upon their accessible knowledge. Indeed, an agent is \textit{anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.} \cite{Russell2010AImodern} In the organisational context of this document, a software agent is essentially a model of a real-life person.;A piece of software that can act upon its environment and perceive it.;;Agent
1;Artefact;An artefact is, as of yet, a somewhat undefined concept, in the context of specifying its domain. Essentially, an artefact can be anything that is not classified using the other classes of this ontology. Furthermore, an artefact can be physically representative (e.g. a chair), or an unphysical concept (e.g. knowledge). Artefacts therefore represent various concepts that the agents can interact with, or that affect the given environment or the given system, i.e. objects forming the environment.;An artefact is an otherwise unclassified element of an organisation system.;;Artefact
1;Behaviour;In the context of this document, a behaviour is a set of actions or operations that an agent can perform in response to its environment or internal states. Behaviours are essential components that define how an agent acts and reacts, allowing it to achieve its objectives and respond to changes. Moreover, a behaviour is, in this context, a way of implementing a specific action and is that which an agent can actually run in runtime.;A behaviour is an implemented action, or a part of one, that defines how an agent interacts with its environment or internal states.;;
1;Knowledge Artefact;A knowledge artefact is a piece of knowledge, or a set of knowledge terms available to agents within the system or within the \ac{IVE}. Depending on the wanted level of abstraction, a knowledge artefact may represent a database containing various pieces of knowledge accessible by sets of agents, or individual pieces of knowledge. In the terms of rather undefined artefact class, knowledge artefacts are yet to be perfected in the context of knowledge representation and their suitability for representing knowledge of \iac{IVE} or \iac{MAS}. Nonetheless, the artefact usually considered to be a knowledge artefact is an ontology.;Knowledge artefact is a piece of knowledge of an agent or an organisation.,organisational culture rulebook,KnowledgeArtefact
1;Role;In the context of this document, a role is defined as a set of normative rules that are applicable to a particular part of the given organisation. Such normative rules are parts of the organisation's normative system, and can be grouped by specific criteria, thus forming roles. Roles are played by agents. When an agent plays a role, the role's constraints are applied to them, therefore constraining their possible actions, their perceivable goals, and their possibilities in general.;A role is a set of norms with a common denominator.;Wizard, Warrior, Ranged, Rogue;Role
1;Workspace;A workspace is the complete environment of a given system, including all the agents, artefacts, etc. What sets the concept of a workspace apart from the concept of an environment is the extent of the involved concepts, i.e. a workspace contains all the elements of an organisation and the whole system, while environment comprises only the elements that are external to the given organisation. It is worth noting that elements of the environment are an integral part of the whole system, since the life and activities of the given organisation are influenced by them.;A workspace is the union of all the elements of a system, including agents, artefacts, etc.;;Workspace
1;Inhabitant Agent,Agents that can be physically represented within \iac{IVE} are called inhabitant agents. These agents can be of artificial or real-world nature. Usually various \ac{IVE} artefacts exist within the \ac{IVE} that represent various inhabitant agents \cite{Rincon2014}. It could be said that these agents have their habitats within their respective \acp{IVE}.,Every agent that can be represented as physically present in an \ac{IVE} is considered an inhabitant agent.;;InhabitantAgent
1;Intelligent Virtual Environment;Intelligent virtual environments are researched as an area on the intersection of two aspects pertaining to the concept of artificial intelligence, if only but marginally: intelligent tools and techniques that are embodied in autonomous agents (real-life and digital alike), and effective ways of representing them, along with various means of achieving different kinds of interaction amongst them \cite{rincon2014DevelopingIntelligentVirtual,luck2000ApplyingArtificialIntelligence}. In other words, \iac{IVE} is a concepte that represents a virtual environment whose main goal is simulating a segment of the real world, populated by artificial autonomous entities (agents). \cite{rincon2014DevelopingIntelligentVirtual};An intelligent virtual environment is a virtual environment that simulates the real world, and is populated by autonomous intelligent entities. \cite{rincon2014DevelopingIntelligentVirtual};;IntelligentVirtualEnvironment
1;IVE Law;A special kind of a norm, an \ac{IVE} law is a norm that is constrained by its applicability to a specific physical space, i.e. a specific \ac{IVE} workspace. Being applicable to only a restricted area means that every \ac{IVE} law is valid only within the bounds of the given area (\iac{IVE} workspace), and never outside of that specified space. This kind of a norm is the key constraint of the concept of a situated organisational unit.;\Iac{IVE} law is a norm that is valid only within a specified physical space (\iac{IVE} workspace).;;IVELaw
1;IVE Workspace;Complimentary to the concept of a workspace, \iac{IVE} workspace represents a physical location, or a physically describable location.;;;IVEWorkspace
1;Objective;An objective is more general than a goal, although their definitions are rather similar. Fulfilling several goals can lead an organisational unit towards fulfilling a set objective. Thus, an objective is more suitable in the context of strategic planning, while a goal is more suitably used in the context of short-term planning.;An objective is a high-level goal the be met, suitable for the context of strategic planning.;;Objective
1;Observable Property;This is a property of an artefact located in \iac{IVE} that is observable by other agents located within the same \ac{IVE}. These are tighly connected to the concept of observable events, and can be influenced upon by an operation.;An observable property is a property of an artefact that can be observed by agents in the same \ac{IVE}.;;ObservableProperty
1;Physical Artefact;Every concept that describes objects that can be physically represented (e.g. a top hat), i.e. embodied and positioned on a topological map, and as such included in \iac{IVE} are classified as physical artefacts. Such elements have their role to play in the given \ac{IVE} and usually contain a defined interface that governs the process of interaction of an agent with the given physical artefact.;Physical artefacts are all the concepts that can be physically represented and included in \iac{IVE}.;;PhysicalArtefact
1;Physical Property;Physical properties are key elements of physical artefacts, i.e. artefacts that can be visualised in a physical space. Usually when an artefact is used, a physical event is generated, and a physical property is modified.;;;PhysicalProperty
1;Goal;A goal is broadly defined as a result or achievement towards which effort is directed\footnote{http://www.dictionary.com/browse/goal}. In the context of this document, a goal is a form of an objective. A goal is an end to be met or reached, and can consist of several sub-goals.;A goal is a result towards which effort is directed - an end to be met.;;Goal
1;Quest;A quest is a similar to a goal, but it has a defined beginning and a defined end, i.e. a starting situation, and an ending situation\footnote{https://medium.com/the-mission/why-you-should-change-your-goals-into-quests-2467bbef9867}. In the context of \acp{MMORPG}, a quest is what drives a story, and, in principle, motivates the player to continue playing the game. Furthermore, a quest is often given to the player by an in-game character. A quest usually has various stages, and represents a challenge for the given player, thus embarking them on an adventure.;A quest is similar to a goal, but has a defined starting and ending situations.;;Quest
1;Strategic Alliance;An alliance that is aimed at forming long-lasting partnerships consisting of organisations of various forms is dubbed a strategic alliance. A strategic alliance is formed around a strategy as a long-term objective that is shared amongst the strategic alliance members. Norms and regulations governing the expected behaviour within the strategic alliance are expected to be accepted by all the members, old and new alike.;Strategic alliance is a form of a long-lasting partnership of organisations of various forms, formed around a shared strategy, or a strategic goal.;;StrategicAlliance
1;Task;A task is the building block of a quest, i.e. its elementary part. A quest is built of atomic tasks that are easier to follow in execution phase, rather than the overview provided by the main definition of a quest. In \acp{MMORPG} a quest could demand an item to be retrieved, yet such a simple-sounding quest could consist of various tasks that have to be fulfilled in order for the main quest to be finished. The relation of quest and task concepts can be recursive\footnote{define further, i.e. a main quest in a game can consist of several tasks, which can be quests themselves}.;A task is the building block of a quest.;;Task
