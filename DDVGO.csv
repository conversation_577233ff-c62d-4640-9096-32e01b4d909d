Include;ConceptName;Definition;Description
1;Achievement;An achievement is a reward gained in a game due to some event accomplished in the game.;The Achievement class represents rewards that players earn by completing specific tasks, reaching milestones, or exhibiting particular skills within a game. These achievements serve as incentives and recognition for players, enhancing engagement and providing goals to strive for. Achievements are linked to Games through the hasAchievement property, indicating which rewards are available in each game. Players can own achievements \mintedInline{ownsAchievement}, and they can be earned during gameplay sessions \mintedInline{involvesAchievement}, enriching the player's gaming experience.
1;Character;Any actor that can exist in a game.;The Character class encompasses all entities that act within the game world, including player-controlled avatars and non-player characters \mintedInline{NPCs}. Characters can be humans, animals, vehicles, or even abstract representations like paddles in Pong. They are central to gameplay, influencing how players interact with the game. Characters are associated with Games via the hasCharacter property, and players can own characters \mintedInline{ownsCharacter}. During gameplay sessions, characters participate and may trigger events \mintedInline{isEventTriggeredByCharacter}, highlighting their dynamic role in the game.
1;Feature;Describes an ability or characteristic.;The Feature class details specific abilities, attributes, or properties that an Item may possess within a game. For instance, a sword might have the feature flaming; adding fire damage, or boots might grant the ability flight. The hasFeature property connects items to their features, enriching the item's functionality and strategic value in gameplay.
1;Game;Describes a game product that can be played by a player.;The Game class represents the game itself as a product and interactive experience. It serves as a hub linking to various components such as Achievements, Characters, Items, Genres, PlayingAreas, and Leaderboards. Through properties like hasAchievement and hasCharacter, it details the content and features that define the game. Players interact with games \mintedInline{playsGame}, and sessions \mintedInline{Session} represent periods during which the game is played, capturing the dynamic nature of gameplay.
1;Genre;Describes the genre a game belongs to.;The Genre class categorizes games based on their style, mechanics, and thematic elements, such as RPGs, simulators, or adventure games. By linking Games to their genres through the hasGameGenre property, it provides context and helps players understand what to expect from the game, aiding in discovery and classification.
1;Item;Portrays any item that exists in a game.;The Item class includes all objects that can be found or used within a game, ranging from weapons and tools to collectibles and decorative objects. Items are integral to gameplay, often enhancing a character's abilities or serving as objectives. They are connected to Games via the hasItem property and can have specific features \mintedInline{hasFeature}. Characters can own items \mintedInline{ownsItem}, and items may be involved in events \mintedInline{isEventRelatedToItem}, illustrating their multifaceted role in the game.
1;Leaderboard;Describes a ranking system of the players.;The Leaderboard class represents competitive ranking systems that track player performance, such as high scores, fastest times, or most victories. Leaderboards encourage competition and replayability, fostering a sense of community. They are linked to Games through the hasLeaderboard property, indicating where players can compete and compare achievements.
1;Player;The entity playing the game.;The Player class represents individuals who engage with the game, whether human players or AI-controlled agents. Players are central to the gaming experience, bringing the game to life through interaction. They have profiles that may include usernames \mintedInline{username}, real-world locations \mintedInline{livesIn}, and relationships with other players \mintedInline{isFriendWithPlayer}. Players own characters \mintedInline{ownsCharacter} and achievements \mintedInline{ownsAchievement}, participate in sessions \mintedInline{involvesPlayer}, and their actions can trigger events \mintedInline{isEventAssociatedToPlayer}, capturing the richness of the player experience.
1;Session;Describes a session of gameplay.;The Session class captures specific instances of gameplay, such as a match, level, or any defined period during which the game is played. Sessions are linked to Games \mintedInline{isSessionInGame}, involve Players \mintedInline{involvesPlayer} and Characters \mintedInline{involvesCharacter}, and can include events and achievements earned during that time \mintedInline{involvesAchievement}. Sessions provide a framework for analyzing gameplay, understanding player behavior, and tracking progress over time.
1;PlayingArea;Describes the place where the gameplay takes place.;The PlayingArea class defines the environments or settings within a game where the action unfolds. This could be a specific level, map, world, or any spatial context like a racetrack, battlefield, or fantasy realm. Through the hasPlayingArea property, Games are connected to their playing areas, emphasizing the importance of environment in shaping gameplay and player immersion.
1;InstantaneousEvent;An event that happens during gameplay at a certain moment in time.;The InstantaneousEvent class encompasses events that occur at specific moments during gameplay, triggered by player actions, game mechanics, or other factors. These events can include earning an achievement \mintedInline{unlocksAchievement}, defeating an enemy, or making a purchase \mintedInline{InAppPurchaseEvent}. Instantaneous events are linked to Games \mintedInline{isEventInGame}, Sessions \mintedInline{isEventInSession}, Players \mintedInline{isEventAssociatedToPlayer}, Characters \mintedInline{isEventTriggeredByCharacter}, and Items \mintedInline{isEventRelatedToItem}, providing a detailed account of the dynamic occurrences within the game.