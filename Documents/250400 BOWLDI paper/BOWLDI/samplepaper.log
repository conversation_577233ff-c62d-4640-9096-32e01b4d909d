This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.1.27)  9 MAY 2025 12:43
entering extended mode
 \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex"
(/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(./cas-dc.cls
Document Class: cas-dc 2021/05/11, 2.3: Formatting class for CAS double column articles
\c@blind=\count187
\@bls=\dimen140
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/fleqn.clo
File: fleqn.clo 2016/12/29 v1.2b Standard LaTeX option (flush left equations)
\mathindent=\skip48
Applying: [2015/01/01] Make \[ robust on input line 50.
LaTeX Info: Redefining \[ on input line 51.
Already applied: [0000/00/00] Make \[ robust on input line 62.
Applying: [2015/01/01] Make \] robust on input line 74.
LaTeX Info: Redefining \] on input line 75.
Already applied: [0000/00/00] Make \] robust on input line 83.
) (/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen144
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count196
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count197
\leftroot@=\count198
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count199
\DOTSCASE@=\count266
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count267
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count268
\dotsspace@=\muskip16
\c@parentequation=\count269
\dspbrk@lvl=\count270
\tag@help=\toks19
\row@=\count271
\column@=\count272
\maxfields@=\count273
\andhelp@=\toks20
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-01-22 L3 programming layer (loader) 
 (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count274
\l__pdf_internal_box=\box53
)) (/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2023-10-10 L3 Experimental document command parser
) (/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count275
) (/usr/share/texlive/texmf-dist/tex/latex/preprint/balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen153
) (/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen154
\lightrulewidth=\dimen155
\cmidrulewidth=\dimen156
\belowrulesep=\dimen157
\belowbottomsep=\dimen158
\aboverulesep=\dimen159
\abovetopsep=\dimen160
\cmidrulesep=\dimen161
\cmidrulekern=\dimen162
\defaultaddspace=\dimen163
\@cmidla=\count276
\@cmidlb=\count277
\@aboverulesep=\dimen164
\@belowrulesep=\dimen165
\@thisruleclass=\count278
\@lastruleclass=\count279
\@thisrulewidth=\dimen166
) (/usr/share/texlive/texmf-dist/tex/latex/makecell/makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells
 (/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen167
\ar@mcellbox=\box54
\extrarowheight=\dimen168
\NC@list=\toks23
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box55
)
\rotheadsize=\dimen169
\c@nlinenum=\count280
\TeXr@lab=\toks24
) (/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip56
\multirow@cntb=\count281
\multirow@dima=\skip57
\bigstrutjot=\dimen170
) (/usr/share/texlive/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2022/06/20 v1.0f Color table columns (DPC)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx))
\everycr=\toks25
\minrowclearance=\skip58
\rownum=\count282
) (/usr/share/texlive/texmf-dist/tex/latex/tools/dcolumn.sty
Package: dcolumn 2023/07/08 v1.06 decimal alignment package (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip settings
\@dblbotnum=\count283
\c@dblbotnumber=\count284
) (/usr/share/texlive/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.sty (/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count285
\xs_countb=\count286
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (/usr/share/texlive/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2023/07/05 v6.0f a miscellany of footnote facilities
\FN@temptoken=\toks26
\footnotemargin=\dimen171
\@outputbox@depth=\dimen172
Package footmisc Info: Declaring symbol style bringhurst on input line 696.
Package footmisc Info: Declaring symbol style chicago on input line 704.
Package footmisc Info: Declaring symbol style wiley on input line 713.
Package footmisc Info: Declaring symbol style lamport-robust on input line 724.
Package footmisc Info: Declaring symbol style lamport* on input line 744.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 765.
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
LaTeX Info: Redefining \color on input line 758.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/svgnam.def
File: svgnam.def 2023/11/15 v3.01 Predefined colors according to SVG 1.1 (UK)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (/usr/share/texlive/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count287
)
\@linkdim=\dimen173
\Hy@linkcounter=\count288
\Hy@pagecounter=\count289
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count290
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count291
 (/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen174
 (/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count292
\Field@Width=\dimen175
\Fld@charsize=\dimen176
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring ON on input line 6081.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count293
\c@Item=\count294
\c@Hfootnote=\count295
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX
 (/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count296
\c@bookmark@seq@number=\count297
 (/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip59
) (./cas-common.sty
\l_stm_title_before_dim=\dimen177
\l_stm_title_after_dim=\dimen178
\g_ead_int=\count298
\g_uad_int=\count299
\@eadauthor=\toks27
\g_stm_tnote_int=\count300
\g_stm_fnote_int=\count301
\g_stm_cor_int=\count302
\g_stm_au_int=\count303
\g_stm_aau_int=\count304
\l_autype_int=\count305
\l_stm_augroup_before_dim=\dimen179
\l_stm_augroup_after_dim=\dimen180
\l_stm_augroup_lskip_tl=\dimen181
\l_stm_augroup_rskip_tl=\dimen182
\g_stm_aff_int=\count306
\g_stm_aff_ext_int=\count307
\g_stm_aff_int_int=\count308
\g_stm_fn_aff_ext_int=\count309
\g_stm_fn_aff_int_int=\count310
\l_stm_aff_before_dim=\dimen183
\l_stm_aff_after_dim=\dimen184
\l_stm_aff_lskip_dim=\dimen185
\l_stm_aff_rskip_dim=\dimen186
\g_stm_augr_int=\count311
\g_stm_aaugr_int=\count312
 (/usr/share/texlive/texmf-dist/tex/latex/moreverb/moreverb.sty
Package: moreverb 2008/06/03 v2.3a `more' verbatim facilities
 (/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks28
\verbatim@line=\toks29
\verbatim@in@stream=\read2
)
\tab@position=\count313
\tab@size=\count314
\listing@line=\count315
)
\g_stm_abs_box=\box56
\casgrabsbox=\box57
\casauhlbox=\box58
\g_stm_key_box=\box59
\g_stm_jtype_int=\count316
\g_stm_blind_int=\count317
\c@au=\count318
\c@cnote=\count319
\c@tnote=\count320
\c@fnote=\count321
\c@aff=\count322
\g_stm_notes_box=\box60
\g_stm_front_box=\box61
\paraindent=\dimen187
\subparaindent=\dimen188
\FullWidth=\dimen189
\l_tbl_width_dim=\dimen190
\l_tbl_abovecap_skip=\skip60
\l_tbl_belowcap_skip=\skip61
\l_tbl_abovetbl_skip=\skip62
\l_tbl_belowtbl_skip=\skip63
\l_fig_width_dim=\dimen191
\cascaptionbox=\box62
\l_fig_abovecap_skip=\skip64
\l_fig_belowcap_skip=\skip65
\l_fig_abovefig_skip=\skip66
\l_fig_belowfig_skip=\skip67
 (/usr/share/texlive/texmf-dist/tex/latex/wrapfig/wrapfig.sty
\wrapoverhang=\dimen192
\WF@size=\dimen193
\c@WF@wrappedlines=\count323
\WF@box=\box63
\WF@everypar=\toks30
Package: wrapfig 2003/01/31  v 3.6
)
\l_wrap_figwidth_dim=\dimen194
\l_wrap_fighspace_dim=\dimen195
\l_wrap_figvspace_dim=\dimen196
\l_wrap_fighcorr_dim=\dimen197
\l_wrap_figvcorr_dim=\dimen198
\l_above_bio_dim=\dimen199
\l_wrap_figlcorr_int=\count324
\l_wrap_figlines_int=\count325
\l_bio_text_box=\box64
\c@ca_biography_ctr=\count326
\l_ca_temp_inta=\count327
\leftMargin=\dimen256
\@enLab=\toks31
\@sep=\skip68
\@@sep=\skip69
) (/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texlive/texmf-dist/tex/latex/stix/stix.sty
Package: stix 2018/04/17 v1.1.3-latex STIX fonts support package
 (/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
LaTeX Font Info:    Changing ? sub-encoding to TS1/0 on input line 78.
)
Now handling font encoding LS1 ...
... no UTF-8 mapping file for font encoding LS1
Now handling font encoding LS2 ...
... no UTF-8 mapping file for font encoding LS2
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 76.
LaTeX Font Info:    Encoding `OML' has changed to `LS1' for symbol font
(Font)              `letters' in the math version `normal' on input line 76.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> LS1/stix/m/it on input line 76.
LaTeX Font Info:    Encoding `OML' has changed to `LS1' for symbol font
(Font)              `letters' in the math version `bold' on input line 76.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> LS1/stix/m/it on input line 76.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 77.
LaTeX Font Info:    Encoding `OT1' has changed to `LS1' for symbol font
(Font)              `operators' in the math version `normal' on input line 77.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> LS1/stix/m/n on input line 77.
LaTeX Font Info:    Encoding `OT1' has changed to `LS1' for symbol font
(Font)              `operators' in the math version `bold' on input line 77.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> LS1/stix/m/n on input line 77.
\symbold-operators=\mathgroup6
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 79.
LaTeX Font Info:    Encoding `OMS' has changed to `LS1' for symbol font
(Font)              `symbols' in the math version `normal' on input line 79.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LS1/stixscr/m/n on input line 79.
LaTeX Font Info:    Encoding `OMS' has changed to `LS1' for symbol font
(Font)              `symbols' in the math version `bold' on input line 79.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LS1/stixscr/m/n on input line 79.
\symsymbols2=\mathgroup7
\symsymbols3=\mathgroup8
\symsymbols4=\mathgroup9
\symintegrals=\mathgroup10
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 84.
LaTeX Font Info:    Encoding `OMX' has changed to `LS2' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 84.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LS2/stixex/m/n on input line 84.
LaTeX Font Info:    Encoding `OMX' has changed to `LS2' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 84.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LS2/stixex/m/n on input line 84.
\symarrows1=\mathgroup11
\symarrows2=\mathgroup12
\symarrows3=\mathgroup13
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  LS1/stix/m/it --> LS1/stix/b/it on input line 88.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  LS1/stix/m/n --> LS1/stix/b/n on input line 89.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LS1/stixscr/m/n --> LS1/stixscr/b/n on input line 90.
LaTeX Font Info:    Overwriting symbol font `symbols2' in version `bold'
(Font)                  LS1/stixfrak/m/n --> LS1/stixfrak/b/n on input line 91.
LaTeX Font Info:    Overwriting symbol font `symbols3' in version `bold'
(Font)                  LS1/stixbb/m/n --> LS1/stixbb/b/n on input line 92.
LaTeX Font Info:    Overwriting symbol font `symbols4' in version `bold'
(Font)                  LS1/stixbb/m/it --> LS1/stixbb/b/it on input line 93.
LaTeX Font Info:    Overwriting symbol font `integrals' in version `bold'
(Font)                  LS2/stixcal/m/n --> LS2/stixcal/b/n on input line 94.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LS2/stixex/m/n --> LS2/stixex/b/n on input line 95.
LaTeX Font Info:    Overwriting symbol font `arrows1' in version `bold'
(Font)                  LS1/stixsf/m/n --> LS1/stixsf/b/n on input line 96.
LaTeX Font Info:    Overwriting symbol font `arrows2' in version `bold'
(Font)                  LS1/stixsf/m/it --> LS1/stixsf/b/it on input line 97.
LaTeX Font Info:    Overwriting symbol font `arrows3' in version `bold'
(Font)                  LS2/stixtt/m/n --> LS2/stixtt/b/n on input line 98.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 99.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 102.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 106.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 108.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 109.
LaTeX Info: Redefining \yen on input line 456.
LaTeX Info: Redefining \circledR on input line 458.
LaTeX Info: Redefining \checkmark on input line 460.
LaTeX Info: Redefining \maltese on input line 461.
LaTeX Font Info:    Redeclaring math accent \grave on input line 698.
LaTeX Font Info:    Redeclaring math accent \acute on input line 699.
LaTeX Font Info:    Redeclaring math accent \hat on input line 700.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 701.
LaTeX Font Info:    Redeclaring math accent \bar on input line 702.
LaTeX Font Info:    Redeclaring math accent \breve on input line 703.
LaTeX Font Info:    Redeclaring math accent \dot on input line 704.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 705.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 707.
LaTeX Font Info:    Redeclaring math accent \check on input line 708.
LaTeX Font Info:    Redeclaring math accent \vec on input line 716.
LaTeX Font Info:    Redeclaring math accent \widehat on input line 728.
LaTeX Font Info:    Redeclaring math accent \widetilde on input line 729.
LaTeX Info: Redefining \vdots on input line 1518.
LaTeX Info: Redefining \cdots on input line 1522.
LaTeX Font Info:    Redeclaring math symbol \mathsterling on input line 1978.
LaTeX Info: Redefining \not on input line 2144.
) (/usr/share/texlive/texmf-dist/tex/latex/inconsolata/inconsolata.sty
Package: inconsolata 2019/05/17 v1.12
 `inconsolata-zi4' v1.12, 2019/05/17 Text macros for Inconsolata (msharpe) (/usr/share/texlive/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks32
\XKV@tempa@toks=\toks33
)
\XKV@depth=\count328
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\zifour@ocount=\count329
) (/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count330
\Gm@cntv=\count331
\c@Gm@tempcnt=\count332
\Gm@bindingoffset=\dimen257
\Gm@wd@mp=\dimen258
\Gm@odd@mp=\dimen259
\Gm@even@mp=\dimen260
\Gm@layoutwidth=\dimen261
\Gm@layoutheight=\dimen262
\Gm@layouthoffset=\dimen263
\Gm@layoutvoffset=\dimen264
\Gm@dimlist=\toks34
)) (/usr/share/texlive/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip70
\bibsep=\skip71
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count333
) (/usr/share/texlive/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
) (/usr/share/texlive/texmf-dist/tex/latex/csquotes/csquotes.sty
Package: csquotes 2022-09-14 v5.2n context-sensitive quotations (JAW)
\csq@reset=\count334
\csq@gtype=\count335
\csq@glevel=\count336
\csq@qlevel=\count337
\csq@maxlvl=\count338
\csq@tshold=\count339
\csq@ltx@everypar=\toks35
 (/usr/share/texlive/texmf-dist/tex/latex/csquotes/csquotes.def
File: csquotes.def 2022-09-14 v5.2n csquotes generic definitions (JAW)
)
Package csquotes Info: Trying to load configuration file 'csquotes.cfg'...
Package csquotes Info: ... configuration file loaded successfully.
 (/usr/share/texlive/texmf-dist/tex/latex/csquotes/csquotes.cfg
File: csquotes.cfg 
)) (/usr/share/texlive/texmf-dist/tex/generic/soul/soul.sty
Package: soul 2023-06-14 v3.1 Permit use of UTF-8 characters in soul (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/soul/soul-ori.sty
Package: soul-ori 2023-06-14 v3.1 letterspacing/underlining (mf)
\SOUL@word=\toks36
\SOUL@lasttoken=\toks37
\SOUL@syllable=\toks38
\SOUL@cmds=\toks39
\SOUL@buffer=\toks40
\SOUL@token=\toks41
\SOUL@syllgoal=\dimen265
\SOUL@syllwidth=\dimen266
\SOUL@charkern=\dimen267
\SOUL@hyphkern=\dimen268
\SOUL@dimen=\dimen269
\SOUL@dimeni=\dimen270
\SOUL@minus=\count340
\SOUL@comma=\count341
\SOUL@apo=\count342
\SOUL@grave=\count343
\SOUL@spaceskip=\skip72
\SOUL@ttwidth=\dimen271
\SOUL@uldp=\dimen272
\SOUL@ulht=\dimen273
) (/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)) (/usr/share/texlive/texmf-dist/tex/latex/lipsum/lipsum.sty (/usr/share/texlive/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2023-10-10 LaTeX2e option processing using LaTeX3 keys
)
Package: lipsum 2021-09-20 v2.7 150 paragraphs of Lorem Ipsum dummy text
\g__lipsum_par_int=\count344
\l__lipsum_a_int=\count345
\l__lipsum_b_int=\count346
 (/usr/share/texlive/texmf-dist/tex/latex/lipsum/lipsum.ltd.tex)) (/usr/share/texlive/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count347
\lst@gtempboxa=\box65
\lst@token=\toks42
\lst@length=\count348
\lst@currlwidth=\dimen274
\lst@column=\count349
\lst@pos=\count350
\lst@lostspace=\dimen275
\lst@width=\dimen276
\lst@newlines=\count351
\lst@lineno=\count352
\lst@maxwidth=\dimen277
 (/usr/share/texlive/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2023/02/27 1.9 (Carsten Heinz)
\c@lstnumber=\count353
\lst@skipnumbers=\count354
\lst@framebox=\box66
) (/usr/share/texlive/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2023/02/27 1.9 listings configuration
))
Package: listings 2023/02/27 1.9 (Carsten Heinz)

\csgdef{mark@title}{1,2}
Package csquotes Info: Checking for multilingual support...
Package csquotes Info: ... none found.
LaTeX Font Info:    Trying to load font information for T1+stix on input line 69.
(/usr/share/texlive/texmf-dist/tex/latex/stix/t1stix.fd
File: t1stix.fd 2015/04/17 v1.1.2-latex STIX T1 font definitions
) (./samplepaper.aux)
\openout1 = `samplepaper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 69.
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for LS1/stix/m/n on input line 69.
LaTeX Font Info:    Trying to load font information for LS1+stix on input line 69.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls1stix.fd
File: ls1stix.fd 2015/04/17 v1.1.2-latex STIX LS1 font definitions
)
LaTeX Font Info:    ... okay on input line 69.
LaTeX Font Info:    Checking defaults for LS2/stix/m/n on input line 69.
LaTeX Font Info:    Trying to load font information for LS2+stix on input line 69.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls2stix.fd
File: ls2stix.fd 2015/04/17 v1.1.2-latex STIX LS2 font definitions
)
LaTeX Font Info:    ... okay on input line 69.
 (/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count355
\scratchdimen=\dimen278
\scratchbox=\box67
\nofMPsegments=\count356
\nofMParguments=\count357
\everyMPshowfont=\toks43
\MPscratchCnt=\count358
\MPscratchDim=\dimen279
\MPnumerator=\count359
\makeMPintoPDFobject=\count360
\everyMPtoPDFconversion=\toks44
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Info: Command `\dddot' is already robust on input line 69.
LaTeX Info: Command `\ddddot' is already robust on input line 69.
Package hyperref Info: Link coloring ON on input line 69.
 (./samplepaper.out) (./samplepaper.out)
\@outlinefile=\write3
\openout3 = `samplepaper.out'.

 (/usr/share/texlive/texmf-dist/tex/latex/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verbatim
)
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(51.4995pt, 494.50888pt, 51.4995pt)
* v-part:(T,H,B)=(55.48286pt, 689.4103pt, 51.784pt)
* \paperwidth=597.50787pt
* \paperheight=796.67715pt
* \textwidth=494.50888pt
* \textheight=689.4103pt
* \oddsidemargin=-20.7705pt
* \evensidemargin=-20.7705pt
* \topmargin=-40.78712pt
* \headheight=12.0pt
* \headsep=12.0pt
* \topskip=10.0pt
* \footskip=12.0pt
* \marginparwidth=57.0pt
* \marginparsep=11.0pt
* \columnsep=18.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\c@lstlisting=\count361
\verbatim@out=\write4
\openout4 = `samplepaper.abs'.

LaTeX Font Info:    Trying to load font information for TS1+stix on input line 102.
(/usr/share/texlive/texmf-dist/tex/latex/stix/ts1stix.fd
File: ts1stix.fd 2015/04/17 v1.1.2-latex STIX TS1 font definitions
) [0

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texlive/texmf-dist/fonts/enc/dvips/stix/stix-t1.enc}{/usr/share/texlive/texmf-dist/fonts/enc/dvips/stix/stix-ts1.enc}]
LaTeX Font Info:    Trying to load font information for LS1+stixscr on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls1stixscr.fd
File: ls1stixscr.fd 2015/04/17 v1.1.2-latex STIX script LS1 font definitions
)
LaTeX Font Info:    Trying to load font information for LS2+stixex on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls2stixex.fd
File: ls2stixex.fd 2015/04/17 v1.1.2-latex STIX extentions LS2 font definitions
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for LS1+stixfrak on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls1stixfrak.fd
File: ls1stixfrak.fd 2015/04/17 v1.1.2-latex STIX fraktur LS1 font definitions
)
LaTeX Font Info:    Trying to load font information for LS1+stixbb on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls1stixbb.fd
File: ls1stixbb.fd 2015/04/17 v1.1.2-latex STIX blackboard LS1 font definitions
)
LaTeX Font Info:    Trying to load font information for LS2+stixcal on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls2stixcal.fd
File: ls2stixcal.fd 2015/04/17 v1.1.2-latex STIX calligraphic LS2 font definitions
)
LaTeX Font Info:    Trying to load font information for LS1+stixsf on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls1stixsf.fd
File: ls1stixsf.fd 2015/04/17 v1.1.2-latex STIX sans-serif LS1 font definitions
)
LaTeX Font Info:    Trying to load font information for LS2+stixtt on input line 112.
 (/usr/share/texlive/texmf-dist/tex/latex/stix/ls2stixtt.fd
File: ls2stixtt.fd 2015/04/17 v1.1.2-latex STIX typewriter LS2 font definitions
)
Overfull \hbox (123.62721pt too wide) detected at line 112
[]
 []

(./samplepaper.abs)

Package hyperref Warning: Ignoring empty anchor on input line 112.


Package hyperref Warning: Ignoring empty anchor on input line 112.

<thumbnails/cas-email.jpeg, id=12, 225.84375pt x 225.84375pt>
File: thumbnails/cas-email.jpeg Graphic file (type jpg)
<use thumbnails/cas-email.jpeg>
Package pdftex.def Info: thumbnails/cas-email.jpeg  used on input line 112.
(pdftex.def)             Requested size: 7.99838pt x 8.0pt.
LaTeX Font Info:    Trying to load font information for T1+zi4 on input line 112.
(/usr/share/texlive/texmf-dist/tex/latex/inconsolata/t1zi4.fd
File: t1zi4.fd 2018/01/14 T1/zi4 (Inconsolata)
)
LaTeX Font Info:    Font shape `T1/zi4/m/n' will be
(Font)              scaled to size 6.80005pt on input line 112.


Package hyperref Warning: Ignoring empty anchor on input line 112.


Package natbib Warning: Citation `Massari2024' on page 1 undefined on input line 172.


Package natbib Warning: Citation `martin2004owl' on page 1 undefined on input line 172.


Package natbib Warning: Citation `hadzic2009ontology' on page 1 undefined on input line 172.


Package natbib Warning: Citation `de2020bdi' on page 1 undefined on input line 174.


Package natbib Warning: Citation `bordini2007java' on page 1 undefined on input line 176.


Package natbib Warning: Citation `bordini2007programming' on page 1 undefined on input line 176.

LaTeX Font Info:    Trying to load font information for T1+cmss on input line 179.
(/usr/share/texlive/texmf-dist/tex/latex/base/t1cmss.fd
File: t1cmss.fd 2023/04/13 v2.5m Standard LaTeX font definitions
) [1

{/usr/share/texlive/texmf-dist/fonts/enc/dvips/inconsolata/i4-t1-0.enc}{/usr/share/texmf/fonts/enc/dvips/cm-super/cm-super-t1.enc} <./thumbnails/cas-email.jpeg>]

Package natbib Warning: Citation `Massari2024' on page 2 undefined on input line 186.


Package natbib Warning: Citation `huhns1997ontologies' on page 2 undefined on input line 186.


Package natbib Warning: Citation `luke1997ontology' on page 2 undefined on input line 186.


Package natbib Warning: Citation `freitas2017applying' on page 2 undefined on input line 186.


Package natbib Warning: Citation `anvari2017multi' on page 2 undefined on input line 186.


Package natbib Warning: Citation `okresaduric2019MAMbO5NewOntology' on page 2 undefined on input line 186.


Package natbib Warning: Citation `de2020bdi' on page 2 undefined on input line 188.


Package natbib Warning: Citation `georgeff1999belief' on page 2 undefined on input line 188.


Package natbib Warning: Citation `van2011goal' on page 2 undefined on input line 190.


Package natbib Warning: Citation `liu2012using' on page 2 undefined on input line 190.


Package natbib Warning: Citation `braubach2005jadex' on page 2 undefined on input line 190.


Package natbib Warning: Citation `martin2004owl' on page 2 undefined on input line 190.


Package natbib Warning: Citation `ribino2013ontology' on page 2 undefined on input line 190.


Package natbib Warning: Citation `boissier2013multi' on page 2 undefined on input line 190.


Underfull \hbox (badness 1325) in paragraph at lines 190--191
\T1/stix/m/n/10 specific knowl-edge (e.g. roles, tasks and en-vi-ron-men-tal
 []


Underfull \hbox (badness 1325) in paragraph at lines 190--191
\T1/stix/m/n/10 key as-pects: re-quire-ments ex-pressed through ob-jec-tives,
 []


Package natbib Warning: Citation `doumanas2024human' on page 2 undefined on input line 192.


Package natbib Warning: Citation `tsaneva2024llm' on page 2 undefined on input line 192.


Package natbib Warning: Citation `ichida2024bdi' on page 2 undefined on input line 192.


Package natbib Warning: Citation `rao1996agentspeak' on page 2 undefined on input line 198.


Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[2]

Package natbib Warning: Citation `bordini2007programming' on page 3 undefined on input line 201.

LaTeX Font Info:    Font shape `T1/zi4/m/n' will be
(Font)              scaled to size 8.50006pt on input line 206.

Package natbib Warning: Citation `bordini2007java' on page 3 undefined on input line 215.

<figuras.pdf, id=43, 818.73953pt x 284.95297pt>
File: figuras.pdf Graphic file (type pdf)
<use figuras.pdf>
Package pdftex.def Info: figuras.pdf  used on input line 243.
(pdftex.def)             Requested size: 494.50888pt x 172.10793pt.

Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[3]
/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex:278: Undefined control sequence.
l.278 Concept classes, e.g. a class \mintinline
                                               {text}{Person} subclass of \m...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex:278: Undefined control sequence.
l.278 ...ine{text}{Person} subclass of \mintinline
                                                  {text}{Thing}, as shown in...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex:278: Undefined control sequence.
l.278 ...the second argument, i.e. the \mintinline
                                                  {text}{wizard} concept as ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/home/<USER>/Documents/Projekti FOI/MAGO/Documents/250400 BOWLDI paper/BOWLDI/samplepaper.tex:278: Undefined control sequence.
l.278 ...pt as being a subclass of the \mintinline
                                                  {text}{person} concept. On...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

Package hyperref Info: bookmark level for unknown lstlisting defaults to 0 on input line 280.
LaTeX Font Info:    Font shape `T1/zi4/m/n' will be
(Font)              scaled to size 5.95004pt on input line 280.
[4 <./figuras.pdf>]
LaTeX Font Info:    Font shape `T1/zi4/m/n' will be
(Font)              scaled to size 7.65005pt on input line 342.

Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[5]
<shared ontology example.pdf, id=150, 598.235pt x 487.8225pt>
File: shared ontology example.pdf Graphic file (type pdf)
<use shared ontology example.pdf>
Package pdftex.def Info: shared ontology example.pdf  used on input line 454.
(pdftex.def)             Requested size: 238.25444pt x 194.28441pt.

Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[6]

Package natbib Warning: Citation `tolkien2007LordRings' on page 7 undefined on input line 471.


Underfull \hbox (badness 1354) in paragraph at lines 473--474
\T1/stix/m/n/10 three in-di-vid-u-als: \T1/zi4/m/n/10 Gandalf the Grey\T1/stix/m/n/10 , \T1/zi4/m/n/10 Aragorn, son of
 []


Underfull \hbox (badness 1616) in paragraph at lines 473--474
\T1/stix/m/n/10 the knowl-edge de-fined within the ex-am-ple on-tol-ogy is
 []

[7 <./shared ontology example.pdf>]

Package natbib Warning: Citation `palanca2023flexible' on page 8 undefined on input line 570.


Underfull \hbox (badness 1242) in paragraph at lines 570--571
[]\T1/stix/m/n/10 All the above ex-am-ples have been de-vel-oped us-ing
 []


Package natbib Warning: Citation `palanca2020spade' on page 8 undefined on input line 572.

[8]

Package natbib Warning: Citation `farquhar1997ontolingua' on page 9 undefined on input line 637.


Package natbib Warning: Citation `fipa000032000fipa' on page 9 undefined on input line 637.


Underfull \hbox (badness 1117) in paragraph at lines 648--649
[]\T1/stix/m/n/10 This work is par-tially sup-ported by Gen-er-al-i-tat Va-
 []

(./samplepaper.bbl
Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[9])

Package natbib Warning: There were undefined citations.


Underfull \hbox (badness 10000) has occurred while \output is active
$[]$
 []

[10

] (./samplepaper.aux

Package natbib Warning: Citation(s) may have changed.
(natbib)                Rerun to get citations correct.

)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
 ***********
Package rerunfilecheck Info: File `samplepaper.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 21805 strings out of 475468
 343614 string characters out of 5775649
 2317975 words of memory out of 5000000
 43249 multiletter control sequences out of 15000+600000
 665996 words of font info for 166 fonts, out of 8000000 for 9000
 417 hyphenation exceptions out of 8191
 90i,12n,93p,2754b,2128s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texlive/texmf-dist/fonts/type1/public/inconsolata/Inconsolata-zi4r.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/STIXGeneral-Bold.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/STIXGeneral-Italic.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/STIXGeneral-Regular.pfb></usr/share/texmf/fonts/type1/public/cm-super/sfss0900.pfb></usr/share/texmf/fonts/type1/public/cm-super/sfsx0900.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/stix-mathit.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/stix-mathrm.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/stix-mathscr.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/stix/stix-mathsf.pfb>
Output written on samplepaper.pdf (11 pages, 343456 bytes).
PDF statistics:
 513 PDF objects out of 1000 (max. 8388607)
 466 compressed objects within 5 object streams
 274 named destinations out of 1000 (max. 500000)
 16 words of extra memory for PDF output out of 10000 (max. 10000000)

