This is BibTeX, Version 0.99d (TeX Live 2023/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: samplepaper.aux
The style file: elsarticle-num.bst
Database file #1: refs.bib
Warning--empty pages in de2020bdi
Warning--can't use both volume and number fields in freitas2017applying
Warning--empty pages in ichida2024bdi
Warning--empty journal in fipa000032000fipa
Warning--empty year in fipa000032000fipa
You've used 26 entries,
            2952 wiz_defined-function locations,
            827 strings with 10605 characters,
and the built_in function-call counts, 8515 in all, are:
= -- 748
> -- 330
< -- 12
+ -- 126
- -- 100
* -- 567
:= -- 1141
add.period$ -- 28
call.type$ -- 26
change.case$ -- 24
chr.to.int$ -- 12
cite$ -- 31
duplicate$ -- 283
empty$ -- 983
format.name$ -- 123
if$ -- 2222
int.to.chr$ -- 0
int.to.str$ -- 26
missing$ -- 30
newline$ -- 88
num.names$ -- 26
pop$ -- 204
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 570
stack$ -- 0
substring$ -- 418
swap$ -- 69
text.length$ -- 8
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 5
while$ -- 62
width$ -- 28
write$ -- 224
(There were 5 warnings)
