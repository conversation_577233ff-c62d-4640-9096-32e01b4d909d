\begin{thebibliography}{10}
\expandafter\ifx\csname url\endcsname\relax
  \def\url#1{\texttt{#1}}\fi
\expandafter\ifx\csname urlprefix\endcsname\relax\def\urlprefix{URL }\fi
\expandafter\ifx\csname href\endcsname\relax
  \def\href#1#2{#2} \def\path#1{#1}\fi

\bibitem{Massari2024}
H.~E. <PERSON>, N.~<PERSON>, F.~<PERSON>, S.~<PERSON>medi, M.~Am<PERSON>, The role of artificial intelligence in the semantic web, in: 2024 10th International Conference on Optimization and Applications (ICOA), 2024, pp. 1--6.
\newblock \href {https://doi.org/10.1109/ICOA62581.2024.10753971} {\path{doi:10.1109/ICOA62581.2024.10753971}}.

\bibitem{martin2004owl}
D<PERSON>~<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, S<PERSON>, S<PERSON>~<PERSON>, <PERSON><PERSON>, B.~<PERSON>, T<PERSON><PERSON>, et~al., Owl-s: Semantic markup for web services, W3C member submission 22~(4) (2004).

\bibitem{hadzic2009ontology}
M.~Hadzic, E.~Chang, P.~Wongthongtham, T.~Dillon, Ontology-based multi-agent systems, Springer, 2009.

\bibitem{de2020bdi}
L.~De~<PERSON>, F.~R. Meneguzzi, B.~Logan, Bdi agent architectures: A survey, in: Proceedings of the 29th International Joint Conference on Artificial Intelligence (IJCAI), 2020, Jap{\~a}o., 2020.

\bibitem{bordini2007java}
R.~H. Bordini, J.~F. H{\"u}bner, A java-based interpreter for an extended version of agentspeak, University of Durham, Universidade Regional de Blumenau 256 (2007).

\bibitem{bordini2007programming}
R.~H. Bordini, J.~F. H{\"u}bner, M.~Wooldridge, Programming multi-agent systems in AgentSpeak using Jason, Vol.~8, John Wiley \& Sons, 2007.

\bibitem{huhns1997ontologies}
M.~N. Huhns, M.~P. Singh, Ontologies for agents, IEEE Internet computing 1~(6) (1997) 81--83.

\bibitem{luke1997ontology}
S.~Luke, L.~Spector, D.~Rager, J.~Hendler, Ontology-based web agents, in: Proceedings of the first international conference on Autonomous agents, 1997, pp. 59--66.

\bibitem{freitas2017applying}
A.~Freitas, A.~R. Panisson, L.~Hilgert, F.~Meneguzzi, R.~Vieira, R.~H. Bordini, Applying ontologies to the development and execution of multi-agent systems, in: Web Intelligence, Vol.~15, SAGE Publications Sage UK: London, England, 2017, pp. 291--302.

\bibitem{anvari2017multi}
A.~Anvari-Moghaddam, A.~Rahimi-Kian, M.~S. Mirian, J.~M. Guerrero, A multi-agent based energy management solution for integrated buildings and microgrid system, Applied energy 203 (2017) 41--56.

\bibitem{okresaduric2019MAMbO5NewOntology}
B.~Okre{\v s}a~{\DH}uri{\'c}, J.~Rincon, C.~Carrascosa, M.~Schatten, V.~Julian, {{MAMbO5}}: {{A}} new {{Ontology Approach}} for {{Modelling}} and {{Managing Intelligent Virtual Environments Based}} on {{Multi-Agent Systems}}, Journal of Ambient Intelligence and Humanized Computing 10~(9) (2019) 3629--3641.
\newblock \href {https://doi.org/10.1007/s12652-018-1089-4} {\path{doi:10.1007/s12652-018-1089-4}}.

\bibitem{georgeff1999belief}
M.~Georgeff, B.~Pell, M.~Pollack, M.~Tambe, M.~Wooldridge, The belief-desire-intention model of agency, in: Intelligent Agents V: Agents Theories, Architectures, and Languages: 5th International Workshop, ATAL’98 Paris, France, July 4--7, 1998 Proceedings 5, Springer, 1999, pp. 1--10.

\bibitem{van2011goal}
J.~Van~Oijen, W.~Van~Doesburg, F.~Dignum, Goal-based communication using bdi agents as virtual humans in training: An ontology driven dialogue system, Springer, 2011.

\bibitem{liu2012using}
C.-H. Liu, J.~J.-Y. Chen, Using ontology-based bdi agent to dynamically customize workflow and bind semantic web service, J. Softw. 7~(4) (2012) 884--894.

\bibitem{braubach2005jadex}
L.~Braubach, A.~Pokahr, W.~Lamersdorf, Jadex: A bdi-agent system combining middleware and reasoning, in: Software agent-based applications, platforms and development kits, Springer, 2005, pp. 143--168.

\bibitem{ribino2013ontology}
P.~Ribino, M.~Cossentino, C.~Lodato, S.~Lopes, L.~Sabatucci, V.~Seidita, et~al., Ontology and goal model in designing bdi multi-agent systems, WOA@ AI* IA 1099 (2013) 66--72.

\bibitem{boissier2013multi}
O.~Boissier, R.~H. Bordini, J.~F. H{\"u}bner, A.~Ricci, A.~Santi, Multi-agent oriented programming with jacamo, Science of Computer Programming 78~(6) (2013) 747--761.

\bibitem{doumanas2024human}
D.~Doumanas, G.~Bouchouras, A.~Soularidis, K.~Kotis, G.~Vouros, From human-to llm-centered collaborative ontology engineering, Applied Ontology (2024) 15705838241305067.

\bibitem{tsaneva2024llm}
S.~Tsaneva, S.~Vasic, M.~Sabou, Llm-driven ontology evaluation: Verifying ontology restrictions with chatgpt, The Semantic Web: ESWC Satellite Events 2024 (2024).

\bibitem{ichida2024bdi}
A.~Y. Ichida, F.~Meneguzzi, R.~C. Cardoso, Bdi agents in natural language environments, in: Proceedings of the 23rd International Conference on Autonomous Agents and Multiagent Systems, International Foundation for Autonomous Agents and Multiagent Systems, 2024.

\bibitem{rao1996agentspeak}
A.~S. Rao, Agentspeak (l): Bdi agents speak out in a logical computable language, in: European workshop on modelling autonomous agents in a multi-agent world, Springer, 1996, pp. 42--55.

\bibitem{tolkien2007LordRings}
J.~R.~R. Tolkien, The {{Lord}} of the {{Rings}}, 50th Edition, HarperCollinsPublishers, London, 2007.

\bibitem{palanca2023flexible}
J.~Palanca, J.~A. Rincon, C.~Carrascosa, V.~J. Julian, A.~Terrasa, Flexible agent architecture: mixing reactive and deliberative behaviors in spade, Electronics 12~(3) (2023) 659.

\bibitem{palanca2020spade}
J.~Palanca, A.~Terrasa, V.~Julian, C.~Carrascosa, Spade 3: Supporting the new generation of multi-agent systems, IEEE Access 8 (2020) 182537--182549.

\bibitem{farquhar1997ontolingua}
A.~Farquhar, R.~Fikes, J.~Rice, The ontolingua server: A tool for collaborative ontology construction, International journal of human-computer studies 46~(6) (1997) 707--727.

\bibitem{fipa000032000fipa}
S.~FIPA00003, Fipa sl content language specification.

\end{thebibliography}
