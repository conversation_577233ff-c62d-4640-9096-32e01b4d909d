Ontologies provide a formal and structured way to represent knowledge, defining concepts and their relationships within a domain. They enable interoperability by creating a shared vocabulary that can be understood across different systems. In multi-agent systems, ontologies can be particularly valuable for improving knowledge sharing and coordination, especially in heterogeneous environments where agents may have different representations of the environment. This paper introduces BOWLDI, a framework that enables the integration of OWL ontologies into BDI agents. BOWLDI acts as an intermediary layer between the agent's deliberative engine and an OWL-based knowledge base, allowing agents to dynamically query, reason about, and incorporate semantic information into their beliefs. The framework includes a mapping mechanism that translates OWL concepts into beliefs compatible with the agent's logic without altering the core reasoning structure. This integration provides a richer representation of the environment, enhancing the agent's ability to make informed decisions. Additionally, BOWLDI supports differentiated knowledge management by distinguishing between public knowledge, which is shared with other agents, and private knowledge, which is retained for internal reasoning. This selective sharing mechanism enables agents to control the dissemination of information based on relevance or sensitivity, improving overall system flexibility and security.
