@article{fipa000032000fipa,
  title={FIPA SL Content Language Specification},
  author={FIPA00003, Supersedes}
}

@article{farquhar1997ontolingua,
  title={The ontolingua server: A tool for collaborative ontology construction},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={International journal of human-computer studies},
  volume={46},
  number={6},
  pages={707--727},
  year={1997},
  publisher={Elsevier}
}

@article{palanca2023flexible,
  title={Flexible agent architecture: mixing reactive and deliberative behaviors in SPADE},
  author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, Andr{\'e}s},
  journal={Electronics},
  volume={12},
  number={3},
  pages={659},
  year={2023},
  publisher={MDPI}
}

@article{martin2004owl,
  title={OWL-S: Semantic markup for web services},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and others},
  journal={W3C member submission},
  volume={22},
  number={4},
  year={2004}
}

@article{palanca2020spade,
  title={Spade 3: Supporting the new generation of multi-agent systems},
  author={Palanca, Javier and Terrasa, Andr{\'e}s and Julian, Vicente and Carrascosa, Carlos},
  journal={IEEE Access},
  volume={8},
  pages={182537--182549},
  year={2020},
  publisher={IEEE}
}

@inproceedings{ichida2024bdi,
  title={BDI Agents in Natural Language Environments},
  author={Ichida, Alexandre Yukio and Meneguzzi, Felipe and Cardoso, Rafael C},
  booktitle={Proceedings of the 23rd International Conference on Autonomous Agents and Multiagent Systems},
  year={2024},
  organization={International Foundation for Autonomous Agents and Multiagent Systems}
}

@book{van2011goal,
  title={Goal-based communication using bdi agents as virtual humans in training: An ontology driven dialogue system},
  author={Van Oijen, Joost and Van Doesburg, Willem and Dignum, Frank},
  year={2011},
  publisher={Springer}
}

@article{liu2012using,
  title={Using Ontology-based BDI Agent to Dynamically Customize Workflow and Bind Semantic Web Service},
  author={Liu, Chih-Hao and Chen, Jason Jen-Yen},
  journal={J. Softw.},
  volume={7},
  number={4},
  pages={884--894},
  year={2012},
  publisher={Citeseer}
}

@book{bordini2007programming,
  title={Programming multi-agent systems in AgentSpeak using Jason},
  author={Bordini, Rafael H and H{\"u}bner, Jomi Fred and Wooldridge, Michael},
  volume={8},
  year={2007},
  publisher={John Wiley \& Sons}
}

@article{bordini2007java,
  title={A Java-based interpreter for an extended version of AgentSpeak},
  author={Bordini, Rafael H and H{\"u}bner, Jomi F},
  journal={University of Durham, Universidade Regional de Blumenau},
  volume={256},
  year={2007}
}

@inproceedings{rao1996agentspeak,
  title={AgentSpeak (L): BDI agents speak out in a logical computable language},
  author={Rao, Anand S},
  booktitle={European workshop on modelling autonomous agents in a multi-agent world},
  pages={42--55},
  year={1996},
  organization={Springer}
}

@article{tsaneva2024llm,
  title={Llm-driven ontology evaluation: Verifying ontology restrictions with chatgpt},
  author={Tsaneva, Stefani and Vasic, Stefan and Sabou, Marta},
  journal={The Semantic Web: ESWC Satellite Events},
  volume={2024},
  year={2024}
}

@article{doumanas2024human,
  title={From Human-to LLM-Centered Collaborative Ontology Engineering},
  author={Doumanas, Dimitrios and Bouchouras, Georgios and Soularidis, Andreas and Kotis, Konstantinos and Vouros, George},
  journal={Applied Ontology},
  pages={15705838241305067},
  year={2024},
  publisher={SAGE Publications Sage UK: London, England}
}

@article{okresaduric2019MAMbO5NewOntology,
  title = {{{MAMbO5}}: {{A}} New {{Ontology Approach}} for {{Modelling}} and {{Managing Intelligent Virtual Environments Based}} on {{Multi-Agent Systems}}},
  shorttitle = {{{MAMbO5}}},
  author = {Okre{\v s}a {\DH}uri{\'c}, Bogdan and Rincon, Jaime and Carrascosa, Carlos and Schatten, Markus and Julian, Vicente},
  year = {2019},
  journal = {Journal of Ambient Intelligence and Humanized Computing},
  volume = {10},
  number = {9},
  pages = {3629--3641},
  issn = {1868-5145},
  doi = {10.1007/s12652-018-1089-4},
  urldate = {2018-10-14},
  abstract = {An intelligent virtual environment simulates a physical world inhabited by autonomous intelligent entities. Multi-agent systems have been usually employed to design systems of this kind. One of the key aspects in the design of intelligent virtual environments is the use of appropriate ontologies which offer a richer and more expressive representation of knowledge. In this sense, this paper proposes an ontology comprising concepts for modelling intelligent virtual environments enhanced with concepts for describing agent-based organisational features. This new ontology, called MAMbO5, is used as an input of the JaCalIVE framework, which is a toolkit for the design and implementation of agent-based intelligent virtual environments.},
  copyright = {All rights reserved},
  langid = {english},
  keywords = {suradnja},
  annotation = {0 citations (Semantic Scholar/DOI) [2023-08-15]\\
6 citations (Crossref) [2023-08-15]},
}

@inproceedings{georgeff1999belief,
  title={The belief-desire-intention model of agency},
  author={Georgeff, Michael and Pell, Barney and Pollack, Martha and Tambe, Milind and Wooldridge, Michael},
  booktitle={Intelligent Agents V: Agents Theories, Architectures, and Languages: 5th International Workshop, ATAL’98 Paris, France, July 4--7, 1998 Proceedings 5},
  pages={1--10},
  year={1999},
  organization={Springer}
}

@inproceedings{de2020bdi,
  title={BDI agent architectures: A survey},
  author={De Silva, Lavindra and Meneguzzi, Felipe Rech and Logan, Brian},
  booktitle={Proceedings of the 29th International Joint Conference on Artificial Intelligence (IJCAI), 2020, Jap{\~a}o.},
  year={2020}
}

@article{anvari2017multi,
  title={A multi-agent based energy management solution for integrated buildings and microgrid system},
  author={Anvari-Moghaddam, Amjad and Rahimi-Kian, Ashkan and Mirian, Maryam S and Guerrero, Josep M},
  journal={Applied energy},
  volume={203},
  pages={41--56},
  year={2017},
  publisher={Elsevier}
}

@inproceedings{freitas2017applying,
  title={Applying ontologies to the development and execution of multi-agent systems},
  author={Freitas, Artur and Panisson, Alison R and Hilgert, Lucas and Meneguzzi, Felipe and Vieira, Renata and Bordini, Rafael H},
  booktitle={Web Intelligence},
  volume={15},
  number={4},
  pages={291--302},
  year={2017},
  organization={SAGE Publications Sage UK: London, England}
}

@inproceedings{luke1997ontology,
  title={Ontology-based web agents},
  author={Luke, Sean and Spector, Lee and Rager, David and Hendler, James},
  booktitle={Proceedings of the first international conference on Autonomous agents},
  pages={59--66},
  year={1997}
}

@article{huhns1997ontologies,
  title={Ontologies for agents},
  author={Huhns, Michael N. and Singh, Munindar P.},
  journal={IEEE Internet computing},
  volume={1},
  number={6},
  pages={81--83},
  year={1997},
  publisher={IEEE}
}

@book{hadzic2009ontology,
  title={Ontology-based multi-agent systems},
  author={Hadzic, Maja and Chang, Elizabeth and Wongthongtham, Pornpit and Dillon, Tharam},
  year={2009},
  publisher={Springer}
}

@INPROCEEDINGS{Massari2024,
  author={Massari, Hakim El and Gherabi, Noreddine and Qanouni, Fatima and Mhammedi, Sajida and Amnai, Mohamed},
  booktitle={2024 10th International Conference on Optimization and Applications (ICOA)}, 
  title={The Role of Artificial Intelligence in the Semantic Web}, 
  year={2024},
  volume={},
  number={},
  pages={1-6},
  keywords={Semantic Web;Knowledge engineering;Machine learning algorithms;Reviews;Merging;Machine learning;Learning (artificial intelligence);Natural language processing;Artificial intelligence;Optimization;Artificial Intelligence;Semantic Web;Machine Learning;Ontology},
  doi={10.1109/ICOA62581.2024.10753971}}

@inproceedings{braubach2005jadex,
  title={Jadex: A BDI-agent system combining middleware and reasoning},
  author={Braubach, Lars and Pokahr, Alexander and Lamersdorf, Winfried},
  booktitle={Software agent-based applications, platforms and development kits},
  pages={143--168},
  year={2005},
  organization={Springer}
}

@article{boissier2013multi,
  title={Multi-agent oriented programming with JaCaMo},
  author={Boissier, Olivier and Bordini, Rafael H and H{\"u}bner, Jomi F and Ricci, Alessandro and Santi, Andrea},
  journal={Science of Computer Programming},
  volume={78},
  number={6},
  pages={747--761},
  year={2013},
  publisher={Elsevier}
}

@article{ribino2013ontology,
  title={Ontology and Goal Model in Designing BDI Multi-Agent Systems},
  author={Ribino, Patrizia and Cossentino, Massimo and Lodato, Carmelo and Lopes, Salvatore and Sabatucci, Luca and Seidita, Valeria and others},
  journal={WOA@ AI* IA},
  volume={1099},
  pages={66--72},
  year={2013},
  publisher={Citeseer}
}

@book{tolkien2007LordRings,
  title = {The {{Lord}} of the {{Rings}}},
  author = {Tolkien, John Ronald Reuel},
  year = {2007},
  edition = {50th Anniversary},
  publisher = {HarperCollinsPublishers},
  address = {London},
  isbn = {978-0-261-10325-2},
  langid = {english},
  annotation = {OCLC: 777941114}
}
