\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\orcidauthor{0000-0001-5653-6646}{Bogdan {Okre\v {s}a \DJ uri\'{c}}}
\orcidauthor{0000-0002-5163-5335}{<PERSON>}
\emailauthor{<EMAIL>}{<PERSON>}
\csgdef{mark@corau2}{1}
\orcidauthor{0000-0003-3649-6530}{<PERSON>}
\NewLabel{cor1}{1}
\citation{Massari2024}
\citation{martin2004owl}
\citation{hadzic2009ontology}
\citation{de2020bdi}
\citation{bordini2007java}
\citation{bordini2007programming}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1}{section.1}\protected@file@percent }
\newlabel{sec: Introduction}{{1}{1}{Introduction}{section.1}{}}
\newlabel{sec: Introduction@cref}{{[section][1][]1}{[1][1][]1}}
\citation{Massari2024}
\citation{huhns1997ontologies,luke1997ontology}
\citation{freitas2017applying,anvari2017multi}
\citation{okresaduric2019MAMbO5NewOntology}
\citation{de2020bdi,georgeff1999belief}
\citation{van2011goal}
\citation{liu2012using}
\citation{braubach2005jadex}
\citation{martin2004owl}
\citation{ribino2013ontology}
\citation{boissier2013multi}
\citation{doumanas2024human,tsaneva2024llm}
\citation{ichida2024bdi}
\citation{rao1996agentspeak}
\@writefile{toc}{\contentsline {section}{\numberline {2}State of the Art}{2}{section.2}\protected@file@percent }
\newlabel{sec:soa}{{2}{2}{State of the Art}{section.2}{}}
\newlabel{sec:soa@cref}{{[section][2][]2}{[1][2][]2}}
\@writefile{toc}{\contentsline {section}{\numberline {3}AgentSpeak Python Library}{2}{section.3}\protected@file@percent }
\newlabel{sec:agentspeak}{{3}{2}{AgentSpeak Python Library}{section.3}{}}
\newlabel{sec:agentspeak@cref}{{[section][3][]3}{[1][2][]2}}
\citation{bordini2007programming}
\citation{bordini2007java}
\@writefile{toc}{\contentsline {section}{\numberline {4}Integrating OWL as Believes language}{3}{section.4}\protected@file@percent }
\newlabel{sec: Integrating OWL as Believes language}{{4}{3}{Integrating OWL as Believes language}{section.4}{}}
\newlabel{sec: Integrating OWL as Believes language@cref}{{[section][4][]4}{[1][3][]3}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}OWL as source and target for Believes}{3}{subsection.4.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces Common and private knowledge in the agents}}{4}{figure.1}\protected@file@percent }
\newlabel{fig: knowledge}{{1}{4}{Common and private knowledge in the agents}{figure.1}{}}
\newlabel{fig: knowledge@cref}{{[figure][1][]1}{[1][3][]4}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}OWL as the language of representation of Believes}{4}{subsection.4.2}\protected@file@percent }
\newlabel{lst: init example turtle}{{1}{4}{Examples of: the \texttt {Person} concept class, \texttt {Wizard} subclass of class \texttt {Person}, and two individuals of class \texttt {Wizard} expressed in turtle syntax}{lstlisting.1}{}}
\newlabel{lst: init example turtle@cref}{{[lstlisting][1][]1}{[1][4][]4}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {1}Examples of: the \texttt  {Person} concept class, \texttt  {Wizard} subclass of class \texttt  {Person}, and two individuals of class \texttt  {Wizard} expressed in turtle syntax}{4}{lstlisting.1}\protected@file@percent }
\newlabel{lst: init example agentspeak}{{2}{5}{Examples of: the \texttt {Person} concept class, \texttt {Wizard} subclass of class \texttt {Person}, and two individuals of class \texttt {Wizard} expressed in AgentSpeak}{lstlisting.2}{}}
\newlabel{lst: init example agentspeak@cref}{{[lstlisting][2][]2}{[1][5][]5}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {2}Examples of: the \texttt  {Person} concept class, \texttt  {Wizard} subclass of class \texttt  {Person}, and two individuals of class \texttt  {Wizard} expressed in AgentSpeak}{5}{lstlisting.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces Mapping of the key ontology concepts to AgentSpeak predicates of various arity}}{5}{table.1}\protected@file@percent }
\newlabel{tab: ontology agentspeak mapping}{{1}{5}{Mapping of the key ontology concepts to AgentSpeak predicates of various arity}{table.1}{}}
\newlabel{tab: ontology agentspeak mapping@cref}{{[table][1][]1}{[1][5][]5}}
\@writefile{toc}{\contentsline {section}{\numberline {5}Private Knowledge vs. Public Knowledge}{5}{section.5}\protected@file@percent }
\newlabel{sec: private public knowledge}{{5}{5}{Private Knowledge vs. Public Knowledge}{section.5}{}}
\newlabel{sec: private public knowledge@cref}{{[section][5][]5}{[1][5][]5}}
\@writefile{toc}{\contentsline {section}{\numberline {6}Example}{6}{section.6}\protected@file@percent }
\newlabel{sec: Example}{{6}{6}{Example}{section.6}{}}
\newlabel{sec: Example@cref}{{[section][6][]6}{[1][6][]6}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}Converting AgentSpeak Expressions to OWL}{6}{subsection.6.1}\protected@file@percent }
\newlabel{sec: example asl - owl w/o sources}{{6.1}{6}{Converting AgentSpeak Expressions to OWL}{subsection.6.1}{}}
\newlabel{sec: example asl - owl w/o sources@cref}{{[subsection][1][6]6.1}{[1][6][]6}}
\newlabel{lst: example one agentspeak}{{3}{6}{A string comprising a set of AgentSpeak expressions is provided as input to the implemented converter}{lstlisting.3}{}}
\newlabel{lst: example one agentspeak@cref}{{[lstlisting][3][]3}{[1][6][]6}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {3}A string comprising a set of AgentSpeak expressions is provided as input to the implemented converter}{6}{lstlisting.3}\protected@file@percent }
\newlabel{lst: example one input file}{{4}{6}{A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}{lstlisting.4}{}}
\newlabel{lst: example one input file@cref}{{[lstlisting][4][]4}{[1][6][]6}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {4}A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}{6}{lstlisting.4}\protected@file@percent }
\newlabel{lst: example one output file}{{5}{6}{A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}{lstlisting.5}{}}
\newlabel{lst: example one output file@cref}{{[lstlisting][5][]5}{[1][6][]6}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {5}A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}{6}{lstlisting.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}Converting OWL to AgentSpeak Expressions}{6}{subsection.6.2}\protected@file@percent }
\newlabel{sec: example owl - asl w/o sources}{{6.2}{6}{Converting OWL to AgentSpeak Expressions}{subsection.6.2}{}}
\newlabel{sec: example owl - asl w/o sources@cref}{{[subsection][2][6]6.2}{[1][6][]6}}
\newlabel{lst: example two input file}{{6}{6}{A file path containing the ontology to be converted is provided as input to the implemented converter}{lstlisting.6}{}}
\newlabel{lst: example two input file@cref}{{[lstlisting][6][]6}{[1][6][]6}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {6}A file path containing the ontology to be converted is provided as input to the implemented converter}{6}{lstlisting.6}\protected@file@percent }
\citation{tolkien2007LordRings}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Visual representation of the concepts of the example shared ontology}}{7}{figure.2}\protected@file@percent }
\newlabel{fig: shared ontology example}{{2}{7}{Visual representation of the concepts of the example shared ontology}{figure.2}{}}
\newlabel{fig: shared ontology example@cref}{{[figure][2][]2}{[1][6][]7}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}Converting OWL with External Sources to AgentSpeak}{7}{subsection.6.3}\protected@file@percent }
\newlabel{sec: example owl - asl w/ sources}{{6.3}{7}{Converting OWL with External Sources to AgentSpeak}{subsection.6.3}{}}
\newlabel{sec: example owl - asl w/ sources@cref}{{[subsection][3][6]6.3}{[1][7][]7}}
\newlabel{lst: example three}{{7}{7}{A file path containing the ontology to be converted is provided as input to the implemented converter, and the output file path is specified}{lstlisting.7}{}}
\newlabel{lst: example three@cref}{{[lstlisting][7][]7}{[1][7][]7}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {7}A file path containing the ontology to be converted is provided as input to the implemented converter, and the output file path is specified}{7}{lstlisting.7}\protected@file@percent }
\newlabel{lst: example three input}{{8}{7}{Elements of the input ontology in the example presented in Sec. \ref {sec: example owl - asl w/ sources}; \texttt {<ontology IRI>}, \texttt {<aragorn IRI>}, and \texttt {<gandalf IRI>} are placeholders for the full values of ontology, Aragorn individual and Gandalf individual URIs, respectively, used here for improved legibility}{lstlisting.8}{}}
\newlabel{lst: example three input@cref}{{[lstlisting][8][]8}{[1][7][]7}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {8}Elements of the input ontology in the example presented in Sec. \ref {sec: example owl - asl w/ sources}; \texttt  {<ontology IRI>}, \texttt  {<aragorn IRI>}, and \texttt  {<gandalf IRI>} are placeholders for the full values of ontology, Aragorn individual and Gandalf individual URIs, respectively, used here for improved legibility}{7}{lstlisting.8}\protected@file@percent }
\citation{palanca2023flexible}
\citation{palanca2020spade}
\newlabel{lst: example three output}{{9}{8}{The output set of AgentSpeak expressions based on the input ontology in the example presented in Sec. \ref {sec: example owl - asl w/ sources}; \texttt {<ontology IRI>} is a placeholder for the full IRI of the subject ontology, used here for improved legibility}{lstlisting.9}{}}
\newlabel{lst: example three output@cref}{{[lstlisting][9][]9}{[1][8][]8}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {9}The output set of AgentSpeak expressions based on the input ontology in the example presented in Sec. \ref {sec: example owl - asl w/ sources}; \texttt  {<ontology IRI>} is a placeholder for the full IRI of the subject ontology, used here for improved legibility}{8}{lstlisting.9}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.4}Converting AgentSpeak with External Sources to OWL}{8}{subsection.6.4}\protected@file@percent }
\newlabel{sec: example asl - owl w/ sources}{{6.4}{8}{Converting AgentSpeak with External Sources to OWL}{subsection.6.4}{}}
\newlabel{sec: example asl - owl w/ sources@cref}{{[subsection][4][6]6.4}{[1][8][]8}}
\newlabel{lst: example four}{{10}{8}{A file path containing the AgentSpeak expressions to be converted is provided as input to the implemented converter, and the output file path is specified}{lstlisting.10}{}}
\newlabel{lst: example four@cref}{{[lstlisting][10][]10}{[1][8][]8}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {10}A file path containing the AgentSpeak expressions to be converted is provided as input to the implemented converter, and the output file path is specified}{8}{lstlisting.10}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.5}SPADE\_BDI Example}{8}{subsection.6.5}\protected@file@percent }
\newlabel{sec: example bdi agent}{{6.5}{8}{SPADE\_BDI Example}{subsection.6.5}{}}
\newlabel{sec: example bdi agent@cref}{{[subsection][5][6]6.5}{[1][8][]8}}
\citation{farquhar1997ontolingua}
\citation{fipa000032000fipa}
\bibstyle{elsarticle-num}
\bibdata{refs}
\bibcite{Massari2024}{{1}{}{{}}{{}}}
\bibcite{martin2004owl}{{2}{}{{}}{{}}}
\bibcite{hadzic2009ontology}{{3}{}{{}}{{}}}
\bibcite{de2020bdi}{{4}{}{{}}{{}}}
\bibcite{bordini2007java}{{5}{}{{}}{{}}}
\bibcite{bordini2007programming}{{6}{}{{}}{{}}}
\bibcite{huhns1997ontologies}{{7}{}{{}}{{}}}
\bibcite{luke1997ontology}{{8}{}{{}}{{}}}
\newlabel{lst: example five ontology to beliefs}{{11}{9}{Implemented SPADE\_BDI agent that uses \texttt {BOWLDIConverter} to convert information contained in an ontology file to a set of beliefs}{lstlisting.11}{}}
\newlabel{lst: example five ontology to beliefs@cref}{{[lstlisting][11][]11}{[1][8][]9}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {11}Implemented SPADE\_BDI agent that uses \texttt  {BOWLDIConverter} to convert information contained in an ontology file to a set of beliefs}{9}{lstlisting.11}\protected@file@percent }
\newlabel{lst: example five beliefs to ontology}{{12}{9}{Implemented SPADE\_BDI agent that uses \texttt {BOWLDIConverter} to convert a set of beliefs to an ontology file}{lstlisting.12}{}}
\newlabel{lst: example five beliefs to ontology@cref}{{[lstlisting][12][]12}{[1][9][]9}}
\@writefile{lol}{\contentsline {lstlisting}{\numberline {12}Implemented SPADE\_BDI agent that uses \texttt  {BOWLDIConverter} to convert a set of beliefs to an ontology file}{9}{lstlisting.12}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7}Conclusion}{9}{section.7}\protected@file@percent }
\newlabel{sec:conclusion}{{7}{9}{Conclusion}{section.7}{}}
\newlabel{sec:conclusion@cref}{{[section][7][]7}{[1][9][]9}}
\bibcite{freitas2017applying}{{9}{}{{}}{{}}}
\bibcite{anvari2017multi}{{10}{}{{}}{{}}}
\bibcite{okresaduric2019MAMbO5NewOntology}{{11}{}{{}}{{}}}
\bibcite{georgeff1999belief}{{12}{}{{}}{{}}}
\bibcite{van2011goal}{{13}{}{{}}{{}}}
\bibcite{liu2012using}{{14}{}{{}}{{}}}
\bibcite{braubach2005jadex}{{15}{}{{}}{{}}}
\bibcite{ribino2013ontology}{{16}{}{{}}{{}}}
\bibcite{boissier2013multi}{{17}{}{{}}{{}}}
\bibcite{doumanas2024human}{{18}{}{{}}{{}}}
\bibcite{tsaneva2024llm}{{19}{}{{}}{{}}}
\bibcite{ichida2024bdi}{{20}{}{{}}{{}}}
\bibcite{rao1996agentspeak}{{21}{}{{}}{{}}}
\bibcite{tolkien2007LordRings}{{22}{}{{}}{{}}}
\bibcite{palanca2023flexible}{{23}{}{{}}{{}}}
\bibcite{palanca2020spade}{{24}{}{{}}{{}}}
\bibcite{farquhar1997ontolingua}{{25}{}{{}}{{}}}
\bibcite{fipa000032000fipa}{{26}{}{{}}{{}}}
\csxdef{lastpage}{10}
\providecommand\NAT@force@numbers{}\NAT@force@numbers
\gdef \@abspage@last{11}
