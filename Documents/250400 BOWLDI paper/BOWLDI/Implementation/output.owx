<?xml version="1.0"?>
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:owl="http://www.w3.org/2002/07/owl#"
         xml:base="output.owl"
         xmlns="output.owl#">

<owl:Ontology rdf:about="output.owl"/>

<owl:ObjectProperty rdf:about="#has_context">
  <rdfs:domain rdf:resource="#Plan"/>
  <rdfs:range rdf:resource="#Belief"/>
  <rdfs:label xml:lang="en-gb">has_context</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_triggering_the_plan">
  <rdfs:domain rdf:resource="#Event"/>
  <rdfs:range rdf:resource="#Plan"/>
  <rdfs:label xml:lang="en-gb">is_triggering_the_plan</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_part_of_body_of_plan">
  <rdfs:domain rdf:resource="#Belief"/>
  <rdfs:range rdf:resource="#Plan"/>
  <rdfs:label xml:lang="en-gb">is_part_of_body_of_plan</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#has_body">
  <rdfs:domain rdf:resource="#Plan"/>
  <rdfs:range rdf:resource="#Belief"/>
  <rdfs:label xml:lang="en-gb">has_body</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_owner_of">
  <rdfs:domain rdf:resource="#Person"/>
  <rdfs:range rdf:resource="#Pet"/>
  <rdfs:label xml:lang="en-gb">is_owner_of</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_context_of">
  <rdfs:domain rdf:resource="#Belief"/>
  <rdfs:range rdf:resource="#Plan"/>
  <rdfs:label xml:lang="en-gb">is_context_of</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_owned_by">
  <rdfs:domain rdf:resource="#Pet"/>
  <rdfs:range rdf:resource="#Person"/>
  <rdfs:label xml:lang="en-gb">is_owned_by</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">None</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#meant_to_handle_event">
  <rdfs:domain rdf:resource="#Plan"/>
  <rdfs:range rdf:resource="#Event"/>
  <rdfs:label xml:lang="en-gb">meant_to_handle_event</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_applied_to_literal">
  <rdfs:domain rdf:resource="#Operator"/>
  <rdfs:range rdf:resource="#Literal"/>
  <rdfs:label xml:lang="en-gb">is_applied_to_literal</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:ObjectProperty>

<owl:DatatypeProperty rdf:about="#has_value">
  <rdfs:domain rdf:resource="#Belief"/>
  <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
  <rdfs:label xml:lang="en-gb">has_value</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:DatatypeProperty>

<owl:Class rdf:about="#Pet">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Pet</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Literal">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Literal</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Operator">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Operator</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Not">
  <rdfs:subClassOf rdf:resource="#Operator"/>
  <rdfs:label xml:lang="en-gb">Not</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Cat">
  <rdfs:subClassOf rdf:resource="#Pet"/>
  <rdfs:label xml:lang="en-gb">Cat</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">Bob</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Belief">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Belief</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#And">
  <rdfs:subClassOf rdf:resource="#Operator"/>
  <rdfs:label xml:lang="en-gb">And</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Or">
  <rdfs:subClassOf rdf:resource="#Operator"/>
  <rdfs:label xml:lang="en-gb">Or</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Plan">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Plan</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Event">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Event</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Person">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Person</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Dog">
  <rdfs:subClassOf rdf:resource="#Pet"/>
  <rdfs:label xml:lang="en-gb">Dog</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:Class rdf:about="#Goal">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">Goal</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Class>

<owl:NamedIndividual rdf:about="#Green_Patch_Plan">
  <rdf:type rdf:resource="#Plan"/>
  <rdfs:label xml:lang="en-gb">Green_Patch_Plan</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:NamedIndividual>

<owl:NamedIndividual rdf:about="#Dusty">
  <rdf:type rdf:resource="#Dog"/>
  <is_owned_by rdf:resource="#Alice"/>
  <rdfs:label xml:lang="en-gb">Dusty</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:NamedIndividual>

<owl:NamedIndividual rdf:about="#Alice">
  <rdf:type rdf:resource="#Person"/>
  <is_owner_of rdf:resource="#Dusty"/>
  <rdfs:label xml:lang="en-gb">Alice</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
  <has_value rdf:datatype="http://www.w3.org/2001/XMLSchema#string">jedan</has_value>
</owl:NamedIndividual>

<owl:NamedIndividual rdf:about="#Charles">
  <rdf:type rdf:resource="#Person"/>
  <rdfs:label xml:lang="en-gb">Charles</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:NamedIndividual>

<owl:NamedIndividual rdf:about="#Rex">
  <rdf:type rdf:resource="#Dog"/>
  <rdfs:label xml:lang="en-gb">Rex</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:NamedIndividual>

<owl:NamedIndividual rdf:about="#Bob">
  <rdf:type rdf:resource="#Person"/>
  <rdfs:label xml:lang="en-gb">Bob</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:NamedIndividual>

<owl:Axiom>
  <owl:annotatedSource rdf:resource="#Alice"/>
  <owl:annotatedProperty rdf:resource="#has_value"/>
  <owl:annotatedTarget rdf:datatype="http://www.w3.org/2001/XMLSchema#string">jedan</owl:annotatedTarget>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">None</rdfs:isDefinedBy>
</owl:Axiom>

<owl:Axiom>
  <owl:annotatedSource rdf:resource="#Alice"/>
  <owl:annotatedProperty rdf:resource="#is_owner_of"/>
  <owl:annotatedTarget rdf:resource="#Dusty"/>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">Bob</rdfs:isDefinedBy>
</owl:Axiom>

<owl:Axiom>
  <owl:annotatedSource rdf:resource="#Dusty"/>
  <owl:annotatedProperty rdf:resource="#is_owned_by"/>
  <owl:annotatedTarget rdf:resource="#Alice"/>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self</rdfs:isDefinedBy>
</owl:Axiom>


</rdf:RDF>
