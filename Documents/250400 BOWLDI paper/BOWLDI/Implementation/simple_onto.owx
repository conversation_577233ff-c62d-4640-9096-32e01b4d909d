<?xml version="1.0"?>
<Ontology xmlns="http://www.w3.org/2002/07/owl#"
     xml:base="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     ontologyIRI="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45">
    <Prefix name="" IRI="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45/"/>
    <Prefix name="owl" IRI="http://www.w3.org/2002/07/owl#"/>
    <Prefix name="rdf" IRI="http://www.w3.org/1999/02/22-rdf-syntax-ns#"/>
    <Prefix name="xml" IRI="http://www.w3.org/XML/1998/namespace"/>
    <Prefix name="xsd" IRI="http://www.w3.org/2001/XMLSchema#"/>
    <Prefix name="rdfs" IRI="http://www.w3.org/2000/01/rdf-schema#"/>
    <Annotation>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <Literal>Simple testing ontology</Literal>
    </Annotation>
    <Declaration>
        <Class IRI="#OWLClass_0ce717fc_6f47_4ba8_9fbb_a6a0bafb6c9a"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_17b1dd44_0d03_4e96_bd43_8b473024f895"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_517633e3_b48e_4dff_8100_57ee5cc26390"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_5aea90c3_aeaf_438b_8c0b_a2c0c20dffbd"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_5c779e39_e9cb_4b05_b8f0_4b300e590c94"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_74bf82db_8758_4990_99af_ec9ab06ab2db"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_92f59436_7bc4_4e8c_96b4_0130c291a402"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_c6eaad81_b7a3_40fd_93fd_e7bc458bec47"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_f2283ff6_d58e_4c64_b0ba_dbff5ffa7f4f"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_118a6fab_1834_4ee7_a851_bec851d84615"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_4a08ff4e_f980_4d8c_b40a_24ac5dda5693"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_5beb39d8_17af_45f0_847a_419d8e711c75"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_68d022a4_2560_445f_8880_61f334206c99"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_823f40a6_18a9_4e4f_b89a_0e16c9ba9be6"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_c9a4e480_8737_48f5_abfd_6fffcc23e18e"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_e34d6b96_2880_4d35_8b11_d68171f009e4"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_eefe4999_e5d1_4aad_949f_13fe9e6898dd"/>
    </Declaration>
    <Declaration>
        <DataProperty IRI="#OWLDataProperty_0c646fc4_9645_48d1_b8f6_583c38dca58d"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_296063c2_5722_4333_baba_43585e40d536"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57"/>
    </Declaration>
    <EquivalentClasses>
        <Class IRI="#OWLClass_517633e3_b48e_4dff_8100_57ee5cc26390"/>
        <ObjectIntersectionOf>
            <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
            <ObjectExactCardinality cardinality="1">
                <ObjectProperty IRI="#OWLObjectProperty_eefe4999_e5d1_4aad_949f_13fe9e6898dd"/>
                <Class IRI="#OWLClass_17b1dd44_0d03_4e96_bd43_8b473024f895"/>
            </ObjectExactCardinality>
        </ObjectIntersectionOf>
    </EquivalentClasses>
    <SubClassOf>
        <Class IRI="#OWLClass_517633e3_b48e_4dff_8100_57ee5cc26390"/>
        <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
    </SubClassOf>
    <SubClassOf>
        <Class IRI="#OWLClass_5aea90c3_aeaf_438b_8c0b_a2c0c20dffbd"/>
        <Class IRI="#OWLClass_0ce717fc_6f47_4ba8_9fbb_a6a0bafb6c9a"/>
    </SubClassOf>
    <SubClassOf>
        <Class IRI="#OWLClass_74bf82db_8758_4990_99af_ec9ab06ab2db"/>
        <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
    </SubClassOf>
    <SubClassOf>
        <Class IRI="#OWLClass_92f59436_7bc4_4e8c_96b4_0130c291a402"/>
        <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
    </SubClassOf>
    <SubClassOf>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
        <Class IRI="#OWLClass_0ce717fc_6f47_4ba8_9fbb_a6a0bafb6c9a"/>
    </SubClassOf>
    <ClassAssertion>
        <Class IRI="#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f"/>
        <NamedIndividual IRI="#OWLNamedIndividual_296063c2_5722_4333_baba_43585e40d536"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
        <NamedIndividual IRI="#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57"/>
    </ClassAssertion>
    <ObjectPropertyAssertion>
        <Annotation>
            <AnnotationProperty abbreviatedIRI="rdfs:isDefinedBy"/>
            <Literal>Bob</Literal>
        </Annotation>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </ObjectPropertyAssertion>
    <InverseObjectProperties>
        <ObjectProperty IRI="#OWLObjectProperty_118a6fab_1834_4ee7_a851_bec851d84615"/>
        <ObjectProperty IRI="#OWLObjectProperty_823f40a6_18a9_4e4f_b89a_0e16c9ba9be6"/>
    </InverseObjectProperties>
    <InverseObjectProperties>
        <ObjectProperty IRI="#OWLObjectProperty_4a08ff4e_f980_4d8c_b40a_24ac5dda5693"/>
        <ObjectProperty IRI="#OWLObjectProperty_e34d6b96_2880_4d35_8b11_d68171f009e4"/>
    </InverseObjectProperties>
    <InverseObjectProperties>
        <ObjectProperty IRI="#OWLObjectProperty_5beb39d8_17af_45f0_847a_419d8e711c75"/>
        <ObjectProperty IRI="#OWLObjectProperty_68d022a4_2560_445f_8880_61f334206c99"/>
    </InverseObjectProperties>
    <InverseObjectProperties>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
        <ObjectProperty IRI="#OWLObjectProperty_c9a4e480_8737_48f5_abfd_6fffcc23e18e"/>
    </InverseObjectProperties>
    <ObjectPropertyDomain>
        <ObjectProperty IRI="#OWLObjectProperty_118a6fab_1834_4ee7_a851_bec851d84615"/>
        <Class IRI="#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f"/>
    </ObjectPropertyDomain>
    <ObjectPropertyDomain>
        <ObjectProperty IRI="#OWLObjectProperty_68d022a4_2560_445f_8880_61f334206c99"/>
        <Class IRI="#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f"/>
    </ObjectPropertyDomain>
    <ObjectPropertyDomain>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
    </ObjectPropertyDomain>
    <ObjectPropertyDomain>
        <ObjectProperty IRI="#OWLObjectProperty_e34d6b96_2880_4d35_8b11_d68171f009e4"/>
        <Class IRI="#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f"/>
    </ObjectPropertyDomain>
    <ObjectPropertyDomain>
        <ObjectProperty IRI="#OWLObjectProperty_eefe4999_e5d1_4aad_949f_13fe9e6898dd"/>
        <Class IRI="#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f"/>
    </ObjectPropertyDomain>
    <ObjectPropertyRange>
        <ObjectProperty IRI="#OWLObjectProperty_118a6fab_1834_4ee7_a851_bec851d84615"/>
        <Class IRI="#OWLClass_5c779e39_e9cb_4b05_b8f0_4b300e590c94"/>
    </ObjectPropertyRange>
    <ObjectPropertyRange>
        <ObjectProperty IRI="#OWLObjectProperty_68d022a4_2560_445f_8880_61f334206c99"/>
        <Class IRI="#OWLClass_5c779e39_e9cb_4b05_b8f0_4b300e590c94"/>
    </ObjectPropertyRange>
    <ObjectPropertyRange>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
        <Class IRI="#OWLClass_0ce717fc_6f47_4ba8_9fbb_a6a0bafb6c9a"/>
    </ObjectPropertyRange>
    <ObjectPropertyRange>
        <ObjectProperty IRI="#OWLObjectProperty_e34d6b96_2880_4d35_8b11_d68171f009e4"/>
        <Class IRI="#OWLClass_c6eaad81_b7a3_40fd_93fd_e7bc458bec47"/>
    </ObjectPropertyRange>
    <ObjectPropertyRange>
        <ObjectProperty IRI="#OWLObjectProperty_eefe4999_e5d1_4aad_949f_13fe9e6898dd"/>
        <Class IRI="#OWLClass_17b1dd44_0d03_4e96_bd43_8b473024f895"/>
    </ObjectPropertyRange>
    <DataPropertyDomain>
        <DataProperty IRI="#OWLDataProperty_0c646fc4_9645_48d1_b8f6_583c38dca58d"/>
        <Class IRI="#OWLClass_5c779e39_e9cb_4b05_b8f0_4b300e590c94"/>
    </DataPropertyDomain>
    <DataPropertyRange>
        <DataProperty IRI="#OWLDataProperty_0c646fc4_9645_48d1_b8f6_583c38dca58d"/>
        <Datatype abbreviatedIRI="xsd:string"/>
    </DataPropertyRange>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_0ce717fc_6f47_4ba8_9fbb_a6a0bafb6c9a</IRI>
        <Literal xml:lang="en-gb">Pet</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_17b1dd44_0d03_4e96_bd43_8b473024f895</IRI>
        <Literal xml:lang="en-gb">Literal</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_470864d9_34a4_4b24_93c5_90f7c024953f</IRI>
        <Literal xml:lang="en-gb">Operator</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_517633e3_b48e_4dff_8100_57ee5cc26390</IRI>
        <Literal xml:lang="en-gb">Not</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:isDefinedBy"/>
        <IRI>#OWLClass_5aea90c3_aeaf_438b_8c0b_a2c0c20dffbd</IRI>
        <Literal>Bob</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_5aea90c3_aeaf_438b_8c0b_a2c0c20dffbd</IRI>
        <Literal xml:lang="en-gb">Cat</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_5c779e39_e9cb_4b05_b8f0_4b300e590c94</IRI>
        <Literal xml:lang="en-gb">Belief</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_74bf82db_8758_4990_99af_ec9ab06ab2db</IRI>
        <Literal xml:lang="en-gb">And</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_92f59436_7bc4_4e8c_96b4_0130c291a402</IRI>
        <Literal xml:lang="en-gb">Or</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_c4e82bed_c7db_4341_bbde_5e9979e4469f</IRI>
        <Literal xml:lang="en-gb">Plan</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_c6eaad81_b7a3_40fd_93fd_e7bc458bec47</IRI>
        <Literal xml:lang="en-gb">Event</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b</IRI>
        <Literal xml:lang="en-gb">Person</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52</IRI>
        <Literal xml:lang="en-gb">Dog</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_f2283ff6_d58e_4c64_b0ba_dbff5ffa7f4f</IRI>
        <Literal xml:lang="en-gb">Goal</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLDataProperty_0c646fc4_9645_48d1_b8f6_583c38dca58d</IRI>
        <Literal xml:lang="en-gb">has value</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_296063c2_5722_4333_baba_43585e40d536</IRI>
        <Literal xml:lang="en-gb">Green Patch Plan</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3</IRI>
        <Literal xml:lang="en-gb">Dusty</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947</IRI>
        <Literal xml:lang="en-gb">Alice</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83</IRI>
        <Literal xml:lang="en-gb">Charles</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7</IRI>
        <Literal xml:lang="en-gb">Rex</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57</IRI>
        <Literal xml:lang="en-gb">Bob</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_118a6fab_1834_4ee7_a851_bec851d84615</IRI>
        <Literal xml:lang="en-gb">has context</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_4a08ff4e_f980_4d8c_b40a_24ac5dda5693</IRI>
        <Literal xml:lang="en-gb">is triggering the plan</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_5beb39d8_17af_45f0_847a_419d8e711c75</IRI>
        <Literal xml:lang="en-gb">is part of body of plan</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_68d022a4_2560_445f_8880_61f334206c99</IRI>
        <Literal xml:lang="en-gb">has body</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6</IRI>
        <Literal xml:lang="en-gb">is owner of</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_823f40a6_18a9_4e4f_b89a_0e16c9ba9be6</IRI>
        <Literal xml:lang="en-gb">is context of</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_c9a4e480_8737_48f5_abfd_6fffcc23e18e</IRI>
        <Literal xml:lang="en-gb">is owned by</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_e34d6b96_2880_4d35_8b11_d68171f009e4</IRI>
        <Literal xml:lang="en-gb">meant to handle event</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_eefe4999_e5d1_4aad_949f_13fe9e6898dd</IRI>
        <Literal xml:lang="en-gb">is applied to literal</Literal>
    </AnnotationAssertion>
</Ontology>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

