// Concepts and Hierarchies
concept(person)[source(ximo)].
concept(wizard).
concept(elf)[source(carlos)].
concept(human).
concept(dwarf).
concept(hobbit).
concept(kingdom).
concept(ring)[source(bogdan)].

is_a(wizard, person)
is_a(elf, person)
is_a(human, person)
is_a(dwarf, person)
is_a(hobbit, person)

// Individuals
individual(gandalf, wizard)
individual(aragorn, human)
individual(legolas, elf)
individual(gimli, dwarf)
individual(frodo, hobbit)
individual(samwise, hobbit)
individual(theoden, human)
individual(sauron, person)
individual(one_ring, ring)
individual(rohan, kingdom)
individual(gondor, kingdom)

// Object Properties
object_property(kingdom, has_king, person)
object_property(person, is_friend_with, person)
object_property(person, has_ring, ring)
object_property(person, fights_against, person)

// Data Properties
data_property(person, has name, string)
data_property(kingdom, has name, string)
data_property(ring, description, string)

// Relations
relation(gandalf, has name, "<PERSON><PERSON><PERSON> the Grey")
relation(arag<PERSON>, has name, "<PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON>")
relation(leg<PERSON><PERSON>, has name, "<PERSON><PERSON><PERSON>")
relation(gim<PERSON>, has name, "<PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON><PERSON>")
relation(frodo, has name, "Frodo Baggins")
relation(theoden, has name, "Théoden, King of Rohan")
relation(sauron, has name, "Sauron, the Dark Lord")
relation(one_ring, description, "The One Ring to Rule Them All")
relation(rohan, has name, "The Kingdom of Rohan")
relation(gondor, has name, "The Kingdom of Gondor")

relation(rohan, has_king, theoden)
relation(gondor, has_king, aragorn)

relation(gandalf, is_friend_with, aragorn)
relation(gandalf, is_friend_with, frodo)
relation(frodo, is_friend_with, samwise)
relation(aragorn, is_friend_with, legolas)
relation(aragorn, is_friend_with, gimli)

relation(sauron, has_ring, one_ring)
relation(frodo, has_ring, one_ring)

relation(sauron, fights_against, frodo)
relation(sauron, fights_against, aragorn)
relation(sauron, fights_against, gandalf)

// Plans

// Plan 1: Protect the One Ring bearer
+!protect_ring_bearer : enemy_of_sauron(RingBearer) <- 
    .print("Protecting the ring bearer:", RingBearer);
    .print("Deploying allies to safeguard", RingBearer);
    ?relation(RingBearer, is_friend_with, Ally);
    .print("Ally identified:", Ally);
    .send(Ally, !defend, RingBearer).

// Plan 2: Rally allies to fight Sauron
+!rally_allies : supports_fight_against_sauron(X) <- 
    .print("Rallying ally:", X);
    ?relation(X, is_friend_with, Y);
    .print("Informing friend:", Y);
    .send(Y, !prepare_for_battle);
    .print("Allies successfully rallied").
