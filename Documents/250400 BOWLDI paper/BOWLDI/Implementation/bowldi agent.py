import argparse
from spade.agent import Agent
from spade.behaviour import OneShotBehaviour
import asyncio

from bowldi import OntologyConverter

class OntologyAgent(Agent):
    class LoadOntologyBehaviour(OneShotBehaviour):
        def __init__(self, file_path):
            super().__init__()
            self.file_path = file_path

        async def run(self):
            if self.file_path:
                print(f"Loading ontology from {self.file_path}...")
                self.agent.converter = OntologyConverter(self.file_path)
                print("Ontology loaded successfully.")

    class GenerateOutputsBehaviour(OneShotBehaviour):
        def __init__(self, run_prolog, run_agentspeak):
            super().__init__()
            self.run_prolog = run_prolog
            self.run_agentspeak = run_agentspeak

        async def run(self):
            if hasattr(self.agent, 'converter') and self.agent.converter:
                print("Generating outputs...")
                self.agent.converter.generate_outputs(self.run_prolog, self.run_agentspeak)
                print("Output generation complete.")
            else:
                print("No ontology loaded. Cannot generate outputs.")

    async def setup(self):
        print("Setting up agent...")
        if hasattr(self, 'file_path') and self.file_path:
            self.add_behaviour(self.LoadOntologyBehaviour(self.file_path))
            self.add_behaviour(self.GenerateOutputsBehaviour(run_prolog=True, run_agentspeak=True))
        else:
            print("No ontology file path provided.")

# Run the main function
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert OWL ontology to Prolog and AgentSpeak representations.")
    parser.add_argument("file_path", type=str, help="Path to the OWL/XML ontology file.")
    parser.add_argument("--prolog", action="store_true", help="Run Prolog conversion.")
    parser.add_argument("--agentspeak", action="store_true", help="Run AgentSpeak conversion.")
    parser.add_argument("--agent", action="store_true", help="Run as a SPADE agent to load the ontology.")
    args = parser.parse_args()

    if args.agent:
        agent = OntologyAgent("ontology_agent@localhost", "password")
        agent.file_path = args.file_path
        future = agent.start()
        future.result()  # Wait for the agent to be fully initialized
        try:
            asyncio.get_event_loop().run_forever()
        except KeyboardInterrupt:
            print("Stopping agent...")
            agent.stop()
    else:
        converter = OntologyConverter(args.file_path)
        converter.generate_outputs(args.prolog, args.agentspeak)
