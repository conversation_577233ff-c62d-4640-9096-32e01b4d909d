concept(Pet)[source(self)].
concept(Literal)[source(self)].
concept(Operator)[source(self)].
concept(Not)[source(self)].
is_a(Not, Operator)[source(self)].
concept(Cat)[source(<PERSON>)].
is_a(<PERSON>, <PERSON>)[source(<PERSON>)].
concept(Belief)[source(self)].
concept(And)[source(self)].
is_a(And, Operator)[source(self)].
concept(Or)[source(self)].
is_a(Or, Operator)[source(self)].
concept(Plan)[source(self)].
concept(Event)[source(self)].
concept(Person)[source(self)].
concept(Dog)[source(self)].
is_a(<PERSON>, Pet)[source(self)].
concept(Goal)[source(self)].
+!drink(beer) : has(owner,beer)
<- sip(beer);
!drink(beer).
object_property(Plan, has_context, Belief)[source(self)].
object_property(Event, is_triggering_the_plan, Plan)[source(self)].
object_property(Belief, is_part_of_body_of_plan, Plan)[source(self)].
object_property(Plan, has_body, Belief)[source(self)].
object_property(Person, is_owner_of, Pet)[source(self)].
object_property(Belief, is_context_of, Plan)[source(self)].
object_property(Pet, is_owned_by, Person).
object_property(Plan, meant_to_handle_event, Event)[source(self)].
object_property(Operator, is_applied_to_literal, Literal)[source(self)].
data_property(Belief, has_value, <class 'str'>)[source(self)].
individual(Green_Patch_Plan, Plan)[source(self)].
individual(Dusty, Dog)[source(self)].
individual(Alice, Person)[source(self)].
individual(Charles, Person)[source(self)].
individual(Rex, Dog)[source(self)].
individual(Bob, Person)[source(self)].
relation(Dusty, is_owned_by, Alice)[source(self)].
relation(Alice, is_owner_of, Dusty)[source(Bob)].
relation(Alice, has_value, jedan).
+!get(beer) : true
<- .send(robot, achieve, has(owner,beer)).
+has(owner,beer) : true
<- !drink(beer).
-has(owner,beer) : true
<- !get(beer).
// while I have beer, sip
+!drink(beer) : has(owner,beer)
<- sip(beer);
!drink(beer).
+!drink(beer) : not has(owner,beer)
<- true.
+msg(M)[source(Ag)] : true
<- .print("Message from ",Ag,": ",M);
-msg(M).

