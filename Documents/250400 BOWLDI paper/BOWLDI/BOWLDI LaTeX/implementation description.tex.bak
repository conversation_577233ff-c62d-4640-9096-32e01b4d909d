

\section{Example of Use}

It is possible to use \mintinline{text}{bowldi.py} to translate OWL ontologies to AgentSpeak code and vice versa. It contains a single class that can convert between the two formats. The class is designed to handle OWL ontologies and AgentSpeak expressions, providing a comprehensive set of methods for loading, extracting, converting, and saving data.

The class takes two arguments at initialisation:
\begin{itemize}
    \item \mintinline{text}{input_file} - the path to the input file (mandatory);
    \item \mintinline{text}{output_file} - the path to the output file (optional).
\end{itemize}

The class is initialized with the input file path, which determines the nature of the data to be processed, whether it is an ontology file (.owx or .owl) or an AgentSpeak file (.asl). The output file path is optional and defaults to \mintinline{text}{output.owl} if the input is an AgentSpeak file, and \mintinline{text}{output.asl} if the input is an ontology file. \Cref{lst: instantiation of the converter} shows the instantiation of the \mintinline{text}{BOWLDIConverter} class.

\begin{listing}
    \inputminted[firstline=673, lastline=687]{python}{Implementation/bowldi.py}
    \caption{Instantiation of the BOWLDIConverter class}
    \label{lst: instantiation of the converter}
\end{listing}

\clearpage
The following extended abstract of the implemented class is generated based on the source code.

\blockquote{
    \mintinline{text}{BOWLDIConverter} class in the bowldi.py file is a comprehensive tool designed to facilitate the conversion between ontologies and AgentSpeak expressions. This Python class is initialized with an input file path, which is mandatory, and an optional output file path. The input file path determines the nature of the data to be processed, whether it is an ontology file (.owx or .owl) or an AgentSpeak file (.asl).

Upon initialization, the class sets the output file path based on the input file's suffix. If the input is an AgentSpeak file, the output defaults to output.owl, and if the input is an ontology file, the output defaults to output.asl. This ensures that the output format is appropriate for the type of input data being processed.

The class then checks if the input file exists. If it does, the file path is resolved to an absolute path. For ontology files, the class loads the ontology and the associated world using the \mintinline{text}{load_ontology} method. It then calls the \mintinline{text}{generate_agentspeak} method to convert the ontology into AgentSpeak expressions.

For AgentSpeak files, the class loads the ontology from the output file path, reads the AgentSpeak expressions from the input file, and parses them using the \mintinline{text}{parse_agentspeak_expressions} method. This dual functionality allows the class to handle both types of input files effectively.

The \mintinline{text}{clean_data} method is used to clean the input data by removing extra spaces and converting to snake case. The \mintinline{text}{clean_string} method is a helper function that replaces spaces between words with underscores, except within quoted strings.

The \mintinline{text}{load_ontology} method loads the ontology using the owlready2 library, synchronizes the reasoner, and sets the rendering function to use labels. The \mintinline{text}{extract_ontology_information} method extracts concepts, relations, and individuals from the ontology and stores them in the data dictionary.

The \mintinline{text}{convert_ontology_concepts}, \mintinline{text}{convert_ontology_object_properties}, \mintinline{text}{convert_ontology_data_properties}, \mintinline{text}{convert_ontology_individuals}, and \mintinline{text}{convert_ontology_individual_relations} methods convert the extracted ontology information into AgentSpeak representation. These methods generate the corresponding AgentSpeak expressions for concepts, object properties, data properties, individuals, and individual relations.

The \mintinline{text}{convert_ontology_information} method combines the converted concepts, relations, and individuals into a single AgentSpeak representation. It calls the individual conversion methods and cleans the resulting data.

The \mintinline{text}{create_concept}, \mintinline{text}{create_object_property}, \mintinline{text}{create_data_property}, \mintinline{text}{create_individual}, \mintinline{text}{add_object_property_to_individual}, and \mintinline{text}{add_data_property_to_individual} methods are used to create and manipulate ontology elements. These methods ensure that the ontology is correctly populated with the necessary concepts, properties, and individuals.

The \mintinline{text}{create_agentspeak_rule} method creates an AgentSpeak rule by adding data properties to an individual representing the rule. The \mintinline{text}{save_ontology} method saves the ontology to the specified file path, removing any existing file at that path.

The \mintinline{text}{parse_agentspeak_expressions} method parses AgentSpeak expressions and converts them into ontology elements. It uses regular expressions to identify and process different types of expressions, such as concepts, properties, and relations.

The \mintinline{text}{generate_agentspeak} method extracts information from the ontology and converts it into AgentSpeak representation. It saves the resulting AgentSpeak expressions to the output file path.

Finally, the script includes a main function that sets up argument parsing for command-line execution. It allows the user to specify input and output file paths and initializes the \mintinline{text}{BOWLDIConverter} class with these paths.

In summary, the \mintinline{text}{BOWLDIConverter} class is a robust and versatile tool for converting between ontologies and AgentSpeak expressions. It provides comprehensive methods for loading, extracting, converting, and saving data, ensuring that the input data is accurately processed and transformed into the desired output format.

}

\subsection{Installing the required libraries}

The applicable libraries have been collected into a conda environment file to run everything smoothly. The file can be seen in \cref{lst: conda env file} and can be found in the repository under the name \mintinline{text}{env.yml}. The environment can be created by running the following command:

\mint{bash}|conda env create -f env.yml|

The environment can be activated by running the following command:

\mint{bash}|conda activate bowldi|

\begin{listing}
    \inputminted{yaml}{Implementation/env.yml}
    \caption{Conda environment file}
    \label{lst: conda env file}
\end{listing}

\subsection{Translating AgentSpeak to OWL}

To run the converter from AgentSpeak to OWL, the following command can be used:

\mint{bash}|python bowldi.py input.asl|

This command will generate an OWL file named \mintinline{text}{output.owl} with the content of the AgentSpeak file \mintinline{text}{input.asl}.

AgentSpeak expressions are extracted from the input file and converted into OWL ontology elements. The responsible method uses regular expressions to identify and process different types of expressions, such as concepts, properties, and relations.

Based on the type of the concept that was extracted from the AgentSpeak file, the method performs one of the following actions (\cref{lst: extract agentspeak}):

\begin{itemize}
    \item If the concept is a class, the method creates a new class in the ontology.
    
    \item If the concept is a part of another concept, the method adds the parent concept to the set of parent concepts of the current concept.
    
    \item If the concept is an object property, the method creates a new object property in the ontology.
    
    \item If the concept is a data property, the method creates a new data property in the ontology.
    \item If the concept is an individual, the method creates a new individual in the ontology.
    
    \item If the concept is a relation between individuals, the method creates a new relation between the specified individuals. The relation uses an existing object or data property to represent the relation between the individuals.
    
\end{itemize}

\begin{listing}
    \inputminted[firstline=608, lastline=633]{python}{Implementation/bowldi.py}
    \caption{Extracting AgentSpeak expressions from the input file}
    \label{lst: extract agentspeak}
\end{listing}

A new regex pattern is used to extract plans from the AgentSpeak file. The method then creates a new class in the ontology to represent the plan. Plans are special because they use pre-defined data properties to represent their parts: the trigger (\mintinline{text}|rule_has_event|), the context (\mintinline{text}|rule_has_context|), and the body (\mintinline{text}|rule_has_body|). The method (shown in \cref{lst: create a plan}) creates these data properties in the ontology and uses them to relate the plan to its parts.

\begin{listing}
    \inputminted[firstline=560, lastline=574]{python}{Implementation/bowldi.py}
    \caption{Method for creating an AgentSpeak plan in the ontology}
    \label{lst: create a plan}
\end{listing}

\paragraph{Creating a concept} The concept creation method in the ontology is shown in \cref{lst: create a concept}. It takes the concept name and parent concepts as arguments and creates a new class in the ontology unless the concept exists in the ontology already. If the concept has parent concepts, the method adds them to the set of parent concepts of the new class.

\begin{listing}
    \inputminted[firstline=420, lastline=437]{python}{Implementation/bowldi.py}
    \caption{Method for creating a concept in the ontology}
    \label{lst: create a concept}
\end{listing}

\paragraph{Creating an object property} The method for creating an object property in the ontology is shown in \cref{lst: create an object property}. It takes the property name, domain, and range as arguments and creates a new object property in the ontology unless the property exists in the ontology already. The method then sets the domain and range of the property to the specified classes.

\begin{listing}
    \inputminted[firstline=439, lastline=477]{python}{Implementation/bowldi.py}
    \caption{Method for creating an object property in the ontology}
    \label{lst: create an object property}
\end{listing}

\paragraph{Creating a data property} The method for creating a data property is similar to the method for creating an object property. It takes the property name, domain, and range as arguments and creates a new data property in the ontology unless the property exists in the ontology already. The method then sets the domain and range of the property to the specified classes. The range of the observed property is based on the type of value associated with the property.

\paragraph{Creating an individual} The method for creating an individual in the ontology is shown in \cref{lst: create an individual}. It takes the individual name and concept as arguments and creates a new individual in the ontology. If the concept is not present in the ontology already, the method creates it. The individual is then added to the specified concept.

\begin{listing}
    \inputminted[firstline=510, lastline=521]{python}{Implementation/bowldi.py}
    \caption{Method for creating an individual in the ontology}
    \label{lst: create an individual}
\end{listing}

\paragraph{Relating an individual} To add an instance of an object or a data property to the ontology, two methods are implemented: 
\begin{itemize}
    \item \mintinline{text}|add_object_property_to_individual|;
    \item \mintinline{text}|add_data_property_to_individual|.
\end{itemize}

If the property in question does not exist in the ontology already, both methods try to create it.

\paragraph{Saving the ontology} The last step in the conversion process is saving the ontology with the converted entities. The ontology saving method is shown in \cref{lst: save the ontology}. It takes the output file path as an argument and saves the ontology to the specified path. If a file already exists on that path, it is removed before saving the ontology. If no output file path is specified, the method saves the ontology to the default output file path: \mintinline{text}|output.owl|.

\begin{listing}
    \inputminted[firstline=576, lastline=580]{python}{Implementation/bowldi.py}
    \caption{Method for saving the ontology}
    \label{lst: save the ontology}
\end{listing}



\subsection{Translating OWL to AgentSpeak}

To run the converter from OWL to AgentSpeak, the following command can be used:

\mint{bash}|python bowldi.py input.owl|

This command will generate an AgentSpeak file named \mintinline{text}{output.asl} with the content mirroring the OWL file \mintinline{text}{input.owl}.

The method for converting an ontology to AgentSpeak expressions extracts concepts, object properties, data properties, individuals, and individual relations from the ontology and converts them into AgentSpeak representation. The method then saves the resulting AgentSpeak expressions to the output file path.

\paragraph{Extracting ontology information} 
While extracting the data, every concept, object property, data property, individual and individual relation is visited, extracted from the ontology, and stored in a dictionary. The applicable methods use the dictionary to generate the corresponding AgentSpeak expressions, as shown in \cref{lst: convert ontology information}.

\begin{listing}
    \inputminted[firstline=382, lastline=418]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology information into AgentSpeak expressions}
    \label{lst: convert ontology information}
\end{listing}

\paragraph{Converting ontology concepts}
The method for converting ontology concepts into AgentSpeak expressions is shown in \cref{lst: convert ontology concepts}. It takes the extracted concepts as an argument and generates AgentSpeak expressions for each concept. Where applicable, the method creates a new AgentSpeak expression for each concept, including the concept name and parent concepts.

\begin{listing}
    \inputminted[firstline=253, lastline=275]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology concepts into AgentSpeak expressions}
    \label{lst: convert ontology concepts}
\end{listing}

\paragraph{Converting ontology object properties}

The method for converting ontology object properties into AgentSpeak expressions is shown in \cref{lst: convert ontology object properties}. It takes the extracted object properties as an argument and generates AgentSpeak expressions for each object property. The method creates a new AgentSpeak expression for each object property, including the property name, domain, and range. If inverse properties are present, the method includes them in the AgentSpeak expression.

\begin{listing}
    \inputminted[firstline=277, lastline=304]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology object properties into AgentSpeak expressions}
    \label{lst: convert ontology object properties}
\end{listing}

\paragraph{Converting ontology data properties}

The method for converting ontology data properties into AgentSpeak expressions is shown in \cref{lst: convert ontology data properties}. It takes the extracted data properties as an argument and generates an AgentSpeak expression for each data property.

\begin{listing}
    \inputminted[firstline=306, lastline=323]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology data properties into AgentSpeak expressions}
    \label{lst: convert ontology data properties}
\end{listing}

\paragraph{Converting ontology individuals}

The method for converting ontology individuals into AgentSpeak expressions is shown in \cref{lst: convert ontology individuals}. It takes the extracted individuals as an argument and generates an AgentSpeak expression for each individual. The method creates a new AgentSpeak expression for each individual, including the individual name and its concept.

\begin{listing}
    \inputminted[firstline=325, lastline=342]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology individuals into AgentSpeak expressions}
    \label{lst: convert ontology individuals}
\end{listing}

\paragraph{Converting ontology individual relations}

The method for converting ontology individual relations into AgentSpeak expressions is shown in \cref{lst: convert ontology individual relations}. It takes the extracted individual relations as an argument and generates an AgentSpeak expression for each relation. In this context, a relation is a property coming from an individual, irrespective of whether the property is an object or a data property. The method creates a new AgentSpeak expression for each such relation, including the relation name, the individuals or values involved, and the property used to relate them.

A special case within this method is handling properties related to \mintinline{text}|Plan| individuals. These properties represent a plan's trigger, context, and body, respectively. The method creates a new AgentSpeak plan expression for each set of these properties, including the plan name and its parts. The \mintinline{text}|Plan| concept must be present in the ontology for this conversion to be successful.

\begin{listing}
    \inputminted[firstline=344, lastline=379]{python}{Implementation/bowldi.py}
    \caption{Method for converting ontology individual relations into AgentSpeak expressions}
    \label{lst: convert ontology individual relations}
\end{listing}

\paragraph{Saving the AgentSpeak expressions}

The last step in the conversion process is saving the AgentSpeak expressions to the output file. The method for saving the AgentSpeak expressions is shown in \cref{lst: save the agentspeak}. It takes the output file path and saves the AgentSpeak expressions to the specified path. If a file already exists on that path, it is removed before saving the AgentSpeak expressions. If no output file path is specified, the method saves the AgentSpeak expressions to the default output file path: \mintinline{text}|output.asl|.

\begin{listing}
    \inputminted[firstline=655, lastline=669]{python}{Implementation/bowldi.py}
    \caption{Method for saving the AgentSpeak expressions}
    \label{lst: save the agentspeak}
\end{listing}