% This is samplepaper.tex, a sample chapter demonstrating the
% LLNCS macro package for Springer Computer Science proceedings;
% Version 2.21 of 2022/01/12
%
%\documentclass[runningheads]{llncs}
\documentclass[a4paper,fleqn]{cas-dc}
\usepackage[numbers]{natbib}
%
%\usepackage[T1]{fontenc}
% T1 fonts will be used to generate the final print and online PDFs,
% so please use T1 fonts in your manuscript whenever possible.
% Other font encondings may result in incorrect characters.
%
\usepackage{amsmath}
\usepackage{hyperref}
\usepackage{graphicx}
\usepackage{cleveref}
\usepackage{csquotes}
\usepackage{soul}
\usepackage{lipsum} 

\newcommand{\additioncommment}[1]{\begin{center}\hl{#1}\end{center}}

\usepackage{listings}
\usepackage{xcolor}

\lstdefinelanguage{Turtle}{
  keywords={rdf:type, rdfs:subClassOf, owl:Class, owl:Thing, rdfs:label, owl:ObjectProperty, owl:DatatypeProperty, owl:NamedIndividual, rdfs:domain, rdfs:range, xsd:string},
  sensitive=false,
  morecomment=[l]{\#},
  morestring=[b]",
}

\lstset{
  language=Turtle,
  basicstyle=\ttfamily\scriptsize, % más pequeño que footnotesize
  keywordstyle=\color{blue},
  stringstyle=\color{red},
  commentstyle=\color{gray},
  numbers=left,
  numberstyle=\tiny\color{gray},
  numbersep=5pt,
  frame=tb, % línea arriba y abajo
  breaklines=true,
  breakatwhitespace=false,
  showstringspaces=false,
  captionpos=b,
  tabsize=2,
}


\usepackage{minted}
\setminted{
    autogobble,
    breaklines,
    frame=lines,
    linenos,
    % numberblanklines=false,
    stripall,
    numbersep=0.2em,
    fontsize=\footnotesize,
    style=arduino,
    tabsize=4,
    texcomments
}

\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
\tsc{EP}
\tsc{PMS}
\tsc{BEC}
\tsc{DE}

\newcommand{\cmark}{\ding{51}}
\newcommand{\xmark}{\ding{55}}
\newcommand{\genia}{\textit{GenIA\textsuperscript{3}}}

\shorttitle{Normative Affective Agents}

\shortauthors{J. Taverner et~al.}

\tnotemark[1,2]

\begin{document}

\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}
\shorttitle{}
\shortauthors{}

\title[mode = title]{BOWLDI: A New Integration of OWL Ontologies in the Believes of AgentSpeak BDI Language}


\author[1]{Bogdan {Okre\v{s}a \DJ uri\'{c}}}[orcid=0000-0001-5653-6646]



\author[2]{Joaquin Taverner}[orcid=0000-0002-5163-5335]
\ead{<EMAIL>}
\cormark[1]
\cortext[cor1]{Corresponding author}

\author[2]{Carlos Carrascosa}[orcid=0000-0003-3649-6530]

\address[1]{University of Zagreb Faculty of Organization and Informatics, Pavlinska 2, 42000, Vara\v{z}din, Croatia}

\address[2]{Valencian Research Institute for Artificial Intelligence (VRAIN), Universitat Polit\`ecnica de Val\`encia, Camino de Vera s/n, 46022, Valencia, Spain}


\begin{abstract}
Ontologies provide a formal and structured way to represent knowledge, defining concepts and their relationships within a domain. They enable interoperability by creating a shared vocabulary that can be understood across different systems. In multi-agent systems, ontologies can be particularly valuable for improving knowledge sharing and coordination, especially in heterogeneous environments where agents may have different representations of the environment. This paper introduces BOWLDI, a framework that enables the integration of OWL ontologies into BDI agents. BOWLDI acts as an intermediary layer between the agent's deliberative engine and an OWL-based knowledge base, allowing agents to dynamically query, reason about, and incorporate semantic information into their beliefs. The framework includes a mapping mechanism that translates OWL concepts into beliefs compatible with the agent's logic without altering the core reasoning structure. This integration provides a richer representation of the environment, enhancing the agent's ability to make informed decisions. Additionally, BOWLDI supports differentiated knowledge management by distinguishing between public knowledge, which is shared with other agents, and private knowledge, which is retained for internal reasoning. This selective sharing mechanism enables agents to control the dissemination of information based on relevance or sensitivity, improving overall system flexibility and security.
\end{abstract}


\begin{highlights}
\item Shared semantic knowledge inclusion in AgentSpeak agents
\item OWL Ontologies and BDI integration
\item AgentSpeak - OWL translation in both ways
\item Clear distinction between private and public knowledge
\end{highlights}

\begin{keywords}
Ontology\sep multi-agent systems\sep BDI\sep OWL
\end{keywords}

\maketitle

% Used for displaying a sample figure. If possible, figure files should
% be included in EPS format.
%
% If you use the hyperref package, please uncomment the following two lines
% to display URLs in blue roman font according to Springer's eBook style:
%\usepackage{color}
%\renewcommand\UrlFont{\color{blue}\rmfamily}
%\urlstyle{rm}

%
%\title{BOWLDI: A new integration of OWL ontologies in the Believes of AgentSpeak BDI Language}
%\titlerunning{BOWLDI}
%
%\titlerunning{Abbreviated paper title}
% If the paper title is too long for the running head, you can set
% an abbreviated paper title here
%
%\author{First Author\inst{1}\orcidID{0000-1111-2222-3333} \and
%Second Author\inst{2,3}\orcidID{1111-2222-3333-4444} \and
%Third Author\inst{3}\orcidID{2222--3333-4444-5555}}
%
%\authorrunning{F. Author et al.}
% First names are abbreviated in the running head.
% If there are more than two authors, 'et al.' is used.
%
%\institute{Princeton University, Princeton NJ 08544, USA \and
%Springer Heidelberg, Tiergartenstr. 17, 69121 Heidelberg, Germany
%\email{<EMAIL>}\\
%\url{http://www.springer.com/gp/computer-science/lncs} \and
%ABC Institute, Rupert-Karls-University Heidelberg, Heidelberg, Germany\\
%\email{\{abc,lncs\}@uni-heidelberg.de}}
%


            % typeset the header of the contribution
%
%\begin{abstract}
%The abstract should briefly summarize the contents of the paper in
%150--250 words.

%\keywords{First keyword  \and Second keyword \and Another keyword.}
%\end{abstract}
%
%
%



\section{Introduction}\label{sec: Introduction}

%Ontologies are the solution to the problem of having a common vocabulary in communication in distributed systems, being multi-agent systems or web services.

%Nevertheless, in a practical way, its use has been more successful in the web services area (as the source of tags of schema.org for instance) than in the multi-agent systems area where there lacks a seamless integration of ontologies that allows their easy and efficient usage. This is the motivation of this work, whose goal is the seamless integration of OWL ontologies (being OWL the W3C standard Web Ontology Language) in AgentSpeak-based BDI agents. To test the integration of the proposal, we have presented an example using SPADE agents.

%\additioncommment{   What is OWL? What is BDI? What is AgentSpeak?}

%\additioncommment{Motivation: ontologies are not very often used in practical use in MASs. Add something about ontologies and LLMs.}

Ontologies provide a formal representation of knowledge, defining the concepts within a domain and the relationships between them \cite{Massari2024}. By means of an ontology, it is possible to model knowledge in a precise and structured way, creating common semantics that can be understood and used by different actors. One of the most widely used languages in the definition of ontologies is OWL (Web Ontology Language) \cite{martin2004owl}, developed by the W3C. This language has been consolidated as a formal standard for representing complex knowledge on the web. OWL allows the definition of classes, properties, and relationships between entities, as well as specifying restrictions such as cardinality, equivalences, or hierarchies. This type of structured knowledge is particularly useful in the context of multi-agent systems (MAS), where agents must interact and make decisions based on information that may be distributed and complex \cite{hadzic2009ontology}.

In this sense, BDI (Belief-Desire-Intention) \cite{de2020bdi} agents can benefit greatly from ontologies. BDI agents make decisions based on their beliefs, desires and intentions. Ontologies can enrich an agent's beliefs, providing a structured and shared knowledge base that facilitates interpretation of the environment and communication with other agents. By using a common ontology, agents can align their representations of the world, reducing ambiguities and improving interoperability within the system, especially in distributed systems.

One of the best-known languages for agent development under the BDI paradigm is AgentSpeak \cite{bordini2007java}. AgentSpeak allows the explicit programming of agent behaviour through plans conditioned by beliefs, desires, and intentions. Agents can share knowledge through communication by using different illocutionary acts, such as \emph{tell}, \emph{ask} or \emph{achieve} \cite{bordini2007programming}. However, their ability to handle complex or semantically structured knowledge is limited, as the agent's knowledge is stored in predicates based on first-order logic. This representation, while sufficient to express simple facts and facilitate basic reasoning, lacks the semantic richness needed to model hierarchical relationships, ontological constraints or more advanced inferences about the domain. Ontologies can provide a formal and expressive framework for knowledge representation so that agents can enrich their belief base by accessing structured and shared information.

In this paper, we present BOWLDI\footnote{Available on \href{https://github.com/AILab-FOI/MAGO/tree/main/Extra/BOWLDI}{GitHub}}, a framework for integrating ontologies into BDI agents developed on Agent\-Speak-based platforms. The aim of BOWLDI is to extend the ability of agents to interact with formalised knowledge expressed in OWL. To this end, BOWLDI acts as an intermediate layer between the agent's deliberative engine and an ontological knowledge base expressed in OWL, allowing agents to dynamically consult, reason and incorporate semantic information into their set of beliefs. The framework provides a mapping mechanism that translates ontological instances and concepts into beliefs compatible with the agent architecture. This allows agents to access a structured and shared knowledge base, improving semantic coherence in communication and interpretation of the environment, without altering the underlying deliberative logic.  Additionally, BOWLDI supports managing knowledge by distinguishing between public knowledge, accessible to other agents, and private knowledge, retained for internal reasoning. This distinction plays a key role in how agents interact with the shared knowledge base and selectively share information depending on its relevance or sensitivity.

The rest of the paper is organized as follows: Section~\ref{sec:soa} reviews the state of the art in ontology integration with multi-agent systems. Section~\ref{sec:agentspeak} introduces a brief theoretical background of AgentSpeak. Section~\ref{sec: Integrating OWL as Believes language} discusses the integration of OWL ontologies as agent beliefs. Section~\ref{sec: private public knowledge} explains the distinction between private and public knowledge in agents. Section ~\ref{sec: Example} showcases BOWLDI workflow implementation through different examples of use. Finally, Section~\ref{sec:conclusion} presents the conclusions and future work.


\section{State of the Art}\label{sec:soa}


Ontologies can be used to structure and represent knowledge in a formal way, which improves the understanding and management of information within a system \cite{Massari2024}. Through the definition of concepts and relationships within a specific domain, ontologies facilitate data interpretation and decision-making, contributing to greater process efficiency. In the context of multi-agent systems, their use can be particularly useful to allow agents to manage their knowledge in a more organized way and make more informed decisions \cite{huhns1997ontologies,luke1997ontology}. Therefore, ontologies have been used previously related to agents as part of the agent's design, from where to extract the agent's goals and domain formalization \cite{freitas2017applying,anvari2017multi}. For example, MAMbO5 \cite{okresaduric2019MAMbO5NewOntology} introduces an ontology framework designed to improve the modelling and management of intelligent virtual environments (IVEs) in MAS. The ontology provides a hierarchical structure that organizes agents, resources, and environmental entities, supporting scalable and adaptive simulations. In this way, MAMbO5 offers a standardized approach that facilitates the creation of robust and maintainable IVEs capable of supporting emergent behaviours and decentralized decision-making in dynamic systems.

One of the most widely used architectures for modelling autonomous agents is BDI (Belief-Desire-Intention) \cite{de2020bdi,georgeff1999belief}. Beliefs represent the agent's knowledge about the world. This knowledge can be updated as the agent receives new information from the environment (i.e., by perceiving the environment or by communicating through messages with other agents) or by inference based on predefined logical rules. Desires express the goals or states of the world that the agent wishes to achieve. Intentions are the action plans that the agent has decided to follow, based on its beliefs, in order to fulfil its desires. 

Ontologies have been previously integrated into BDI systems to enhance the organization and representation of domain-specific knowledge, allowing for more structured decision-making and communication. For instance, in \cite{van2011goal}, a goal-oriented communication framework is proposed using BDI agents acting as virtual humans in crisis simulation scenarios. It integrates an ontology to structure domain-specific knowledge (e.g. roles, tasks and environmental states), which allows agents to generate dialogues that are contextualised and consistent with defined goals. By using this ontology, agents are able to generate natural, goal-oriented communications that are also adaptive to changing situations. However, the approach does not rely on a particularly formal methodology for ontology construction and sharing. In contrast, \cite{liu2012using} presents a more structured and formally defined ontology-based BDI architecture, developed for the semantic web using the JadeX platform \cite{braubach2005jadex}. The agent dynamically generates customized workflows and binding web services described by means of OWL-S \cite{martin2004owl}. To this end, the architecture consists of four components: the application ontology, which describes a specific domain; the operation ontology, which defines the BDI agent; the BDI agent engines, which process goals, beliefs, workflows and actions; and the JadeX extension, which enables dynamic integration of web services. The BDI agent engines work together to process information and handle user requests. First, the goal engine defines the goal that the user wants to achieve. Second, the belief engine manages and updates the user's information. Third, the workflow engine generates customized processes according to specific needs. Finally, the action engine links the relevant web services to the generated workflow. Another interesting work can be found in \cite{ribino2013ontology}. The authors propose a methodological approach for the development of multi-agent systems based on the JaCaMo framework \cite{boissier2013multi}. This approach is based on three key aspects: requirements expressed through objectives, ontological formalization of the problem domain, and BDI agent modelling. There are two fundamental activities that approach the initial requirements analysis phase. First, an ontology is used to represent the problem domain, which helps to structure and clarify the concepts involved. From this ontology, the objectives to be achieved by the system are identified, which allows a better understanding and a reduction of ambiguities in the requirements.

In recent years, natural language processing (NLP) has gained increasing relevance, enhancing the ability of machines to understand and generate human language more accurately \cite{doumanas2024human,tsaneva2024llm}. This progress has opened up new possibilities for integrating NLP into the development of autonomous agents, enabling more natural and efficient interaction with users. In this context, \cite{ichida2024bdi} presents NatBDI, a hybrid architecture that combines NLP and reinforcement learning with a BDI framework. The authors propose using natural language to communicate goals and plans, and they employ an ontology to structure and organize these elements. Additionally, they use reinforcement learning to adapt the system when no applicable plans exist for a given goal.
 


\section{AgentSpeak Python Library}\label{sec:agentspeak}

In the context of BDI-based systems, one of the most widely used agent-oriented languages is AgentSpeak \cite{rao1996agentspeak}. AgentSpeak is a high-level language designed for programming intelligent agents, where agents follow a decision-making process based on classical practical reasoning. The language allows agents to reason about their goals (desires), the state of the world (beliefs), and the actions they intend to take (intentions). Agents can dynamically update their beliefs based on new information from the environment or communication with other agents and adapt their intentions accordingly. 


Being a high-level language, AgentSpeak needs to be compiled into other languages, such as Java via the Jason platform \cite{bordini2007programming} or Python through the ``python-agentspeak'' library\footnote{\url{https://github.com/niklasf/python-agentspeak}}. The ``python-agentspeak'' library enables the integration of AgentSpeak with various tools in the Python ecosystem, including artificial intelligence, data processing, and automation libraries.

The syntax of AgentSpeak is based on first-order logic to represent beliefs, desires, and intentions through the use of predicates. The predicates are of the form:
\begin{gather}
    \texttt{functor}(\texttt{term}_1, \texttt{term}_2, \cdots , \texttt{term}_n)
\end{gather}

For example, to express the belief that Frodo is the ring-bearer, the following syntax is used:
\begin{gather}
    \texttt{bearer}(\texttt{ring}, \texttt{frodo})
\end{gather}

Capitalized terms are considered variables, similar to the use of ‘?’ in other languages such as Lisp.

In \cite{bordini2007java}, the concept of annotation was added to the predicates. Annotations are, in turn, a list of predicates that allow additional information to be added. The most common annotation is the ``source'' predicate, which indicates the source of the information. For example, to represent that Elrond names Frodo as the bearer of the ring, the following syntax can be used:
\begin{gather}
    \texttt{bearer}(\texttt{ring}, \texttt{frodo}) [\texttt{source}(\texttt{elrond})]
\end{gather}

Plans are expressed through the use of rules with a left-hand side representing the activation condition and a right-hand side defining the body of the plan (i.e. the sequence of actions, separated by semicolons and ended by a full stop) that the agent must execute if the condition is met. The condition is composed of a trigger event and a context that must be true for the plan to be triggered:
\begin{gather}
    \texttt{<trigger\_event>}: \texttt{<context>} \leftarrow \texttt{<body>}.
\end{gather}

For example, the plan to destroy the ring can be defined as:
\begin{align}
    &+!\texttt{destroy\_ring}[\texttt{source}(\texttt{gandalf})]: \notag \\
    &\texttt{at}(\texttt{orodruin}, \texttt{frodo}) \, ~\texttt{\&}~ \, \texttt{bearer}(\texttt{ring}, \texttt{frodo}) \notag \\
    &\leftarrow \notag \\
    &\quad -\texttt{bearer}(\texttt{ring}, \texttt{frodo}); \notag \\
    &\quad +\texttt{destroyed}(\texttt{ring}); \notag \\
    &\quad \texttt{.print(`mission completed`)}
\end{align}

The plan is triggered by the achievement goal ``+!destroy\_ring'', which is applicable when ``frodo'' is at ``orodruin'' and is the ``bearer'' of the ``ring''. Upon activation, the plan executes a sequence of actions: first, the fact that the agent is the ring bearer is removed (``-bearer(ring, frodo)''), indicating that the ring is no longer in possession of Frodo. Then, the fact that the ring has been destroyed is added (``+destroyed(ring)''). Finally, ``.print(`mission completed')''  is an internal action that, in this case, prints a message on the console. Internal actions' name has to begin with a dot and can be implemented by the user to add additional actions. In ``python-agentspeak'' these actions are defined as Python functions and can be invoked within AgentSpeak plans to interact with the environment, perform computations, or integrate with external libraries.



\section{Integrating OWL as Believes language}\label{sec: Integrating OWL as Believes language}

\begin{figure*}
    \centering
    \includegraphics[width=\textwidth]{figuras.pdf}
    \caption{Common and private knowledge in the agents}
    \label{fig: knowledge}
\end{figure*}

Building on the definition of a BDI agent, given in Sec. \ref{sec:soa}, it is clear that all three components build a completely functional agent together. This paper focuses on modelling, formalising, and utilising the beliefs of a BDI agent. Agent's beliefs can be modelled using AgentSpeak, as shown in Sec. \ref{sec:agentspeak}. Other forms of formalising beliefs exist and are in use, whereof the one in the focus of this paper is the use of ontologies, i.e. OWL.

Since agent interaction, especially in the forms of cooperation and competition, is one of the crucial features of a MAS, it is not feasible to allow each agent to have its own set of concepts, whereof a majority is unknown to other agents of the same environment. One of the fundamental features of ontologies addresses this issue: they are created to be shared. Utilising a common, shared ontology made available to agents, e.g. within the same environment, allows all agents with access to the observed ontology to understand, use, and reason upon a common set of concept definitions. This, in turn, ensures consistent and unambiguous communication between agents that share a common ontology. 

%One of the most important steps in designing a MAS is to describe the different interactions that will exist in it. These interactions are made possible only if the different participating agents use a common vocabulary. The approach of this paper, as seen in Figure \ref{fig: knowledge}, proposes using a common OWL ontology (usually formed by a set of classes and properties describing them), as this common vocabulary is needed for a MAS to interact. This idea is directed even to the design of the MAS, as one of the first steps has to be to decide the common vocabulary, that is, the ontology to be used.

One of the most important steps in designing a MAS is defining the interactions between agents. These interactions are only possible if all participating agents share a common vocabulary. This paper proposes using a shared OWL ontology (typically composed of classes and their properties) as this common vocabulary, as illustrated in Figure \ref{fig: knowledge}. Establishing this ontology should be an early step in the MAS design process, ensuring that agents can communicate and interact effectively.

The proposed ontology will be used to initialize the beliefs of all the agents in the system, as it will be the vocabulary to be used to form the agent's public knowledge (beliefs). A set of individuals of the OWL common ontology will form this public knowledge. These agents may also have a subset of beliefs that form private knowledge of the agent that may or may not share vocabulary with the agent's public knowledge.



\subsection{OWL as source and target for Believes}

It is complex to translate compound ranges of object properties. These can be a conjunction or a disjunction of a set of classes. The complexity arises when the range consists of a conjunction of classes. Translating a conjunction of classes is not a simple task, i.e. it has to be carefully thought through to represent such a state in Prolog or AgentSpeak.

The approach used in the implementation presented in this paper considers the source value of an AgentSpeak belief as a piece of data designating where the observed belief originated, i.e. where it comes from. An agent may tag a certain belief as being sourced from a perception, self-inferred, another agent, an imported ontology, or a customised source string. A source value may be stated explicitly or rendered based on the available data.

When converting an ontology containing various concepts into a set of AgentSpeak expressions, the translator checks the IRI (international resource identifier) values of individual entities stored within an ontology. Should the IRI of an observed entity, i.e. a concept, a property, or an individual, differ from the IRI of the observed ontology, it may be considered an entity initially defined by an outside source. Hence, such an entity is designated as sourced by the applicable ontology, referenced by its IRI.

When the translation is conducted in the other way, i.e. converting a set of AgentSpeak expressions into an ontology file, each belief's \texttt{source} argument is considered. The set of distinct URLs found in the collection of \texttt{source} attribute values within the observed AgentSpeak set of expressions is considered an ontology that can be imported into the translating process. Should an ontology be successfully imported while translation is in process, any concepts or properties found in an imported ontology will not be translated and added to the output ontology. Therefore, the translated AgentSpeak set of expressions will not feature entities sourced from external knowledge sources.

The above-described approach to translating ontologies to a set of AgentSpeak expressions and vice versa, it is argued here, enables BDI agents to discern private from public knowledge. Public knowledge is, within this paper, considered to be knowledge that is either publicly available and usable or shared publicly by specific agents. Public knowledge is used for standardising communication and resolving potential conflicts that may arise from overlapping intentions of concepts or synonyms and similar concepts. Optimally, public knowledge contains definitions of concepts, properties, and rules. These can be used in personal ontologies that extend imported ontologies with new concepts, properties, and individuals. Individuals are argued here to be highly specific, although not completely exclusive, to the personal knowledge of an agent. Hence, personal knowledge consists of knowledge that extends public knowledge and is particular to a specific agent. Further discussion on the differences between private and shared knowledge is provided in Sec.~\ref{sec: private public knowledge}.



\subsection{OWL as the language of representation of Believes}

Converting a set of agent's beliefs expressed as AgentSpeak predicates, as shown in Sec. \ref{sec:agentspeak}, to concepts and the other related elements of an ontology, the approach proposed in this paper recognises several mappings, detailed as follows. 

Concept classes, e.g. a class \mintinline{text}{Person} subclass of \mintinline{text}{Thing}, as shown in lines 1--3 of listing \ref{lst: init example turtle}, are translated to predicates with the functor of value \texttt{concept}, followed by the label of the concept rendered in the chosen language, as shown in line 1 of listing \ref{lst: init example agentspeak}. Concept subclasses are mapped into a combination of AgentSpeak predicates: one designating the concept as a class and the other having the functor value of \texttt{is\_a}, shown in lines 3--4 of listing \ref{lst: init example agentspeak}, which is interpreted as the first argument of this binary predicate being a subclass of the second argument, i.e. the \mintinline{text}{wizard} concept as being a subclass of the \mintinline{text}{person} concept. Ontology information defining this relationship is visible in lines 5--7 of listing \ref{lst: init example turtle}.

\begin{lstlisting}[caption={Examples of: the \texttt{Person} concept class, \texttt{Wizard} subclass of class \texttt{Person}, and two individuals of class \texttt{Wizard} expressed in turtle syntax}, label={lst: init example turtle}]
:person rdf:type owl:Class ;
        rdfs:subClassOf owl:Thing ;
        rdfs:label "person"@en-gb .

:wizard rdf:type owl:Class ;
        rdfs:subClassOf :person ;
        rdfs:label "wizard"@en-gb .

:is_friends_with rdf:type owl:ObjectProperty ;
          rdfs:domain :person ;
          rdfs:range :person ;
          rdfs:label "is friends with"@en-gb .

:has_name rdf:type owl:DatatypeProperty ;
          rdfs:domain :person ;
          rdfs:range xsd:string ;
          rdfs:label "has name"@en-gb .

:radagast rdf:type owl:NamedIndividual , 
           :wizard ;
         :has_name "Radagast the Brown" ;
         rdfs:label "Radagast"@en-gb .

:gandalf rdf:type owl:NamedIndividual , 
          :wizard ;
         :has_name "Gandalf the Grey" ;
         :is_friends_with :radagast ;
         rdfs:label "Gandalf the Grey"@en-gb .
\end{lstlisting}



\begin{lstlisting}[caption={Examples of: the \texttt{Person} concept class, \texttt{Wizard} subclass of class \texttt{Person}, and two individuals of class \texttt{Wizard} expressed in AgentSpeak}, label={lst: init example agentspeak}]
concept(person).

concept(wizard).
is_a(wizard, person).

object_property(person, is_friends_with, person).

data_property(person, has_name, str).

individual(radagast, wizard).
relation(radagast, has_name, "Radagast the Brown").

individual(gandalf_the_grey, wizard).
relation(gandalf_the_grey, has_name, "Gandalf the Grey").
relation(gandalf_the_grey, is_friends_with, radagast).
\end{lstlisting}


Object and data property definitions from an ontology are mapped to AgentSpeak predicates with the functors of \texttt{object\_property} or \texttt{data\_property} used for object or data properties, respectively. For example, an object property designating a \texttt{Person} individual as a friend of a \texttt{Person} individual, with the label of \texttt{is friends with}, as shown in lines 9--12 of listing \ref{lst: init example turtle}, is mapped to a ternary AgentSpeak predicate that should be interpreted as the first argument being the domain of the property, the second the label of the property, and the last argument the range of the given property, as shown in line 6 of listing \ref{lst: init example agentspeak}.

Individuals of an ontology, e.g. individual \enquote{Gandalf the Grey} of class \texttt{Wizard}, as defined in lines 24--28 of listing \ref{lst: init example turtle}, are translated to AgentSpeak expressions of binary predicates with the functor of value \texttt{individual}, where the first argument is a label of the individual, followed by the second argument, which is the individual's type, as shown in line 10 of listing \ref{lst: init example agentspeak}. Due to the constraints of AgentSpeak, all the identifiers are rendered using lowercase letters and with \texttt{\_} symbols instead of spaces. Specific properties that an individual is a part of, i.e. the individual properties where the given individual is the subject of the property, are mapped to ternary predicates where the first argument is the individual that is the subject of the property, the second argument is the reference label of the property, and the third argument is the object of the given property, i.e. a value or a reference to the related individual, for data or object properties respectively, as shown in lines 13 and 14 of listing \ref{lst: init example agentspeak}.

\begin{table}
    \centering
    \caption{Mapping of the key ontology concepts to AgentSpeak predicates of various arity}
    \begin{tabular}{p{0.35\linewidth}p{0.26\linewidth}l}
        \multirow{2}{*}{\textbf{Ontology}} & \multicolumn{2}{c}{\textbf{AgentSpeak}} \\
        & predicate arity & functor \\\hline
        Class & unary & \texttt{concept}\\
        Subclass relationship & binary & \texttt{is\_a} \\
        Object property & ternary & \texttt{object\_property} \\
        Data property & ternary & \texttt{data\_property} \\
        Individual & binary & \texttt{individual} \\
        Property instance & ternary & \texttt{relation} \\
    \end{tabular}
    \label{tab: ontology agentspeak mapping}
\end{table}

Using the described mapping relationships, the implemented BOWLDI translator can translate OWL entities to AgentSpeak expressions and vice versa while keeping the contents consistent, i.e., pointing out errors if they exist. The Python-implemented translator class can be used on input files or input strings.

Since the BOWLDI translator class was implemented with the intention of being modular, some of the containing methods are implemented with modularity, and the \enquote{pure function} idea in mind.



\section{Private Knowledge vs. Public Knowledge}\label{sec: private public knowledge}

As stated at the beginning of Sec. \ref{sec:agentspeak}, a common shared vocabulary can be used to, e.g. standardise interaction, provide a shared conceptual model that all the involved parties understand, and set up a relevant source in case arbitrage is necessary. This section focuses on the second of the listed benefits of having a common shared vocabulary in the form of an ontology. Furthermore, this observation is provided on the implementation level of an individual agent instead of observing the applicable MAS as a whole.

In the context of this paper, as mentioned in Sec. \ref{sec: Integrating OWL as Believes language}, public knowledge of an agent is considered the knowledge contained in the data sources of the agent, in particular in the agent's personal knowledge model, i.e. an ontology or a set of AgentSpeak expressions. Using the terms in Fig. \ref{fig: knowledge}, public knowledge is a subset of the agent's beliefs. Moreover, public knowledge of an agent is the subset of its knowledge that it is ready to share with other agents of the environment and that is freely accessible to those other agents.

Since public knowledge of an agent may be accessed, browsed, and retrieved by other agents of the common MAS, it must be based on a common shared conceptualisation, i.e. on a model featuring the key concepts that are relevant to all of the agents of the observed MAS or at least to all the agents the observed agent can interact with. Therefore, as shown in Fig. \ref{fig: knowledge}, public knowledge contains individuals pertaining to the concepts found in the common, shared ontology. Definitions of such individuals' concepts and their respective properties are found in the common, shared ontology.

On the other hand, the private knowledge of an agent is, in the context of this paper, considered to be the subset of its individual knowledge wherein the featured concepts may be related to the common, shared ontology but are more probable to feature concepts, properties, and individuals that cannot be found in the common, shared ontology, but may be related to them. In other words, an agent's private knowledge may feature concepts unknown to other agents that may or may not be explicitly related to the concepts of the common, shared ontology or that are inherently individual to the observed agent. 

The above distinction between private and public knowledge implies that an agent's individual knowledge may be selectively shared with other agents of the observed MAS. Furthermore, a subset of the given agent's knowledge may exist that is, by default, understandable only to the observed agent because it is unrelated to the concepts of the shared, common ontology. Although private knowledge may not be explicitly related to the common ontology, the agent could decide to share it with others.

In terms of implementation, an agent's public knowledge is presented here as a collection of individuals in an ontology that imports the shared ontology of the observed system. Instead of public knowledge featuring only definitions of individuals, an agent's private knowledge may include individuals based on the concepts found in the common ontology. However, it may extend such a set of concepts with definitions of additional concepts and their respective properties and individuals.

The example presented in Sec. \ref{sec: Example} illustrates the distinction and nature of an agent's private and public knowledge.



\section{Example}\label{sec: Example}

This section contains several use-case examples to exemplify how the implemented solution works and how it might be used in different scenarios. Although it is suggested to follow these examples in the provided order, that is not strictly necessary, as each can be considered self-contained and self-sufficient. All the described examples are available in open source\footnote{Available on \href{https://github.com/AILab-FOI/MAGO/tree/main/Documents/250400\%20BOWLDI\%20paper}{GitHub}}.



\subsection{Converting AgentSpeak Expressions to OWL}\label{sec: example asl - owl w/o sources}

The initial use case considers BDI agents, with some beliefs expressed using AgentSpeak. Listing \ref{lst: example one agentspeak} shows that the provided example's set of AgentSpeak expressions consists of the definitions of two concepts, one individual, and two examples of object and data property, one each. The string containing those definitions, i.e. their AgentSpeak expressions, is provided to the implemented converter as a value of the argument \texttt{input\_data}. These input data could have been stored in a local file, whereupon the path to the observed file would be provided as a value of the \texttt{input\_data\_path} argument, as shown in listing \ref{lst: example one input file}. It should be noted that \texttt{input\_data} takes precedence.

\begin{lstlisting}[caption={A string comprising a set of AgentSpeak expressions is provided as input to the implemented converter}, label={lst: example one agentspeak}]
from bowldi import BOWLDIConverter

input_data = """
// Concepts and Hierarchies
concept(person).
concept(wizard).
is_a(wizard, person).

// Individual
individual(gandalf, wizard).

// Object Property
object_property(person, is_friend_with, person).

// Data Property
data_property(person, has_name, str)."""

converter = BOWLDIConverter(
    input_data=input_data,
)

converter.get_response()
\end{lstlisting}


\begin{lstlisting}[caption={A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}, label={lst: example one input file}]
from bowldi import BOWLDIConverter

converter = BOWLDIConverter(
    input_data_path="input.asl",
)

converter.get_response()
\end{lstlisting}


\begin{lstlisting}[caption={A file path containing a set of AgentSpeak expressions is provided as input to the implemented converter}, label={lst: example one output file}]
<owl:Class rdf:about="#wizard">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">wizard</rdfs:label>
  <rdfs:isDefinedBy rdf:datatype="http://www.w3.org/2001/XMLSchema#string">self
  </rdfs:isDefinedBy>
</owl:Class>
\end{lstlisting}


The result of the conversion process is an OWL file of RDF/XML syntax, with the default name of \texttt{output.owl}, a part of which is shown in listing \ref{lst: example one output file}. Should the developer wish to customise the name of the created output file, it is possible to do so using the \texttt{output\_data\_path} argument. One may notice the addition of the instance of the property \texttt{rdfs:isDefinedBy} in listing \ref{lst: example one output file}. This results from none of the expressions in the input set of beliefs having any \texttt{source} values set.

The two concepts are converted to two hierarchical concepts, the individual to the individual of the related concept, and the properties are converted to their respective and appropriate entities in the ontology.



\subsection{Converting OWL to AgentSpeak Expressions}\label{sec: example owl - asl w/o sources}

\begin{lstlisting}[caption={A file path containing the ontology to be converted is provided as input to the implemented converter}, label={lst: example two input file}]
from bowldi import BOWLDIConverter

converter = BOWLDIConverter(
    input_data_path="output.owl",
)

converter.get_response()
\end{lstlisting}


\begin{figure}
    \centering
    \includegraphics[width=\linewidth]{shared ontology example.pdf}
    \caption{Visual representation of the concepts of the example shared ontology}
    \label{fig: shared ontology example}
\end{figure}

The output of the previous example is used as input data for this example, which is presented here to showcase the use of the implemented converter in converting an ontology file to a set of coherent AgentSpeak expressions. In contrast to AgentSpeak input, the ontology input is always expected to be a file, i.e. a path to the local file containing the ontology to be used as input for the converter. As in the previous example, the argument \texttt{input\_data\_path} takes the path to the file containing the input data, as shown in listing \ref{lst: example two input file}.

The ontology is converted into a file containing the same information as the string used as input in listing \ref{lst: example one agentspeak}. One may notice the addition of the argument \texttt{source} and its values, which are all \enquote{self.} The latter results from all the converted entities (concepts, properties, and individuals) being defined in the observed ontology, i.e. not using any external data sources, such as a shared ontology. In other words, all the entities are created within the loaded ontology, not using any elements of public knowledge.

With the exception of the \texttt{source} argument, the information contained in the input string of listing \ref{lst: example one agentspeak}, the input ontology of this example, and the resulting output file are the same.



\subsection{Converting OWL with External Sources to AgentSpeak}\label{sec: example owl - asl w/ sources}

Since one of the main features of ontologies is that they are to be shared, imported, and extended, it is imperative to build a tool that may work with ontologies that extend other ontologies. Therefore, the implemented converter can take input ontologies that import other ontologies and convert them to a set of AgentSpeak expressions. The main goal of this approach is to implement the ability to distinguish between the shared or public knowledge available to multiple agents and the private knowledge accessible only to an agent individual. More on this distinction is provided in Sec. \ref{sec: private public knowledge}.

The ontology extended by the ontology used as input for the conversion process is a simple one, featuring only a small but sufficient number of entities. The concepts and properties of this shared ontology\footnote{Available on \href{https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250400\%20BOWLDI\%20paper/lotr_example.owl}{GitHub}} example are inspired by The Lord of the Rings novel trilogy \cite{tolkien2007LordRings}. The featured entities, visualised in Fig. \ref{fig: shared ontology example}, include several concepts, including \texttt{human}, \texttt{wizard}, and \texttt{person}, five object properties, including \texttt{is\_friend\_with} and \texttt{has\_king}, and two data properties, \texttt{description} and \texttt{has\_name}. This ontology is imported by the example ontology used as the input file for the conversion process. Therefore, the example ontology contains three individuals that pertain to their respective concepts of the shared ontology.

The ontology\footnote{Available on \href{https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250400\%20BOWLDI\%20paper/onto_example.rdf}{GitHub}} importing the shared ontology contains three individuals: \texttt{Gandalf the Grey}, \texttt{Aragorn, son of Arathorn}, and \texttt{Legolas}, of the concepts \texttt{wizard}, \texttt{human}, and \texttt{elf}, respectively. Therefore, without further modifications, the knowledge defined within the example ontology is considered private, while the knowledge formalised within the imported ontology is considered public.

Public knowledge of the example ontology is discerned from the private knowledge contained within using the IRIs (Internationalized Resource Identifier) of the observed entities. Should the IRI of the ontology coincide with the IRI of the entity, the entity is considered private (e.g. the individual in the excerpt in listing \ref{lst: example three input}). Otherwise, the entity is considered imported and sourced from the imported ontology, which its IRI can identify. Therefore, the \texttt{source} argument of the output file containing the set of AgentSpeak expressions contains the IRI of the imported shared ontology for every entity that was not defined in the input ontology, i.e. for all the entities except the three individuals. The special exceptions here are already defined sources in the ontology. Some assertions may have a \texttt{isDefinedBy} annotation property attached, which is translated into AgentSpeak as a value of the \texttt{source} argument, as shown in listing \ref{lst: example three output}.

Running the conversion process of this use case, as shown in the listing \ref{lst: example three}, is exactly the same as in the previous example; see listing \ref{lst: example two input file} for comparison.

\begin{lstlisting}[caption={A file path containing the ontology to be converted is provided as input to the implemented converter, and the output file path is specified}, label={lst: example three}]
from bowldi import BOWLDIConverter

converter = BOWLDIConverter(
    input_data_path="onto_example.rdf",
    output_data_path="converted.asl"
)

converter.get_response()
\end{lstlisting}


\begin{lstlisting}[caption={Elements of the input ontology in the example presented in Sec. \ref{sec: example owl - asl w/ sources}; \texttt{<ontology IRI>}, \texttt{<aragorn IRI>}, and \texttt{<gandalf IRI>} are placeholders for the full values of ontology, Aragorn individual and Gandalf individual URIs, respectively, used here for improved legibility}, label={lst: example three input}]
<?xml version="1.0"?>
<rdf:RDF xmlns="<ontology IRI>/onto_example.rdf"
    xml:base="<ontology IRI>/onto_example.rdf"
    ...
    xmlns:lotr="<ontology IRI>/lotr_example.owl#"
    xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#">
    <owl:Ontology rdf:about="<ontology IRI>/onto_example.rdf">
        <owl:imports rdf:resource="<ontology IRI>/lotr_example.owl"/>
    </owl:Ontology>

    ...

    <owl:NamedIndividual rdf:about="<ontology IRI>/onto_example.rdf#<aragorn IRI>">
        <rdf:type rdf:resource="<ontology IRI>/lotr_example.owl#human"/>
        <lotr:is_friend_with rdf:resource="<ontology IRI>/onto_example.rdf#<gandalf IRI>"/>
        <lotr:has_name>Aragorn, son of Arathorn</lotr:has_name>
        <rdfs:label xml:lang="en-gb">Aragorn, son of Arathorn</rdfs:label>
    </owl:NamedIndividual>
</rdf:RDF>
\end{lstlisting}


\begin{lstlisting}[caption={The output set of AgentSpeak expressions based on the input ontology in the example presented in Sec. \ref{sec: example owl - asl w/ sources}; \texttt{<ontology IRI>} is a placeholder for the full IRI of the subject ontology, used here for improved legibility}, label={lst: example three output}]
concept(wizard)[source("<ontology IRI>/lotr_example.owl")].
is_a(wizard, person)[source("<ontology IRI>/lotr_example.owl")].
concept(human)[source("<ontology IRI>/lotr_example.owl")].
is_a(human, person)[source("<ontology IRI>/lotr_example.owl")].
concept(elf)[source("<ontology IRI>/lotr_example.owl")].
is_a(elf, person)[source("<ontology IRI>/lotr_example.owl")].
concept(kingdom)[source("<ontology IRI>/lotr_example.owl")].
concept(person)[source("<ontology IRI>/lotr_example.owl")].
concept(ring_of_power)[source("<ontology IRI>/lotr_example.owl")].
concept(dwarf)[source("<ontology IRI>/lotr_example.owl")].
is_a(dwarf, person)[source("<ontology IRI>/lotr_example.owl")].
concept(hobbit)[source("<ontology IRI>/lotr_example.owl")].
is_a(hobbit, person)[source("<ontology IRI>/lotr_example.owl")].
object_property(person, is_friend_with, person)[source("<ontology IRI>/lotr_example.owl")].
object_property(kingdom, has_king, person)[source("<ontology IRI>/lotr_example.owl")].
object_property(person, is_in_team_with, person)[source("<ontology IRI>/lotr_example.owl")].
object_property(person, has_ring, ring_of_power)[source("<ontology IRI>/lotr_example.owl")].
object_property(person, fights_against, person)[source("<ontology IRI>/lotr_example.owl")].
data_property(person, has_name, str)[source("<ontology IRI>/lotr_example.owl")].
data_property(ring_of_power, description, str)[source("<ontology IRI>/lotr_example.owl")].
individual(gandalf_the_grey, wizard)[source("self")].
individual(aragorn_son_of_arathorn, human)[source("self")].
individual(legolas, elf)[source("self")].
relation(gandalf_the_grey, has_name, "Gandalf the Grey")[source("Bogdan")].
relation(gandalf_the_grey, has_name, "Mithrandir")[source("self")].
relation(aragorn_son_of_arathorn, has_name, "Aragorn, son of Arathorn")[source("self")].
relation(aragorn_son_of_arathorn, is_friend_with, gandalf_the_grey)[source("self")].
relation(aragorn_son_of_arathorn, is_friend_with, legolas)[source("self")].
relation(legolas, has_name, "Legolas Greenleaf")[source("self")].
\end{lstlisting}




\subsection{Converting AgentSpeak with External Sources to OWL}\label{sec: example asl - owl w/ sources}

Finally, when the input file contains a set of AgentSpeak expressions that contain URLs in their \texttt{source} argument, and if those URLs are the addresses of ontologies, it is possible to convert them into ontologies that import specified extended ontologies. These imported ontologies are then searched for the associated concepts, and the output ontology does not feature their redefinition. Implementing this use case follows the same previous approach, as visible in listing \ref{lst: example four}.

The \texttt{get\_response} method is a way to check the functioning of the converter. This method should return a JSON object with several key-value pairs, one being \texttt{status}. Following a successful conversion, the value of this key is \texttt{success}, and the path to the output file can be found within the response as well. Otherwise, it is \texttt{error}. Should the conversion process terminate in an error, the accompanying error message is added to the response.

\begin{lstlisting}[caption={A file path containing the AgentSpeak expressions to be converted is provided as input to the implemented converter, and the output file path is specified}, label={lst: example four}]
from bowldi import BOWLDIConverter

converter = BOWLDIConverter(
    input_data_path="converted.asl",
    output_data_path="ontology.owl"
)

converter.get_response()
\end{lstlisting}



\subsection{SPADE\_BDI Example}\label{sec: example bdi agent}

All the above examples have been developed using AgentSpeak as agents' language, but no agent platform is needed or used for them. This subsection presents an example where agents are SPADE\_BDI \cite{palanca2023flexible}. 

SPADE \cite{palanca2020spade} is an agent platform based on XMPP\footnote{https://xmpp.org/} communication, where agents are developed in Python using a behaviours-based model. One extension of SPADE is SPADE\_BDI, where one of the possible behaviours controlling the execution of the agent may be a BDI-based behaviour implemented in AgentSpeak language.

Every SPADE\_BDI agent must start with a reference file that contains the agent's initial set of beliefs expressed as a collection of AgentSpeak expressions. For the sake of this example, agent Alice is given an empty file with no contents whatsoever (referenced in line 8 of listing \ref{lst: example five ontology to beliefs}). Additionally, every SPADE agent must be given a Jabber ID (JID) and the accompanying password that it will use to connect to an XMPP server at the designated address. Agents implemented in listings \ref{lst: example five ontology to beliefs} and \ref{lst: example five beliefs to ontology} use usernames \texttt{alice} and \texttt{carme} respectively. They connect to the XMPP server running locally on \texttt{localhost} and use the same password \texttt{password}. 

Once run, Alice reads the \texttt{onto\_example.rdf} file used in earlier examples and converts it to AgentSpeak belief expressions (lines 13--14 of listing \ref{lst: example five ontology to beliefs}). More than simply converting the ontology contents to a string containing all the relevant data, as shown in earlier examples, Alice uses BOWLDI to add all the beliefs contained within the ontology directly to her BDI agent. Thus, line 16 of listing \ref{lst: example five ontology to beliefs} prints out all the beliefs that were converted from the ontology. Should these data be printed, it would look exactly the same as listing \ref{lst: example three output}.

\begin{lstlisting}[caption={Implemented SPADE\_BDI agent that uses \texttt{BOWLDIConverter} to convert information contained in an ontology file to a set of beliefs}, label={lst: example five ontology to beliefs}]
import asyncio
import spade
from spade_bdi.bdi import BDIAgent
from bowldi import BOWLDIConverter


async def main():
    a = BDIAgent("alice@localhost", "password", "empty.asl")

    await a.start()
    await asyncio.sleep(1)

    converter = BOWLDIConverter(input_data_path="onto_example.rdf",)
    converter.add_beliefs_to_agent(a.bdi_agent)

    a.bdi_agent.dump()

    await asyncio.sleep(1)
    await a.stop()


if __name__ == "__main__":
    spade.run(main())
\end{lstlisting}


Analogous to the examples shown earlier, BOWLDI can be used to translate a set of beliefs of an agent to an ontology. Listing \ref{lst: example five beliefs to ontology} contains the implementation of such an agent. Agent Carme loads a set of beliefs stored in the \texttt{output.asl} file, thus loading the same set of beliefs shown in listing \ref{lst: example three output}. Once the beliefs are loaded, Carme uses BOWLDI to gather all the beliefs of her BDI agent and uses them as input data, utilising the same approach to input data shown in the first example presented in Sec. \ref{sec: example asl - owl w/o sources}.

\begin{lstlisting}[caption={Implemented SPADE\_BDI agent that uses \texttt{BOWLDIConverter} to convert a set of beliefs to an ontology file}, label={lst: example five beliefs to ontology}]
import asyncio
import spade
from spade_bdi.bdi import BDIAgent
from bowldi import BOWLDIConverter


async def main():
    a = BDIAgent("carme@localhost", "password", "output.asl")

    await a.start()
    await asyncio.sleep(1)

    converter = BOWLDIConverter()
    converter.convert_agent_beliefs_to_ontology(
        a.bdi_agent, "onto_carme.owl")

    await asyncio.sleep(1)
    await a.stop()


if __name__ == "__main__":
    spade.run(main())
\end{lstlisting}




\section{Conclusion}\label{sec:conclusion}

Ontologies have played an essential role in the communication within distributed systems since their early attempts, such as the development of Ontolingua \cite{farquhar1997ontolingua}—a language for creating ontologies—and the efforts to standardize agent ontologies, as seen in FIPA-SL \cite{fipa000032000fipa}. Despite these pioneering efforts, many approaches have not progressed significantly, mainly due to usability challenges. This issue serves as the motivation for the work presented in this paper.

As discussed, a good practice when designing a multi-agent system is to decide early on which ontology will define the common vocabulary used for communication among agents. This paper proposes the use of an OWL ontology as the shared public vocabulary. With BOWLDI, agents can easily share this vocabulary, allowing them to define their own public knowledge based on the concepts within the shared ontology.

BOWLDI not only facilitates the integration of various ontologies into agents' beliefs but also allows agents to distinguish between public knowledge (shared with other agents) and private knowledge (retained for internal reasoning). This distinction enhances knowledge management and supports more effective interactions in distributed environments.

While BOWLDI was designed to integrate an ontology-based knowledge model with AgentSpeak agents, it represents the first step towards full integration. Future developments aim to allow OWL to be directly incorporated into AgentSpeak, eliminating the need for translation between belief representations. Nevertheless, the current approach is fast and practical for real-world applications, offering a robust solution for integrating semantic knowledge in multi-agent systems.

%%%\additioncommment{Why is this good, and what are the benefits and problems?}

\section*{Acknowledgements}
This work is partially supported by Generalitat Valenciana CIPROM/2021/077, Grant PID2021-123673OB-C31 funded by MCIN/AEI/ 10.13039/501100011033 and by ``ERDF A way of making Europe'', MOBODL-2023-08-5618 funded by the European Union and the Croatian Science Foundation and ``Funding for open access charge: CRUE-Universitat Politècnica de València''.






\bibliographystyle{elsarticle-num} 
\bibliography{refs}


\end{document}
