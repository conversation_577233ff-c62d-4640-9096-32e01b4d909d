%% 
%% Copyright 2007-2020 Elsevier Ltd
%% 
%% This file is part of the 'Elsarticle Bundle'.
%% ---------------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.2 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.2 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'Elsarticle Bundle' is
%% given in the file `manifest.txt'.
%% 

%% Template article for Elsevier's document class `elsarticle'
%% with numbered style bibliographic references
%% SP 2008/03/01
%%
%% 
%%
%% $Id: elsarticle-template-num.tex 190 2020-11-23 11:12:32Z rishi $
%%
%%
\documentclass[preprint,12pt]{elsarticle}

\usepackage{xcolor} %%% For \textcolor
\usepackage{todonotes}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{tabularx}
%\usepackage[showframe]{geometry}% http://ctan.org/pkg/geometry
%\usepackage{lipsum}% http://ctan.org/pkg/lipsum
\usepackage{graphicx}% http://ctan.org/pkg/graphicx
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{booktabs}

%%% This command decides whether to insert a space or not,
\usepackage{xspace} 

%%% Review Papeer annotations
\usepackage[commentmarkup=uwave]{changes}
\definechangesauthor[name=mrebollo, color=red]{MR}
%%%
%   Useful Commands:
%     - \added[id=<id>, comment=<comment>]{<new text>}
%     - \deleted[id=<id>, comment=<comment>]{<old text>}
%     - \replaced[id=<id>, comment=<comment>]{<new text>}{<old text>}
%     - \highlight[id=<id>, comment=<comment>]{<text>}
%     - \comment[id=<id>]{<comment>}
%     - Remove markup: \usepackage[final]{changes}
%%%





\newcommand{\eps}{\varepsilon}
\newcommand{\xit}{x_i^{t}}
\newcommand{\xitp}{x_i^{t+1}}
\newcommand{\xitm}{x_i^{t-1}}
\newcommand{\xitmm}{x_i^{t-2}}
\newcommand{\xik}{x_i^{k}}
\newcommand{\xjt}{x_j^{t}}
\newcommand{\xjtp}{x_j^{t+1}}
\newcommand{\xjtm}{x_j^{t-1}}
\newcommand{\xjtmm}{x_j^{t-2}}
\newcommand{\xjk}{x_j^{k}}
\newcommand{\xkt}{x_k^{t}}
\newcommand{\xktp}{x_k^{t+1}}
\newcommand{\sumj}{\sum_{a_j\in N_i}}
\newcommand{\sumk}{\sum_{k=0}^{t}}
\newcommand{\eitp}{\eps_i^{t+1}}
\newcommand{\eit}{\eps_i^{t}}
\newcommand{\ejt}{\eps_j^{t}}
\newcommand{\Ae}{\Delta_{\eps_i}}
\newcommand{\xiz}{x_i^{0}}
\newcommand{\mean}[1]{\langle #1 \rangle}
\newcommand{\col}{\textsc{Co-L}\xspace}
\newcommand{\acol}{\textsc{ACo-L}\xspace}

%% Use the option review to obtain double line spacing
%% \documentclass[authoryear,preprint,review,12pt]{elsarticle}

%% Use the options 1p,twocolumn; 3p; 3p,twocolumn; 5p; or 5p,twocolumn
%% for a journal layout:
%% \documentclass[final,1p,times]{elsarticle}
%% \documentclass[final,1p,times,twocolumn]{elsarticle}
%% \documentclass[final,3p,times]{elsarticle}
%% \documentclass[final,3p,times,twocolumn]{elsarticle}
%% \documentclass[final,5p,times]{elsarticle}
%% \documentclass[final,5p,times,twocolumn]{elsarticle}

%% For including figures, graphicx.sty has been loaded in
%% elsarticle.cls. If you prefer to use the old commands
%% please give \usepackage{epsfig}

%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsthm package provides extended theorem environments
%% \usepackage{amsthm}

%% The lineno packages adds line numbers. Start line numbering with
%% \begin{linenumbers}, end it with \end{linenumbers}. Or switch it on
%% for the whole article with \linenumbers.
%% \usepackage{lineno}

\journal{Nuclear Physics B}

\begin{document}

\begin{frontmatter}

%% Title, authors and addresses

%% use the tnoteref command within \title for footnotes;
%% use the tnotetext command for theassociated footnote;
%% use the fnref command within \author or \address for footnotes;
%% use the fntext command for theassociated footnote;
%% use the corref command within \author for corresponding author footnotes;
%% use the cortext command for theassociated footnote;
%% use the ead command for the email address,
%% and the form \ead[url] for the home page:
%% \title{Title\tnoteref{label1}}
%% \tnotetext[label1]{}
%% \author{Name\corref{cor1}\fnref{label2}}
%% \ead{email address}
%% \ead[url]{home page}
%% \fntext[label2]{}
%% \cortext[cor1]{}
%% \affiliation{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%% \fntext[label3]{}

\title{Asynchronous Consensus for Multi-Agent Systems and its Application to Federated Learning. VERSION RECHAZADA}

%% use optional labels to link authors explicitly to addresses:
%% \author[label1,label2]{}
%% \affiliation[label1]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}
%%
%% \affiliation[label2]{organization={},
%%             addressline={},
%%             city={},
%%             postcode={},
%%             state={},
%%             country={}}

\author{Carlos Carrascosa$^{1}$, Aaron Pico$^{1, 3}$, Miro-Manuel Matagne$^{1}$, Miguel Rebollo$^{1}$ and J.A Rincon$^{2}$}



%\affiliation{organization={Valencian Institute for Artificial Intelligence\\
%  Universitat Politècnica de València},%Department and Organization
%            addressline={Camino de Vera, s/n}, 
%            city={Valencia},
%            postcode={46022}, 
%            state={Valencia},
%            country={Spain}}

\address{%
$^{1}$ \quad Valencian Research Institute for Artificial Intelligence. Universitat Politècnica de València, Camí de Vera s/n, 46022 Valencia, Spain;  \{<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>\}

$^{2}$ \quad Departamento de Digitalización, Escuela Politécnica Superior, Universidad de Burgos, Miranda de Ebro, Spain; \{<EMAIL>\}

$^{3}$ \quad Valencian Graduate School and Research Network of Artificial Intelligence, Universitat Politècnica de
València, Camí de Vera s/n, 46022 Valencia, Spain
}
 


\begin{abstract}

Federated Learning (FL) improves the performance of the training phase of machine learning procedures by distributing the model training to a set of clients and recombining the final models in a server. All clients share the same model, each with a subset of the complete dataset, addressing size issues or privacy concerns. However, having a central server generates a bottleneck and weakens the failure tolerance in truly distributed environments.

This work follows the line of applying consensus for FL as a no-centralized approach. Moreover, the paper presents a fully distributed consensus in MAS modeling and a new asynchronous consensus in MAS. The paper also includes some descriptions and tests for implementing such learning algorithms in an actual agent platform, along with simulation results obtained in a case study about electrical production in Australian wind farms.


\end{abstract}

%%Graphical abstract
%\begin{graphicalabstract}
%\includegraphics{grabs}
%\end{graphicalabstract}

%%Research highlights
%\begin{highlights}
%\item Research highlight 1
%\item Research highlight 2
%\end{highlights}

\begin{keyword}
%% keywords here, in the form: keyword \sep keyword

%% PACS codes here, in the form: \PACS code \sep code

%% MSC codes here, in the form: \MSC code \sep code
%% or \MSC[2008] code \sep code (2000 is the default)

Machine Learning \sep Federated Learning \sep Complex Networks \sep Consensus

\end{keyword}

\end{frontmatter}

\section{Introduction}

The FL algorithm proposed by Google \cite{brendan2016communication} has supposed to be an interesting improvement in the distributed machine learning area. This algorithm aims to profit from having many devices training the same model while preserving the privacy of samples used for this training. All devices share their trained models with a server that averages them and sends them back to the devices that use this averaged model instead of their local one to go on training. 

Adapting this algorithm to a multi-agent system (MAS) is intuitive \cite{FLaMAS2022}. Still, in this original approach, it does not make a profit from being a distributed system, as it is a centralized one. For this reason, one possible line of evolution of this algorithm is to make this model averaging by a truly distributed approach that takes profit of the advantages of using a MAS. In this line, the consensus in MAS has been applied \cite{carrascosa2022co}. When applying this approach, which will be called \emph{Consensus-based Learning (\col)} in the rest of the paper, it can be noticed that although it has the expected advantages of being a distributed process (such as fault tolerance or scalability), it has some delay issues related to its intrinsic synchronous nature. Agents must wait for the update of all their neighbors' models before calculating a new model update and sending it back to all their neighbors. 
%%%
%%%
%%%
\added{This makes not only the agent to have to wait for the slowest of his neighbors but also to send and receive a lot of big messages, something that should be minimized overall when in IoT environments. With this ideas in mind, a}
%%%
%%%
%%%
\deleted{A} new asynchronous consensus for MAS process has been devised and applied to the FL algorithm. 

This paper presents this new asynchronous consensus for MAS, generalizing first the Consensus in MAS definition to be fully distributed. It has been applied to the FL algorithm in a new Asynchronous Consensus-based Learning (\acol) algorithm. After that, \replaced{the implementation of \acol  in SPADE agents \cite{Palanca2020_SPADE} is detailed followed by some experiments with synthetic models that allow to test such implementation. After that, a bigger simulation case study to compare \acol with a traditional ML approach is presented in}{
some implementation tests using SPADE agents\cite{Palanca2020_SPADE} are presented along with} a case study where it has modeled electrical production in wind farms in Australia. The paper finishes with some conclusions and comments about future work.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{State of the Art}
\label{sec:state}

According to the work presented by Olfati-Saber \cite{olfati2007}, let us model a MAS by an undirected graph $G=(A, E)$. The set $A = \{a_i\}$ are the vertex of the graph, each one representing an agent in the MAS.  $E \subseteq A \times A$ represents the set of graph edges, so that $(a_i, a_j) \in E$ if agent $a_i$ and agent $a_j$ know each other. According to this graph, the consensus process is addressed by Equation \ref{eq:cons}. 

\begin{equation}\label{eq:cons}
\xitp = \xit + \eps \sumj (\xjt - \xit)
\end{equation}

Where $\xit$ is the value of agent $a_i$ at time $t$, $\eps$ is the \emph{learning factor} ($\eps \in [0, 1]$) and $N_i = \{a_j / (a_i, a_j) \in E\}$, the set of all agents neighbors of agent $a_i$.

Consensus is usually applied to the distributed control processes in the area of MAS. Ensuring and accelerating the convergence of the consensus is one of the main concerns when new algorithms appear, mainly when applied to high-order dynamic models.
 Amirkhani and Barshooi \cite{Amirkhani22} examine several variations of the consensus-related problems, including random-network consensus, leader-follower consensus, finite-time consensus, and group consensus/cluster consensus, among others. Finally, they reviewed the most prevalent applications in the MAS, such as rendezvous, formation control, and sensor networks.

FL was primarily defined by Google employees Kone{\v{c}}n{\'y} et al. \cite{Jakub16} as a way to reduce the uplink communication cost when training data is distributed over a large set of clients. This approach was improved by Yang et al. \cite{Yang18}, and finally, some security concerns were included %%%\cite{Bonawitz16} 
\cite{Bonawitz17}.

\added{There is much work being developed nowadays in FL. Some of this work is related to mixing blockchain with FL (as can be observed in the survey \cite{chhetri2023survey} and in \cite{wang2023}). There is also a line of work related to different approaches to clustering the participants in the FL (as in \cite{zhao2023reinforcement, zeng2023stochastic, rebollo2023gtg}), or even improving the aggregation of FL models as in \cite{wu2022}. Lately, there has been work related to evolving the centralized FL approach into a decentralized one, as in \cite{gao2023, PANIGRAHI202324, georgatos2023}. Another line of interest is related to the application of FL to IoT devices as is analyzed in \cite{nguyen2021federated}. Also, there is work in applying model optimization technologies to FL as in \cite{dai2023}. 
}





In \replaced{\cite{kairouz2021advances, baresi2023}}{\cite{kairouz2021advances}}, the leading open issues and the main lines of research about FL are analyzed. Some of these lines are related to improving communication.
%%%
%%%
%%%
\added{More specifically, according to them, current open challenges in FL are:}
\added{
\begin{enumerate}
    \item Ensuring data privacy while training machine learning models on distributed devices.
    \item Developing efficient algorithms for model aggregation and updating.
    \item Complex coordination mechanisms are required to handle many devices connected to a potentially unstable network.
    \item Reducing network overhead, as the partial models trained on each device need to be shared.
    \item Addressing the heterogeneity of devices and data, as different devices may have varying computational capabilities and data distributions.
\end{enumerate}
}

\added{This paper presents a new Asynchronous Consensus in MAS algorithm, and how this algorithm can be applied to Distributed Federated Learning (\acol), dealing with open challenges 3, 4 and 5:}

\added{
\begin{itemize}
    \item (3): Consensus algorithms in general (and asynchronous consensus in particular) allow to adapt to dynamic changes in the network given by agents entering and going out of the process, as can be produced by unstable network problems.
    \item (4): Asynchronous consensus algorithm allows to reduce the number of messages as at each iteration an agent only shares its model with one of its neighbors.
    \item (5): Asynchronous consensus, as its name claims, allows to have an asynchronous synchronization process of the agents sharing their models, so the heterogeneity of the different devices in the system does not affect to the whole system as in synchronous processes such as centralized FL or even Consensus-based FL, where the slowest agent marks the duration of all agents iterations.
\end{itemize}
}
%%%
%%%
%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%



\section{Asynchronous Consensus in MAS}
\label{sec:async-cons}

Applying the consensus process to an entirely distributed environment with independent agents using Equation~\ref{eq:cons} has some limitations. First, the learning step $\varepsilon$ is bounded by the maximum degree of the network, which is a global parameter, and only an observer with complete knowledge of the environment can calculate it. In the second place, all agents synchronize their epochs to ensure they are in the same iteration. In this section, we propose a generalization on the consensus process to address these problems. 

\subsection{\added{Consensus Process with} Dynamic Calculation of $\varepsilon$ } \label{subsec:eps}

The learning factor $\eps$ used for reaching a consensus between a set of agents must fulfill the following: 

\begin{equation}\label{eq:eps}
\eps \leq \min_{a_i} \frac{1}{d_i}
\end{equation}

\noindent where $d_i$ is the degree of agent $a_i$ (the number of neighbors or agents known by agent $a_i$). The main idea behind consensus in MAS is that it can be executed with only local knowledge, with agents only knowing their data and who their neighbors are, and without any other global knowledge about the underlying network's size or structure. Therefore, agents should obtain the $\eps$ parameter along the consensus process.

Let us assume that in $t=0$, $\eps_i^{0} = 1/d_i, \forall a_i \in A$. At each consensus step, $\eit$ will be updated as follows:

\begin{equation}
\eitp = \min (\eit, \min_{a_j \in N_i} \eps_j^{t}) \label{eq:mineps}
\end{equation}

Eventually, if all agents belong to the same connected component, $\eit = \min_{a_i} 1 / d_i, \forall a_i \in A$. However, the final consensus value $\xit \neq \mean{\xiz}$, and we need to introduce a term in the equation to correct the deviation produced while the system obtains the correct $\eps$ value.

Let us consider the $\eps_i$ learning factor from the $a_i$ agent's point of view. From Equation~\ref{eq:cons}, if we substitute $\xit$ by its calculation from iteration $t-1$, then by iteration $t-2$, and so on until iteration $t = 0$, we obtain

\begin{align}\label{eq:conszero}
\xit &= \xitm + \eps_i \sumj  (\xjtm - \xitm) = \nonumber \\
&= \xitmm + \eps_i \sumj(\xjtmm - \xitmm) + \eps_i \sumj  (\xjtm - \xitm) = \nonumber \\
&= \xitmm + \eps_i\sum_{k=0}^{1} \sumj(\xjk - \xik) = \nonumber \\
&= \xiz + \eps_i\sumk\sumj(\xjk - \xik)
\end{align}

%%MRP: este desarrollo no incluye t en el valor de eps: solo es asumir que tiene un valor local eps_i, distinto de eps, para ver cual es el término de la corrección. Una vez que se calcula, se generaliza a un valor cambiante en cada iteración.

\noindent and reorganizing the terms

\begin{equation}\label{eq:consstep}
	\frac{\xit - \xiz}{\eps_i} = \sumk\sumj(\xjk - \xik)
\end{equation}

Any other value $\eps_i = 1/d_i \geq \eps$ , then it can be defined $\Ae = \eps_i - \eps$ as the amount $\eps_i$ is greater than $\eps$. Therefore, $\eps = \eps_i - \Ae$ and, substituting in Equation~\ref{eq:conszero}


\begin{align}
\xitp &= \xiz + (\eps_i - \Ae)\sumk\sumj(\xjk - \xik) = \nonumber \\ 
	&= \xiz + \eps_i\sumk\sumj(\xjk - \xik) - \Ae\sumk\sumj(\xjk - \xik) = \nonumber \\ 
	&= \xit + \eps_i\sumj(\xjt - \xit ) - \Ae\sumk\sumj(\xjk - \xik)
\end{align}

\noindent and, by Equation~\ref{eq:consstep} 

\begin{align}\label{eq:correps_notime}
\xitp &= \underbrace{\xit + \eps_i\sumj(\xjt - \xit )}_{\textit{consensus in t+1}} - \underbrace{\Ae\frac{\xit - \xiz}{\eps_i}}_{\textit{correction}} = \nonumber \\ 
&= \xit + \eps_i\sumj(\xjt - \xit ) - \frac{\eps_i - \eps}{\eps_i}(\xit - \xiz) = \nonumber \\ 
&= \xit + \eps_i\sumj(\xjt - \xit ) - \left(1 - \frac{\eps}{\eps_i}\right)(\xit - \xiz)
\end{align}

The quotient $\eps / \eps_i$ corrects the deviation between the initial local value of $\eps_i$ for agent $a_i$ and the actual global value $\eps$ for the complete network. If we consider that the local value approaches in each iteration to the global one, \added{that is,  $\lim_{t \to \infty} \eit = \eps$, we need to correct the consensus value step by step.} We can rewrite the term that includes in $\xitp$ that lag introduced by an inaccurate $\eit$ value as
\added{
\begin{equation}\label{eq:correps}
\xitp = \xit + \eps_i\sumj(\xjt - \xit ) -\left( 1 - \frac{\eitp}{\eit}\right)(\xit-\xiz)
\end{equation}
}

When $\eit = \eitp$, the quotient tends to one, canceling the correction term and leaving the original consensus expression.


\subsection{Asynchronous Consensus}

The second limitation of the consensus algorithm is the need to synchronize all agents to be in the same iteration. This effect will lead to delays as an agent must wait for all his neighbors to send their values to calculate his new value, pass to the next iteration, and update them to all agents simultaneously.

A new asynchronous version of the consensus algorithm can be reached if agents interchange their values by pairs. Without losing any generality, it can be assumed that an agent randomly selects one of his neighbors, interchanging their current values and updating each one following Equation \ref{eq:cons}, adapted to $|N_i| = 1$.     


\begin{equation}\label{eq:async}
\xitp = \xit + \eps (\xjt - \xit) = (1 - \eps)\xit + \eps \xjt
\end{equation}

\added{Using this approach, it is not necessary that an agent waits for all their neighbors to perform a consensus step. Nevertheless, each agent chooses one of their neighbors to exchange their values and continue to the next step. As a result, it can occur that different agents were at different steps. This is not a problem, since once the consensus is reached, agents can continue with their execution without affecting to the agreed value. Figure 1 depicts an example of such an asynchronous process.}

\begin{proof}
\added{Olfati-Saber and Murray \cite{olfati2007} demonstrates that sum conservation and double stochastic Perron matrix is a necessary and sufficient condition for the consensus process to converge to the average of the initial values. Therefore, } we support the demonstration in the sum conservation, which is a property of the consensus process ruled by Equation~\ref{eq:cons}.

\begin{equation*}
\sum_{a_i} \xiz = \sum_{a_i} \xit \qquad \forall t \geq 0
\end{equation*}


Let be $a_i$ and $a_j$ the agents that exchange their values in iteration $t+1$.

\begin{equation*}
\sum \xitp = \xitp + \xjtp + \sum_{a_k\neq a_i,a_j}\xktp
\end{equation*}

\noindent Applying Equation~\ref{eq:cons}:

\begin{align*}
\sum \xitp & = (1 - \eps)\xit + \eps \xjt + (1 - \eps)\xjt + \eps \xit + \sum_{a_k\neq a_i,a_j}\xkt = \\ 
& = (1 - \eps + \eps)\xit + \eps \xjt + (\eps + 1 - \eps)\xjt + \eps \xit + \sum_{a_k\neq a_i,a_j}\xkt = \\
& =  \xit + \xjt + \sum_{a_k\neq a_i,a_j}\xkt = \sum \xit
\end{align*}
\end{proof}
Therefore, we can ensure that the asynchronous consensus process converges to the average of the initial values $\mean{\xiz}$ as long as $\eps \leq \frac{1}{\max d_i}$.



\begin{figure}[!tbh]
\begin{centering}
\includegraphics[width=0.48\textwidth]{Images/syn.eps}
\includegraphics[width=0.48\textwidth]{Images/dyn.eps}
\includegraphics[width=0.48\linewidth]{Images/asyn.eps}
\includegraphics[width=0.48\textwidth]{Images/asdyn.eps}
\end{centering}
\caption{Consensus evolution in a network with four agents with each approach (synchronous, dynamic $\eps$, asynchronous consensus, and asynchronous and dynamic $\eps$ combined). Initially, $x_0 = \{0.2, 0.4, 0.6, 0.8\}$, so $\mean{x^0} = 0.5$. The correct learning factor is available in the second iteration for the dynamic consensus, and its effect barely appears in the plot.}\label{fig:fourcons}
\end{figure}

%%%\comment[id=MR]{Rev 3. Comm 1. Consider providing diagrams or examples to illustrate the workings of the asynchronous consensus algorithm.}
%%%\comment[id=MR]{Es justo la figura 1. Creo que solo hay que responderle y revisar el texto para que la referencia y laexplicación estén claras (creo que lo están). Quizá forzar a que la figura salga antes.}


\subsection{Full-Distributed Consensus}
\label{sub_sec:full_distributed_consensus}

The complete consensus process combines the asynchronous process with the dynamic calculation of the learning factor $\eps$. \added{In the first step (Equation~\ref{eq:finaleps}), the value of $\eit$ is updated considering the value of the neighbor $a_j$ selected for the exchange. This is the special case of Equation \ref{eq:mineps} adapted for a unique neighbor. T¡Furthermore, from Equations \ref{eq:correps} and \ref{eq:async} we obtain Equation~\ref{eq:finalcons}, which rules the asynchronous consensus process}. Once the learning factor is changed, each agent updates its value, introducing the correction whenever $\eps_i^t$ and $\eps_j^t$ have changed.


\begin{eqnarray}
\eitp & = & \min (\eit,  \ejt )\label{eq:finaleps} \\
\xitp & = & (1 - \eitp)\xit + \eitp \xjt - \left( 1 - \frac{\eitp}{\eit}\right)(\xit-\xiz)\label{eq:finalcons}
\end{eqnarray}

	Figure~\ref{fig:fourcons} depicts an example of the evolution of consensus with each approach over a simple synthetic network with four agents and initial values $x_0 = \{0.2, 0.4, 0.6, 0.8\}$. The convergence value is $\mean{x^0} = 0.5$. The synchronous version results from applying the Olfaty-Saber and Murray solution of Equation~\ref{eq:cons}. The dynamic consensus shows a similar behavior because the learning factor is adjusted correctly in the second iteration, slightly deviating from the synchronous version. The asynchronous algorithm needs a more extended period to converge. Nevertheless, many couples of agents can exchange their values in parallel and reduce the overall performance. Finally, the combined version shows that the assumptions are correct, and the complete algorithm converges and arrives at the mean of the initial values.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Asynchronous Consensus Learning Algorithm \acol}
\label{sec:ACo-L}

This section extends the Consensus Learning (\col) algorithm, proposed in \cite{carrascosa2022co}, with the extension that includes the complete asynchronous behavior.

Like in any consensus algorithm, the starting point is an undirected graph $G=(A, E)$ as defined at the beginning of Section \ref{sec:state}, which models the agents in the system along with the communication topology. All the agents in the system execute Algorithm \ref{alg:algorithm}, implement the same Neural Network (NN), formed by the same layers and neurons, to learn a global model $(W, tr)$. $W$ denotes a set of weights for a training dataset $tr$. All the agents will share this model by communicating the set of weights $W$. 

Each agent $a_i$ will keep its own local NN $(W_i, tr_i)$ while approximating to the global model, being $W_i$, a set of weights and biases for each layer of the NN. \added{The NNs are exchanged with their direct neighbors, who are the only agents who could know the original values. From this point, agents exchange aggregated values that are impossible (or at least very complicated without extra information) to uncouple. That's why we speak about maintaining information as local. On the other hand, the training dataset is kept private, and the proprietary agent is the only one who knows their content.}

\begin{equation}\label{eq:matrices}
W_i = (W_{i,1},\ldots,W_{i,k})
\end{equation}

\noindent where $W_{i,j} \in \mathbb{R}^{n,m}$ represents the weights (or the bias) learned by agent $a_i$ for the layer $j$ of its NN. 


\begin{algorithm}[!tbh]
\caption{\acol Algorithm for agent $a_i$}
\label{alg:algorithm}
\begin{algorithmic}[1] %[1] enables line numbers
\WHILE{!doomsday }
\STATE $W\gets Train(f)$

\FOR{$t\gets 1, c$}
    \STATE $X_j = RANDOM\_SELECT(N_i)$

    \FOR{$j\gets 1, k$}
        \STATE $X_i^t\gets W_j$
        \STATE $X_i^{t+1}\gets (1 - \varepsilon) X_i^t + \varepsilon X_j^t$
        \STATE $W_j\gets X_i^{t+1}$
    \ENDFOR

\ENDFOR

\ENDWHILE
\end{algorithmic}
\end{algorithm}


The Asynchronous Consensus-based Learning algorithm (\emph{\acol}) is the process an agent $a_i$ follows to participate in an FL solution using an asynchronous consensus process. Therefore, they share the model being learned with their local neighbors and make a consensus on such a model based on Equation \ref{eq:async}. This model is formed by the weights matrices resulting from the training of the learning process (Equation \ref{eq:matrices}). This consensual model is then used for each agent in the next training. 

So, first, an agent $a_i$ following the \emph{\acol} algorithm will make \emph{e} epochs of training the algorithm. The result of this training is the set of \emph{k} matrices at Equation \ref{eq:matrices}, and for each one of them, the next \emph{c} iterations of the \acol, following the Equation \ref{eq:async} are made, leading to \emph{k} new matrices that will be used in the training process again. 





%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{\acol{} Implementation with SPADE Agents}

Agents are implemented using SPADE \cite{Palanca2020_SPADE}, being capable of communicating with other agents and applying the two variations of consensus learning algorithms commented above: synchronous, with \col{}, and asynchronous, with \acol{}. The agents communicate and exchange their models following a predefined network graph, which represents the constraints imposed that define the agent's neighborhood.

\subsection{Agent architecture}


SPADE agents were developed as agents interacting with other agents and humans through the XMPP protocol\footnote{https://xmpp.org/}. They are programmed to specify the set of behaviors they execute.
The agents built into this implementation contain three behaviors :

\begin{itemize}
    \item A Presence Behavior allows agents to notify their neighbors when they connect or disconnect.
    \item A Message Receiving Behavior allows agents to be ready to receive and react to messages constantly.
    \item A Finite-State Machine Behavior, which allows transitioning between the training, sending, and receiving states to follow the corresponding consensus learning scheme. 
\end{itemize}

\paragraph{Presence behavior} When an agent connects, this behavior automatically identifies and subscribes to its neighbors in the graph. If the neighbors are connected, they will receive a notification stating that this agent is trying to subscribe. When an agent receives a subscription request, accepts it, and subscribes back. All agents keep track of the active neighbors. 

Furthermore, as mentioned in Section \ref{subsec:eps}, the maximal degree of the network graph is an essential piece of data to apply the consensus algorithm. However, since the graph is dynamic, as agents can connect and disconnect at any time, this maximal order is also a dynamic value, and it needs updates every time a new agent connects or disconnects.


\paragraph{Message Reception} A second behavior focuses on communication. It changes considerably between \col{} and \acol{} versions of the consensus on execution. In the \col{}, the agent must wait for the update of the models of all his neighbors before updating it and sending it back. In the \acol{} version, the agent waits for a message from one of his neighbors, then applies the consensus using the model weights and biases in this message and responds to this agent with its weights. Notice that messages contain all the information needed for the consensus: not just the weights.

\paragraph{Finite-State Machine (FSM)} This  behavior controls the transition from one state to another based on certain conditions. The \acol{} version needs four different states to build the solution:

\begin{itemize}
    \item The \verb|SETUP| state: the initial state of the FSM can be used to perform operations before starting the other processes.
    \item The \verb|TRAINING| state: performs the training of the local model.
    \item The \verb|SEND| state: after training the local mode, the model's weights are sent randomly to one of the agent's neighbors.
    \item The \verb|RECEIVE| state: waits for a response to the message sent in the \verb|SEND| state and applies the Equation \ref{eq:async} when the weights of the other agent are received.
\end{itemize}


\begin{figure}[!bt]
\centering
    \includegraphics[width=12cm]{Images/async_consensus_queue.png}
    \caption{Application of consensus after the training process by storing the incoming messages in a queue}
    \label{fig:async_global_queue}
\end{figure}



\subsection{\acol{} Execution}
When an agent is executed, but none of his neighbors are connected, the agent trains its model locally until at least one of its neighbors connects.

Generally, when the agent has at least one active neighbor, the execution of the FSM is pretty straightforward: the agent trains its model locally for one epoch in the \verb|TRAINING| state, then switches to the \verb|SEND| state where it sends its model's weights to a random active neighbor, and finally waits in the \verb|RECEIVE| state for a response from the neighbor he sent the weights to, and applies the Asynchronous Consensus algorithm (Equation \ref{eq:async}) once it receives this neighbor's model's weights. 

This process implies that training and consensus are different moments. The agent will not receive any message from their neighbors asking for his weight throughout the training phase. Indeed, if such an event occurs, deciding which set of weight it has to exchange is a problem. If the consensus uses the pre-training weights of the agent, then the training process partially completed during the current round is lost. On the other hand, a model's training consists of changing the model's weights, so not considering these changes would represent a considerable waste of time and resources. 

The first solution to this problem was to interrupt the training phase if a message arrives from one of the agent's neighbors during the training phase, apply the consensus with the weights of the model before the training phase, replace the mid-training weights with the weights obtained by the consensus, and then resume the training. 
With this solution, the first part of the training (before the interruption) still needs some recovery. The weights obtained through the consensus replace the weights of the current training round. That provokes the modifications made to the weights to be lost in the process. Therefore, this part of the training proves useless. 

A second solution was therefore implemented where the training part is never interrupted. When a message arrives from a neighbor, the agent switches the value of a flag to 1 and stores the incoming message locally. At the end of the training phase, the agent systematically verifies the flag's value. If it is active, it applies the consensus on the model that results from this training phase, and then the flag resets to 0. This solution saves training time, improving the solution's efficiency.


There is, however, a slight twitch to be made to this solution, as we only considered the situation where one neighbor sends a message to the agent, whereas there could be multiple neighbors sending messages to the agent during the same training phase. Therefore, instead of a flag, a queue stores all the messages that arrived during the training phase. After the training round, agents recover all the stored messages and apply the asynchronous consensus algorithm individually. Figure \ref{fig:async_global_queue} illustrates the final solution adopted in this implementation.

\added{The rest of the paper presents, in the next section how this SPADE implementation of the \acol algorithm has been validated, and then a case study modeling the electrical production in Australian wind farms.}


\section{Implementation validation with Synthetic Models} %%%\section{Experimental Results with Synthetic Models}


\added{This section presents the experiments made to validate the implementation of ACoL. So, to validate the communication and coordination of SPADE agents using \acol, Experiment 1 with two agents was made. After that, this set execution was compared with having a single training agent in Experiment 2. This implementation validation finished with a comparison between \acol and \col algorithms implemented in SPADE agents in Experiment 3 with 7 SPADE agents.}


Datasets must be used to benchmark the performances to test the proposed solution's efficiency and assert that it works as desired. The dataset used throughout this work is the MNIST database\footnote{http://yann.lecun.com/exdb/mnist/} (Modified National Institute of Standards and Technology database). It is a comprehensive dataset of handwritten digits containing 60,000 training samples and 10,000 test samples. Researchers widely use the MNIST dataset to test various model performances; therefore, many accuracy benchmarks are available online. So it allows us to compare our results with the available benchmarks to validate some of the obtained results.

The images in the MNIST dataset are all black and white (meaning each image pixel can be encoded by a 0 or a 1) and have the same format (28 x 28 pixels). No preprocessing was applied to the samples in the dataset since this study aims to test and compare new AI model training techniques rather than to reach an optimal accuracy score for the MNIST dataset prediction. 

The principles of FL apply to any type of model as long as agents exchanges model information. The selection of the model is, therefore, not crucial in the elaboration of the solution presented in this study. This work uses mainly quite comprehensive models to clearly represent the results obtained and to illustrate more clearly the principles of \acol{}. The models used for testing the solution were Multilayer Perceptrons (MLP) with one hidden layer.

As the images' size in the MNIST dataset is 28 x 28 pixels, and the input layer consists of one neuron for each pixel in the image, 
the input layer of the MLP consists of 784 neurons. The goal of the model is to recognize handwritten digits; therefore, there are ten possible prediction outcomes, which means that the output layer must contain ten neutrons. The number of neurons in the hidden layer remains to be chosen. This parameter will be tuned throughout to evaluate its impact on the solution's efficiency. 

The hyperparameters related to the training of the MLP model are the following ones. Activation Function: ReLU, Learning Rate: 0.01, Optimizer: SGD, and Batch Size: 3.


Once the \acol{} setting was built, experiments had to be made to test if the solution works appropriately and then evaluate if this solution presents advantages compared to the other versions of \col. 

\subsection{Experiment 1: Evaluation of the solution}

First, the solution had to be tested to see if agents communicate satisfactorily and if the training process is carried on appropriately. Some logging systems were created to trace all sent messages, all training data after each round, all variations of the $\eps$ parameter, and all the weights of the local model after each change (training or consensus). 

\begin{figure}[!tbh]
\centering
    \includegraphics[width=6.75cm]{Images/consensus_acc.png}
    \includegraphics[width=6.75cm]{Images/cons_weights.png}
    \caption{Experiment 1: (left) Evolution of test accuracy  and (right) evolution of weights}
    \label{fig:cons_2agents_acc}
\end{figure}

The first tests were executed on a single machine, with two running agents connected in the network graph. The experiment was repeated ten times, where each agent ran for ten training rounds. The test accuracy was stored in the logs, and we could follow the evolution of the training process throughout the rounds. Figure \ref{fig:cons_2agents_acc} (left) shows the increase in test accuracy as iterations pass, indicating the training process is working correctly.


To verify that the consensus algorithm is adequately applied, another test checks if the weights of the models of the two agents are converging towards the same values. We simply analyze the logs containing the weights of the models of the agents and plot the evolution of the values of one of the weights (the same one) for both agents. The result is presented in Figure \ref{fig:cons_2agents_acc} (right). As we see, the weights of both agents converge throughout the training process, indicating that the consensus algorithm works as expected. 


\subsection{Experiment 2: Comparison with a single training agent}

A comparison was made to compare the \acol{} setting with one single client training the model locally. In the \acol{} setting, two agents were used. They kept exchanging their weights throughout the process and applied the consensus described previously. This setting, therefore, has twice more computational power as a single agent training a model locally. Still, a clear performance improvement could not be observed if the consensus algorithm was inefficient. The evolution of the test accuracy throughout ten training rounds for both situations was plotted and is represented in Figure \ref{fig:cons_vs_single_agent}. 

\begin{figure}[!tbh]
\centering
    \includegraphics[width=9cm]{Images/consensus_vs_single_agent_acc.png}
    \caption{Experiment 2: Evolution of Accuracy.}
    \label{fig:cons_vs_single_agent}
\end{figure}


Using the \acol{} setting and applying the consensus algorithm, the two agents present significantly higher test accuracy than the single agent training locally. It indicates that the consensus process seems efficient and allows multiple agents to combine their weights, improving global performance. 

\subsection{Experiment 3: Comparison between \acol{} and \col{}}

The main goal of the study is to determine if the \acol{} setting presents an advantage compared to the \col{} setting, with a synchronous execution. The \col{} setting had already been created and separately tested. The aim of including the \acol{} setting is to determine if it improves performance due to the absence of waiting times, which is considerable in the synchronous setting. 

\begin{figure}[H]
\centering
   \includegraphics[width=7cm]{Images/lab_test_network.png}
   \caption{Experiment 3: Graph for the network of agents.}
   \label{fig:network_graph}
\end{figure}

As mentioned previously, the performance of the \col{} setting highly depends on the topology of the network graph. The existence of small-world phenomena or power-law degree distributions decreases the number of iterations and accelerates convergence. Therefore, the performance regarding time is altered. The first experiments were conducted with the graph presented in Figure \ref{fig:network_graph}, which has a maximum degree of 3. Note that these tests were carried out with one agent on each machine. 

Seven agents were launched with synchronous and asynchronous settings (Figure \ref{fig:network_graph}). The training results after each round were stored in logs. Figure \ref{fig:comp_acc_rounds} (left) displays the results of the average accuracy evolution among the agents.

\begin{figure}[!tbh]
\centering
    \includegraphics[width=6.75cm]{Images/comp_acc_rounds.png}
    \includegraphics[width=6.75cm]{Images/comp_acc_time.png}
    \caption{Experiment 3: (left) Evolution of accuracy by rounds. (right) Evolution of accuracy by time}
    \label{fig:comp_acc_rounds}
\end{figure}

The performance of the synchronous version seems better throughout the five first rounds, then is more or less equal to the performance of the asynchronous version, as expected. At each round in the synchronous version, an agent considers the models of all of its neighbors. In contrast, the asynchronous version only applies the consensus algorithm with one of his randomly chosen neighbors. Therefore, the consensus performed in the synchronous version considers more training data and gives place to a more efficient model in terms of test accuracy. The convergence of the two versions after five rounds is due to the convergence of the model's training. The models are reaching the maximum accuracy they will be able to reach. 

However, the advantage of \acol{} lies in the fact that it does not present long waiting times because of its asynchronous character. It is, therefore, primordial to compare the performance of the synchronous and asynchronous versions regarding execution times to see if the gain of time of the asynchronous setting compensates for the more efficient training of the synchronous version at each round. The evolution of the average test accuracy among the seven agents concerning time for both asynchronous and synchronous versions are plotted in Figure \ref{fig:comp_acc_rounds} (right). 

After 50 seconds, the asynchronous version presents a higher test accuracy than the synchronous version throughout the execution. The fact that the synchronous version seems more efficient in the first 50 seconds can be explained by the fact that the start of the timing (second 0) corresponds to the end of the first training round (because before that moment, no test accuracy had been obtained yet). Therefore, as a round of training is more efficient in the synchronous setting than in the asynchronous setting, it is normal that the synchronous version presents a higher accuracy value at the beginning of the measurements. 

In conclusion, the \acol{} setting presents a lower efficiency than the synchronous version when comparing the performances, but it presents an advantage when comparing the accuracy. From a practical point of view, the objective when training an AI model is to train it fast and not limit the number of rounds, which makes \acol{} an efficient alternative. 


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Case: Electrical Production in Wind Farms}
\added{The aim of this section is to demonstrate the applicability of Federated Learning, enhanced by the proposed \acol, in addressing real-world problems while leveraging the training benefits already described in the previous sections.
For this reason, a real case study chosen for its suitability to this task is presented. This case study involves the prediction of power generation in Australia's wind farms network, for which data are available.
This is a relevant case for testing Federated Learning approaches. Unlike scenarios where a dataset is distributed among hypothetical nodes, this example is distinguished by each node (wind farm) having real, independently generated data.
}

%Finally, this section presents a study case for predicting energy generation in wind farms. 
\added{To approach the case study with our proposal}
each farm has an agent attached, containing a NN trained with the power generation along some period. Agents connect to the nearest neighbors within a determined radius, exchange the trained models and apply \acol{} to obtain the common model that integrates the readings all over the territory. 

The data used is public and belongs to the Australian Energy Market Operator (AEMO).\footnote{https://anero.id/energy/wind-energy} It is available the energy production data for 51 Australian wind farms for the year 2018, with the MW values produced at each farm captured at 5-minute intervals. The layout of these farms can be seen on the map in Figure \ref{fig:aemo_map}. The first ten months have been used as training data and the last two as test data.

\begin{figure}[!tbh]
\centering
    \includegraphics[width=0.475\textwidth]{Images/aemo_map.png}
    \includegraphics[width=0.475\textwidth]{Images/aemo_network.png}
    \caption{(Left) Location of AEMO wind farms. (Right) Random Generated Graph, where each node is an agent controlling the corresponding farm, linking agents within a radius of 450 km.}
    \label{fig:aemo_map}
\end{figure}


The different farms have been connected, shaping a randomly generated graph according to their actual positions on the map. There will be connections between farms within a radius of around 450 km. Isolated farms will be connected to the nearest farm independently on the distance, to avoid isolated farms without participating in the consensus process. The result of these connections can be seen in Figure \ref{fig:aemo_map} (right).

The NN model used is an Echo State Network (ESN). This model can achieve competitive performance with a higher training speed. The core of the ESD is a reservoir: a randomly generated network that remains fixed over time. Its structure is not a layered one but a graph. the backward links generate recurrent networks that integrate time in a natural way. This property also makes them suitable for temporal series analysis, such as the sequences of power generation available in the dataset. Since the only trainable part of the network is the output layer, it is not necessary sending the whole model over the network.

The model's input has 24 steps (each step is the average of values in one hour), and the output is a single value, the prediction of power production for the following hour. ESN implementation provided by TensorFlow Addons has been used with the default connectivity values, leaky, and spectral radius values. The parameters configuring the ESN are 100 neurons,  0.1 connectivity, a leaking rate of 1, and 0.9 as spectral radius.
 
Comparing \acol{} with traditional ML helps us appreciate the performance of the proposed solution. Two approaches are used, a global one, where a single model is trained with the data of all farms, and an individual one, where each farm maintains a local, private model trained only with the power values the farm has generated during the year. 

It is also compared to an aggregation strategy used in \cite{EnsembleESN} for a similar problem, which consists of using an ensemble of prediction models. The predicted value aggregates the output of all the models that form the ensemble. Specifically, the strategy used is Global Fusion, in which the different values are aggregated by applying an average. The ensemble comprises the individual models of the 51 wind power stations.

\added{Note that for the purpose of this section to demonstrate the advantages or differences in using the new federated and decentralized approach \acol , a controlled environment is essential, where all other factors remain constant. Consequently, we have reimplemented the strategies to use them with the same models and parameters.}

\begin{figure}[!tbh]
\centering
    \includegraphics[width=7cm]{Images/wind_farms_loss.png}
    \caption{Evolution of test loss for the three models }
    \label{fig:wind_farms_loss}
\end{figure}

The results shown have been achieved after 50 training epochs and are the average of these values for all farms (Table \ref{tab:table_loss_wind_farms}). The evolution of the loss throughout the training in Figure \ref{fig:wind_farms_loss} shows how \acol manages to reach better performance in fewer epochs.

\begin{table}[!bt]
\caption{MAE, RMSE, and MSE metrics of the different models}
\label{tab:table_loss_wind_farms}
\centering
\begin{tabular}{llll}
\toprule
\textbf{}    & \textbf{MAE}  & \textbf{RMSE}  & \textbf{MSE}    \\ 
\midrule
\acol        & \textbf{8.20} & \textbf{12.27} & \textbf{242.59} \\ 
Global Model & 9.69          & 14.33          & 367.9           \\ 
Single Model & 10.03         & 14.06          & 272.894         \\ 
Global Fusion Model     & 10.12         & 15.13          & 438.99          \\
\bottomrule
\end{tabular}

\end{table}

\begin{figure*}[!th]
  \includegraphics[width=\textwidth,height=4cm]{Images/predicitions.png}
 \caption{Prediction of test data (daily moving average) by \acol and GL models}
 \label{fig:prediction}
\end{figure*}


To check the model's performance trained with \acol{}, the test data (last two months) of one of the wind stations (ARWF1) is shown together with the data predicted by the model with this information. The prediction for the GL model is also displayed for comparison. Each value is predicted using the 24 steps of actual data previous to it. For better visualization, what is shown is the daily moving average of both. It is displayed in Figure \ref{fig:prediction}. We can see how \acol{} and GL captures the tendency of the daily data properly, and \acol{} is closer to the maximum peaks of the actual data (in blue). 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{Conclusions and Future Work}

This work proposes a truly distributed algorithm for FL using a consensus process to calculate the generalized weights and bias of a common NN model shared by a set of agents. \added{To summarize, the paper presents the following contributions:}

\begin{itemize}
    \item A theoretical model for the generalized consensus in MAS to be fully distributed, allowing the calculation of the $\varepsilon$ parameter dynamically between the participants.

    \item  The model also allows an asynchronous exchange of consensus values, where each agent evolves at its own speed. The asynchronous solution avoids idle times, waiting for the entire neighborhood to complete the consensus process and share the new values, improving the parallelization of the agent set.

    \item With these improvements to the consensus process, we have defined \acol{} algorithm as a consensus-based, FL algorithm that is fully distributed and reduces the synchronization time of an agent with his neighbors.

    \item The theoretical model has been implemented in the SPADE platform, and we have checked its performance with some experimental sets.
\end{itemize}


Finally, a simulated case execution based on actual data about Australian energy generation in wind farms. This simulation allows to model and compares results between \acol{} algorithm with a global machine learning model or with one machine learning process isolated for each agent. 

\comment{CONCLUSIONES sobre la bondad del \acol: menor número de mensajes, menos sobrecarga de la red, teniendo en cuenta los open issues: (3) coordinación en redes inestables (4) reducción del número de mensajes (5) heterogeneidad de dispositivos (velocidades).}

As a future work, \added{we are working on several directions:}

\begin{itemize}
    \item The deployment of SPADE agents with \acol{} in IoT devices for an edge execution.

    \item \added{Working in model updating to reduce the size and amount of messages needed in FL.}

    \item \added{Including different priorities in the agents so that models could be more important according to the criticality level of the agent.}
    
\end{itemize}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%% The acknowledgments section is defined using the "acks" environment
%%% (rather than an unnumbered section). The use of this environment 
%%% ensures the proper identification of the section in the article 
%%% metadata as well as the consistent spelling of the heading.


\section*{Acknowledgement}
This work has been developed thanks to the funding of projects:

\begin{itemize}
    \item Grant PID2021-123673OB-C31 funded by MCIN/AEI/ 10.13039/501100011033 and by “ERDF A way of making Europe”
    \item PROMETEO CIPROM/2021/077
    \item TED2021-131295B-C32
    \item Ayudas del Vicerrectorado de Investigacion de la UPV (PAID-PD-22)
\end{itemize}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%% The next two lines define, first, the bibliography style to be 
%%% applied, and, second, the bibliography file to be used.

\bibliographystyle{elsarticle-num} 
\bibliography{references}


\end{document}
\endinput
%%
%% End of file `elsarticle-template-num.tex'.
