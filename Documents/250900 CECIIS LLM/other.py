#!/usr/bin/env python3
"""
Universal chat CLI for OpenAI (ChatGPT) and Mistral (Le Chat).
Usage examples:

python main.py -s "You are a witty assistant." -u "Tell me a joke."
python main.py --provider mistral --model magistral-medium-latest \
               -s "You are concise." -u "Explain transformers."

Every call appends a JSON-L line to chat_log.jsonl.
"""
from __future__ import annotations
import argparse, datetime, json, os, sys
from pathlib import Path

try:
    import config  # local secrets / defaults
except ImportError:
    sys.exit("❌ Missing config.py with API keys.")

LOG_FILE = Path(__file__).with_name("chat_log.jsonl")

system_prompt = """
You are a merchant blacksmith in a role-playing video game. Your name is <PERSON><PERSON><PERSON>. You are a 42 year-old male.

Your personality is described using the following vector of facets of personality traits, as per the Five Factor Model of personality by <PERSON>cCrae and Costa. Each facet is described using a value [0, 1], where 0 is the lowest and 1 is the highest. The closer the value is to 1, the more the personality trait facet is expressed. The closer the value is to 0, the less the personality trait is expressed.

Facet order:
['Fantasy', 'Aesthetics', 'Feelings', 'Actions', 'Ideas', 'Values',
 'Competence', 'Order', 'Dutifulness', 'Achievement striving', 'Self-Discipline', 'Deliberation',
 'Warmth', 'Gregariousness', 'Assertiveness', 'Activity', 'Excitement seeking', 'Positive emotions',
 'Trust', 'Straightforwardness', 'Altruism', 'Compliance', 'Modesty', 'Tender-mindedness',
 'Anxiety', 'Angry hostility', 'Depression', 'Self-Consciousness', 'Impulsiveness', 'Vulnerability']

Personality vector:
array([0.25, 0.5, 0.25, 1.0, 0.75, 1.0, 1.0, 1.0, 0.75, 1.0, 1.0, 1.0, 0.25, 0.5, 1.0, 0.75, 0.5, 0.5, 0.25, 0.5, 0.0, 0.25, 0.0, 0.0, 0.5, 0.5, 0.25, 0.5, 0.25, 0.25])

Your task is to roleplay as this character. If the player asks for a service, you can only provide one at a time. Choose which one, based on your personality.

Provide the response as a JSON object containing two keys: "response" and "service". The "response" key should contain the text of your response to the player. The "service" key should contain the name of the service you are providing, if any. If you are not providing a service, the "service" key should contain the value "none".
"""

user_prompt = "Hello! You are a merchant, are you not? I would love to sell some weapons, upgrade my gear, or dismantle some of my items for raw material. What can you provide for me?"


# ---------- provider-specific clients --------------------------------------
def _openai_client():
    from openai import OpenAI  # lazy import; openai>=1.30

    return OpenAI(api_key=config.OPENAI_API_KEY or os.getenv("OPENAI_API_KEY"))


def _mistral_client():
    from mistralai import Mistral  # mistralai>=1.2

    return Mistral(api_key=config.MISTRAL_API_KEY or os.getenv("MISTRAL_API_KEY"))


# ---------- generic wrapper -------------------------------------------------
def chat(provider: str, model: str, messages: list[dict[str, str]]) -> str:
    if provider == "openai":
        client = _openai_client()
        resp = client.chat.completions.create(model=model, messages=messages)
        return resp.choices[0].message.content.strip()

    if provider == "mistral":
        client = _mistral_client()
        resp = client.chat.complete(
            model=model,
            messages=messages,
            response_format={
                "type": "json_object",
            },
        )
        return resp.choices[0].message.content

    raise ValueError(f"Unknown provider '{provider}'")


# ---------- driver ----------------------------------------------------------
def main() -> None:
    ap = argparse.ArgumentParser(description="Chat with OpenAI or Mistral.")
    ap.add_argument("-s", "--system", required=False, help="System prompt")
    ap.add_argument("-u", "--user", required=False, help="User prompt")
    ap.add_argument(
        "--provider",
        choices=("openai", "mistral"),
        default=getattr(config, "DEFAULT_PROVIDER", "mistral"),
        help="LLM provider to use",
    )
    ap.add_argument("--model", help="Model ID (else provider default)")
    args = ap.parse_args()

    provider = args.provider
    model = args.model or getattr(config, "DEFAULT_MODEL", {}).get(provider)

    if provider == "openai" and not (
        config.OPENAI_API_KEY or os.getenv("OPENAI_API_KEY")
    ):
        sys.exit("❌ OpenAI key missing (config.py or env).")
    if provider == "mistral" and not (
        config.MISTRAL_API_KEY or os.getenv("MISTRAL_API_KEY")
    ):
        sys.exit("❌ Mistral key missing (config.py or env).")

    msgs = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
    reply = chat(provider, model, msgs)

    # Console output
    print("\n🤖 Assistant:", reply, "\n")

    # Log all I/O
    LOG_FILE.open("a", encoding="utf-8").write(
        json.dumps(
            {
                "ts": datetime.datetime.now().isoformat() + "Z",
                "provider": provider,
                "model": model,
                "system": system_prompt,
                "user": user_prompt,
                "assistant": reply,
            },
            ensure_ascii=False,
        )
        + "\n"
    )


if __name__ == "__main__":
    main()
