\section[MAGO]{Developing a Framework for Agent Gamification Based on Ontologies (MAGO)}

\begin{frame}{\insertsection}
    The result of a cooperation between:
        \begin{itemize}
            \item University of Zagreb Faculty of Organization and Informatics (\alert{UNIZG FOI}) and
            \item Universitat Politècnica de València (\alert{UPV}), Valencian Research Institute for Artificial Intelligence (\alert{VRAIN}).
        \end{itemize}

    This cooperation is funded by the European Union and the Croatian Science Foundation.
\end{frame}

\begin{frame}{\insertsection}
    \customFigure[1]{% how wide is the figure?
        \input{Documents/241018 AI2Future Zagreb HR/Figures/research structure overview}
    }{The flow between the parts and the phases}{fig:research-quick-overview-flow}
\end{frame}



\subsection{MAGO-Ag Ontology}

\begin{frame}{\insertsubsection}
    \begin{itemize}
        \item An ontology comprising concepts applicable to \alert{implementing \acp{MAS} as \acp{IVE}}.

        \item The \alert{main goal} of the ontology is to enable the modelling of a multiagent system in terms of implementation possibilities.

        \item The ontology contains a selection of modified and enriched concepts of the MAMbO5 ontology, a result of earlier cooperation \cite{okresaduric2019MAMbO5NewOntology}.

        \begin{itemize}
            \item e.g. Agent, Behaviour, Action, Process, Objective, Artefact
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{\insertsubsection}
    \customFigure[1]{
        \includegraphics{Documents/241018 AI2Future Zagreb HR/Figures/onto.png}
    }{Visual relationship of the concepts of the MAGO-Ag ontology}{ontology concepts}
\end{frame}



\subsection{MAGO-Ag Framework}

\begin{frame}{\insertsubsection}
    \begin{itemize}
        \item The \alert{main objective} of the MAGO-Ag framework is to translate a \ac{MAS} modelled using the MAGO-Ag ontology into an \alert{implementation template} for a \ac{MAS} comprising SPADE agents.
    \end{itemize}
\end{frame}

\begin{frame}{\insertsubsection}
    \customFigure[0.6]{
        \includegraphics{Documents/241018 AI2Future Zagreb HR/Figures/Translation documents.png}
    }{Essential files of the translation process}{translation files}
\end{frame}

\begin{frame}{\insertsubsection}
    \customFigure[1]{
        \includegraphics{Documents/241018 AI2Future Zagreb HR/Figures/Behaviour Individuals.png}
    }{A selection of individuals modelling agent behaviour instance}{behaviour individuals}
\end{frame}

\begin{frame}[fragile]{\insertsubsection}
    \begin{listing}
        \begin{mintedPython}
class Navigate(FSMBehaviour):

    async def on_start(self) -> None:
        print("Starting behaviour.")

    async def on_end(self) -> None:
        print("Ending behaviour.")

    async def state_setup(self):
        self.add_state(name='Observe_environment', state=Observe_environment(), initial=True)
        self.add_state(name='Move_forwards', state=Move_forwards())
        self.add_state(name='Rotate', state=Rotate())
        self.add_transition(source='Observe_environment', dest='Rotate')
        self.add_transition(source='Rotate', dest='Move_forwards')
        self.add_transition(source='Move_forwards', dest='Observe_environment')        
        \end{mintedPython}
        \caption{Finite state machine behaviour implementation template with three state behaviours}
    \end{listing}
\end{frame}

\begin{frame}{\insertsubsection}
    \customFigure[0.6]{
        \includegraphics{Documents/241018 AI2Future Zagreb HR/Figures/Generated template files.png}
    }{Generated template files for the modelled system}{generated template files}
\end{frame}

\begin{frame}[fragile]{\insertsubsection}
    \begin{listing}
        \begin{mintedPython}
:~/$ python translate.py 
. . .
:~/$ python Template/World_world.py 
AgentAlice: New agent running.
AgentBravo: New agent running.
AgentClive: New agent running.
        \end{mintedPython}
        \caption{Running the translation script and the modelled system's generated implementation template}
    \end{listing}
\end{frame}

\begin{frame}{\insertsubsection}
    \customFigure[1]{
        \includegraphics{Documents/241018 AI2Future Zagreb HR/Figures/Agent available knowledge.png}
    }{Pieces of knowledge available to agents after template generation}{available agent knowledge}
\end{frame}