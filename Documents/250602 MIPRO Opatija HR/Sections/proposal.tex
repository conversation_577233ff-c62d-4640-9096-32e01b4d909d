% !TeX root = ../../../pres.tex

\section{The Proposed Ontology and Framework}

\begin{frame}{\insertsection}
    \begin{itemize}
        \item The proposed \alert{ontology MAGO-Ag} extends the MAMbO5 ontology, a result of earlier cooperation \cite{okresaduric2019MAMbO5NewOntology}, enriching it with concepts necessary for the framework to function.

        \item The \alert{main goal} of the ontology is to enable modelling of a multiagent system in terms of implementation possibilities.

        \item Several \alert{concepts and properties have been added or modified} to facilitate the use of the framework for transforming the modelled system into an implementation blueprint.

        \begin{itemize}
            \item e.g. Agent, Artefact, Role, Objective, System, etc.
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{\insertsection}
    \begin{itemize}
        \item The proposed \alert{framework MAGO-Ag} translates a multiagent system modelled using the MAGO-Ag ontology into an implementation template for a multiagent system comprising SPADE agents.

        \item The framework uses the \alert{ontology as input}, thus receiving the necessary description of a multiagent system. The output is an \alert{implementation template} that the system developer is expected \alert{to extend} with actionable code customised to the specific implementation needs of the modelled system.
    \end{itemize}
\end{frame}

\begin{frame}{\insertsection}
    \customFigure[1]{
        \input{Documents/250602 MIPRO Opatija HR/Figures/framework}
    }{Visual representation of the workings of the proposed MAGO-Ag frameworks}{framework}
\end{frame}



\subsection{Use-Case Example}

\begin{frame}{\insertsection: \insertsubsection}
    \begin{exampleblock}{Use-Case Example}
        This agricultural multiagent system coordinates farmers and drones to optimise crop management. Two Agent \alert{Farmer} individuals \alert{monitor soil conditions} using periodic checks and \alert{plan tasks} using a \alert{finite state machine} behaviour consisting of the following states: Assessing, Deciding, and Implementing. Two Agent Drone class individuals undertake aerial surveys via event-triggered one-shot behaviours and navigate fields through flight patterns controlled by a finite state machine behaviour comprising three states: Idle, Surveying, and Returning.
    \end{exampleblock}
\end{frame}

\begin{frame}{\insertsection: \insertsubsection}
    \customFigure[1]{
        \includegraphics[width=\linewidth]{Documents/250602 MIPRO Opatija HR/Figures/MAGO-Ag example individuals.png}
    }{A selection of individuals and their relationships in the example ontology describing the use-case example}{example}
\end{frame}

\begin{frame}{\insertsection: \insertsubsection}
    \customFigure[1]{
        \includegraphics[width=\linewidth]{Documents/250602 MIPRO Opatija HR/Figures/code example behaviour.png}
    }{Python code implementing a SPADE finite-state machine behaviour consisting of three interconnected states}{behaviour}
\end{frame}

\begin{frame}{\insertsection: \insertsubsection}
    \customFigure[1]{
        \includegraphics[width=\linewidth]{Documents/250602 MIPRO Opatija HR/Figures/code example agent.png}
    }{Python code instantiating a SPADE agent with data on the individual's URI, available knowledge artefacts, available roles and related behaviours, and the defined system features}{agent}
\end{frame}

\begin{frame}{\insertsection: \insertsubsection}
    \begin{itemize}
        \item The modelled system is translated into the implementation template consisting of the following four files:
        \begin{itemize}
            \item \mintedInline{Workspace_workspace.py} is the main entry point of the system, where all agents are instantiated and their initial beliefs are set.
            \item \mintedInline{Agent_Agent_Farmer.py} and \mintedInline{Agent_Agent_Drone.py} are where the agent classes for the two agent types of the system are implemented.
            \item \mintedInline{Behaviours.py} contains the implementations of the behaviours that the agents execute.
        \end{itemize}
    \end{itemize}
\end{frame}
