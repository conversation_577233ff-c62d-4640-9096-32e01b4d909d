% !TeX root = ../../../pres.tex

\section{Related Work}

\begin{frame}{\insertsection}
    In the domain of multiagent systems, ontologies have been used:
    \begin{itemize}
        \item as a medium of \alert{describing a domain} by instantiating individuals populating templates pertaining to the given domain \cite{okresaduric2019MAMbO5NewOntology,freitas2017ModelDrivenEngineeringMultiAgent};
        \item in a \alert{model verification} process using automated reasoners to verify and validate the built models \cite{skobelev2020OntologybasedOpenMultiagent,schmidt2015OntologyCollaborativeTasks,warden2010OntologyBasedMultiagentSimulations};
        \item for \alert{code generation} based on the defined templates \cite{freitas2015IntegratingOntologiesMultiAgent,freitas2017ModelDrivenEngineeringMultiAgent,aghdam2022NewOntologyBasedMultiAgent}.
    \end{itemize}
\end{frame}
