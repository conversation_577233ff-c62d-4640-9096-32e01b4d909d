- The summary is not informative enough and, in fact, misleading. The authors should clearly state what their proposal of an innovative framework involves: a multi-agent system concept, an ontology, more than one ontology, or all of these.
  - adapted accordingly

- Also, the authors should be clear if their framework has been implemented or not. What part of the system is work-in-progress?
  - the framework is implemented, removed the work-in-progress mention

- Terms “framework”, “system”, “approach” and “method” and used alternatively and indiscriminately. “The method” is a fundamental component of “the framework”, implemented as “a system”, and all this forms “an approach”. But these facts are not clear from the summary. This must be improved.
  - adapted accordingly

- Since the approach has not been implemented and verified, the authors should refrain from using categorical statements that imply that the system will necessarily show all benefits, such as : “speed up the design cycle, reduce coding errors, and enhance system extensibility”, etc. These statements *must* be conditional – once the system has been finished, implemented, tested, and verified, it is expected that these benefits will become apparent.
  - modified categorical statements into conditionals

- The list of keywords (multiagent system, ontology, framework) is insufficient and must be expanded. Also, it would be good to align keywords with the standard IEEE (or ACM) indexing terms.
  - added two more keywords and aligned them with the IEEE indexing terms.

- The introduction is far too long and heterogeneous. Subsections A - E must be moved to a new Section II describing the background theory. Only a short description of the state-of-the-art, the motivation for this research, and what has been accomplished should be stated in the introduction. This will greatly improve the readability of the paper, its structure and the logical flow. Of course, the roadmap paragraph should remain as the last paragraph in the introduction.
  - subsections I.A-E moved to Sec. II, roadmap left in Sec. I.

- The new Section II “Background theory” requires at least one additional visualization (e.g., figure, table) for explaining concepts such as Intelligent Virtual Environment and Agent Implementation Framework. The source (reference) of this visualization should be clearly stated in the text and description of figures and/or tables.
  - visualisation added

- There are duplicates in citations “[11], [12] [11], [12]”.
  - deleted

- The title of section III should be modified to be more specific. Instead of “The ontology” much better title would be “The MAGO-Ag ontology” or “The proposed MAGO-Ag ontology”.
  - modified as suggested

- The first sentence in the Section III is completely intelligible and must be rephrased as 3 or 4 separate sentences: “The ontology used in this research instance, MAGO-Ag, is importing and building on the MAMbO5 ontology presented in [8], detailing and extending it with concepts specifically intended to contain knowledge about the implementation of a multiagent system that is later used by the implementation template-generating framework.”. Use grammar NLP tools or ask native English speakers for help.
  - modified as suggested, into several shorter sentences

- Fig.1 and Fig.2. are pivotal for understanding the contribution of the paper. Fig.1. must be moved to page 3 in Section III where it is mentioned in the text and the ontology is being described.
  - Fig. 1 moved

- The description of Fig.2. must be expanded to make clear that it shows the Use Case Example (Section IV, subsection A) in the agricultural domain.
  - caption modified

- Similar to Section III, Section IV also requires a more specific name, such as “The MAGO-Ag framework” or “The proposed MAGO-Ag framework”, not just “The framework”.
  - modified as suggested

- On page 4 you state: “After running the framework, the modelled system is translated into a set of Python files containing its spread-out implementation template”.  Again, is this work in progress, or are you talking about already accomplished achievements? If indeed the authors have already implemented the framework, then it is necessary to visualize the architecture of the framework. A new figure showing the implemented IT system must be added in the revised version. However, if the system has not been physically implemented, then the authors must rephrase all statements accordingly. For example, in this case: “After running the framework, the modelled system *WILL BE* translated into a set of Python files containing its spread-out implementation template”, or: “IT IS ENVISIONED THAT after running the framework, the modelled system is translated into a set of Python files containing its spread-out implementation template.”
  - added figure

- One more point. The title is awkwardly phrased. A significantly better title would be: "A Framework for Ontology-Driven Multiagent System Generation".
  - modified title

1. You mention:
"Once the ontology contains all the necessary information to model the wanted multiagent system using the concepts provided in the MAGO-Ag ontology and the concept individuals provided by the system designer, it is possible to use the MAGO-Ag framework to translate the modelled system into an implementation template."

Then you also mention:
"After running the framework, the modelled system is translated into a set of Python files containing its spreadout implementation template."

Is then MAGO-Ag simply an ontology or a framework that can be run as a computer program? If the latter is the case, this needs to be clearly stated. Also, it was not described how the framework takes an ontology file and actually generates the "several Python files", presumably the ones as shown in Figures 3 and 4? Does it work with .owl or .rdf file format and which algorithm/method was used for processing the ontology? More technical details need to be provided, as the paper is focused on the technical aspect.
- described in more detail

2. Which code was used for generating the files, where can it be found? 
Also, where can MAGO-Ag ontology be found, as right now you only the SPADE system (the reference to SPADE should contain an URL to make it more accessible)?
- added links

3. "[11], [12] [11], [12]" -> Double referencing, please remove one pair
- deleted

4. Figure 2 caption needs to be modified as it is the same as the Figure 1 caption.
- updated