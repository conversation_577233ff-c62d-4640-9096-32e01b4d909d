\begin{tikzpicture}[
    agent/.style={rectangle, draw, rounded corners, minimum width=1.6cm, minimum height=1cm, inner sep=0.5em},
    env/.style={rectangle, draw, thick, dashed, inner sep=10pt},
    ive/.style={rectangle, draw, thick, inner sep=12pt},
    node distance=1.4cm and 1.5cm,
    arrow/.style={-Latex, thin},
    label/.style={font=\small\itshape}
]

\node [agent] (ew) {Empty workspace};
\node [agent, right=of ew, xshift=-1cm] (behavs) {Behaviours};
\node [agent, right=of behavs, xshift=-1cm] (agc) {Agent classes};
\node [agent, below right=of agc, yshift=1cm, xshift=-1cm] (kns) {Knowledge artefacts};
\node [agent, above=of kns, xshift=-0.5cm] (roles) {Roles};
\node [agent, above right=of kns, yshift=-1cm, xshift=-2cm] (fw) {Finalised workspace};

\node [agent, above=of ew, yshift=2cm, text width=2cm] (onto) {Ontology extending MAGO-Ag ontology};
\node [agent, below=of fw, yshift=-2cm, text width=3cm] (temp) {Implementation template};

\node [ive, fit={(ew) (behavs) (agc) (roles) (kns) (fw)}] (box) {};
\node [anchor=south east, label] (text1) at (box.south east) {MAGO-Ag framework};

\draw[arrow, bend left=42] (onto) to node[label, above right] {input} (box.north);
\draw[arrow, bend right=42] (box.south) to node[label, above right] {output} (temp);

\draw[arrow, bend right=10] (ew) to (behavs);
\draw[arrow, bend left=10] (behavs) to (agc);
\draw[arrow, bend left=10] (agc) to (roles);
\draw[arrow, bend right=10] (agc) to (kns);
\draw[arrow, bend left=10] (roles) to (fw);
\draw[arrow, bend right=10] (kns) to (fw);

\end{tikzpicture}