\begin{tikzpicture}[
    agent/.style={rectangle, draw, rounded corners, minimum width=1.6cm, minimum height=1cm},
    env/.style={rectangle, draw, thick, dashed, inner sep=10pt},
    ive/.style={rectangle, draw, thick, inner sep=12pt},
    node distance=1.4cm and 1.5cm,
    arrow/.style={-Latex, thin},
    label/.style={font=\tiny\itshape}
]

% Nodes inside the environment
\node[agent] (agent1) {Agent 1};
\node[agent, right=of agent1] (agent2) {Agent 2};
\node[agent, right=of agent2] (agent3) {Agent 3};

    \onslide<+->

% Agent environment box
\node[env, fit={(agent1) (agent2) (agent3)}, label=above:Agent environment] (envbox) {};
\node [anchor=south east, label] (text1) at (envbox.south east) {Agent environment};

    \onslide<+->

% Other IVE contents
\node[agent, below=of agent2] (agent4) {Agent 4};
\node[agent, right=of agent4] (human1) {Human};
\node[agent, left=of agent4] (art) {Artefact};
\draw[arrow, bend left=21] (agent4) to node[label, below] {use} (art);

% IVE box
\node[ive, fit={(envbox) (agent4) (human1) (art)}, label=above:Intelligent Virtual Environment] (ivebox) {};
\node [anchor=south east, label] (text1) at (ivebox.south east) {Intelligent Virtual Environment};

    \onslide<+->

% Outside nodes
\node[agent, below=of agent4] (onto1) {Ontology};
\node[agent, below=of art] (onto2) {Ontology};
\draw[arrow, bend right=21] (onto1.north) to node[label, left] {model} (ivebox.south);
\draw[arrow, bend right=42] (agent1) to node[label, right, pos=0.25] {knowledge base} (onto2);

    \onslide<+->

\node[agent, above=of agent2, xshift=2cm] (fram) {Agent implementation framework};
\draw[arrow, bend right=21] (fram) to node[label, below right, pos=0.4] {implements} (agent1.north);
\draw[arrow, bend right=30] (fram) to node[label, right, pos=0.3] {implements} (agent2);
\draw[arrow, bend left=15] (fram) to node[label, right, pos=0.3] {implements} (agent3.north);
\draw[arrow, bend left=21] (fram) to node[label, right, pos=0.9] {implements} (agent4);

\end{tikzpicture}