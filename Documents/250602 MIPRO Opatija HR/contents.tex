% !TeX root = ../../../pres.tex

\input{Documents/250602 MIPRO Opatija HR/Sections/introduction}

\input{Documents/250602 MIPRO Opatija HR/Sections/theory}

\input{Documents/250602 MIPRO Opatija HR/Sections/related work}

\input{Documents/250602 MIPRO Opatija HR/Sections/proposal}

\section{Conclusion}

\begin{frame}{\insertsection}
    \begin{itemize}
        \item The presented ontology-driven approach facilitates the transformation of a conceptual model into executable implementation templates.
        \item Further refinement is possible, as it should explore:
        \begin{itemize}
            \item incorporating additional agent development frameworks;
            \item exploring mechanisms of dynamic adaptation;
            \item integrating with large language models;
            \item exploring use in agent communication and migration between workspaces.
        \end{itemize}
    \end{itemize}
\end{frame}