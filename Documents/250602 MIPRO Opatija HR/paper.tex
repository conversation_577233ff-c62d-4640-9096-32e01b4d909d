\documentclass{MIPRO}

\usepackage{cite}
\usepackage{csquotes}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage[T1]{fontenc}
\usepackage{flushend}
\usepackage{multirow}
\usepackage{multicol}
\usepackage{array}
\usepackage{hyperref}
\usepackage{tikz}
\usetikzlibrary{positioning, arrows.meta, shapes.geometric, fit}

\begin{document}

\title{A Framework for Ontology-Driven Multiagent System Generation}

\author{
\IEEEauthorblockN{
B. Okreša <PERSON>\IEEEauthorrefmark{1},
<PERSON><PERSON>\IEEEauthorrefmark{2}
}

\IEEEauthorblockA{\IEEEauthorrefmark{1} %1st affiliation 
University of Zagreb Faculty of Organization and Informatics, Varaždin, Croatia}

\IEEEauthorblockA{\IEEEauthorrefmark{2} %2nd affiliation 
Departamento Sistemas Informáticos y Computación, Universitat Politècnica de València, Valencia, Spain}

<EMAIL>, carras<PERSON>@dsic.upv.es

\thanks{This publication is supported by the project MOBODL-2023-08-5618 funded by the European Union and the Croatian Science Foundation.}
}

\maketitle

\begin{abstract}
This paper presents a novel framework for developing multiagent systems using ontology-based modelling consisting of an ontology and an executable module to translate ontology to implementation templates. It systematically maps concepts and relationships to agent roles, behaviours, and interactions by capturing domain knowledge in an ontology. Furthermore, it automatically generates implementation templates for the modelled multiagent system. This way, the need for extensive manual coding may be somewhat reduced, ensuring that conceptual integrity is maintained throughout development. A case study showcases how the proposed framework may speed up the design cycle, reduce coding errors, and enhance system extensibility. Using ontology-driven generation ensures consistency and interoperability, as each agent artefact follows the established domain semantics. This approach may be especially beneficial in complex, knowledge-intensive environments that require collaborative and distributed solutions. Ultimately, it may lead to a flexible and scalable process from domain modelling to multiagent implementation. Our framework provides a way that might help bridge the gap between conceptual design and agent-based execution, streamlining multiagent system development and ensuring robust alignment with domain-specific ontologies.
\end{abstract}

\renewcommand\IEEEkeywordsname{Keywords}
\begin{IEEEkeywords}
\textit{multiagent system, ontology engineering, software frameworks, knowledge representation, artificial intelligence}
\end{IEEEkeywords}



\section{Introduction}\label{sec: Introduction}

In the general context, an agent can be defined simply as \enquote{something that acts.}
\cite[p. 21]{russell2022ArtificialIntelligenceModern} 
In the more specific context of information and communication sciences (ICT), and especially in the domain of artificial intelligence (AI), an agent is defined rather more specifically, but still rather abstractly as \enquote{anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.}
\cite[p. 54]{russell2022ArtificialIntelligenceModern}
Even more specifically and with an increased number of constraints, an agent can be defined as \enquote{a computer system that is situated in some environment and that is capable of autonomous action in this environment in order to achieve its delegated objectives.}
\cite[p. 4]{wooldridge2013IntelligentAgents}
Although explicated several years ago, these definitions hold even in the current surge of various frameworks for implementing agents and their tight integration with state-of-the-art large and small language models (LM).

This paper presents a framework that can be used to generate a SPADE implementation template or blueprint for a multiagent system modelled using concepts of the accompanying ontology pertaining to the concept of an IVE.

The rest of the paper is organised as follows. Some background theory is laid out in Sec. \ref{sec: Background Theory}. Related work is presented in Sec. \ref{sec: Related Work}. The ontology of the framework laid out in this paper is briefly described in Sec. \ref{sec: The Ontology}. Section \ref{sec: The Framework}. contains the introduction of the framework and its fundamental characteristics. Discussion on the ontology and the related framework and the conclusion are given in Sec. \ref{sec: Discussion and Conclusion}.



\section{Background Theory}\label{sec: Background Theory}

This section covers some of the fundamental concepts used in this paper. An abstracted set of these concepts and their relationships is shown in Fig. \ref{fig: background theory}.

\subsection{Multiagent System}

A system comprising an increased number of such agents is considered a multiagent system, although a clear designation of the minimum value of such a number is not clearly and decidedly stated in the available literature. It is precisely the environment comprising multiple agents where additional important features of agents may be observed. Communication possibilities, social interaction, and cooperative and competitive behaviour are some of the defining concepts of intelligent or autonomous artificial agents. Multiagent systems boast a rich set of application domains \cite{Schatten2017CECIISLSMASAD}, especially when combined with the related and similar concept of agent-based modelling. Since the definition of an agent, given here earlier, is very broad and allows for a multitude of applications, a wide variety of agent and multiagent system development frameworks exist nowadays or have been implemented in the recent past. A state-of-the-art approach to implementing agents provides us with a new and improved perspective and implementation framework almost every week. It is this agile environment that motivates this paper and the related research to investigate different ways that would enable us to express a model of a multiagent system in a formalised way that can easily be shared and understood yet expressive enough to allow for modelling a multiagent system, while not overburdening the system designer with an abundance of concepts.

\begin{figure}
    \centering
    \resizebox{\linewidth}{!}{
        \input{fig theory}
    }
    \caption{Abstract visualisation of the selection of the fundamental background theory concepts: artificial agents that may have an ontology as a knowledge base are located in an environment with other agents that may be found in combination with artefacts and humans in an intelligent virtual environment, which may be modelled using an ontology, whereas agent implementation frameworks may implement artificial agents}
    \label{fig: background theory}
\end{figure}


\subsection{Ontology}

An ontology is briefly described as an \enquote{explicit specification of a shared conceptualization.} \cite{guarino2009WhatOntology,Gruber1993,studer1998KnowledgeEngineeringPrinciples}. In other words, an ontology provides us with a formalised and explicitly stated set of concepts and their relationships, coupled with their meaning and definition, using some additional features based on the knowledge that is usually shared and widely acceptable. In addition, an ontology is meant to be shared and utilised by more than a single individual. In combination with multiagent systems, ontologies have been used as knowledge bases and sources of knowledge and reasoning mechanisms for individual agents \cite{hadzic2009OntologyBasedMultiAgentSystems}. Their defining feature of being shared and used by a number of individuals is used in behaviour instances similar to those found in humans – the shared ontology provides common concepts and vocabulary to individual agents, who build upon this common knowledge set by providing their own specialised concepts, concept individuals, properties, and related ideas that can be found in their environment.



\subsection{Agent Environment}

The environments agents can be located in are usually classified by observable trait pairs, representing two distinct extremes, that shape agent design and decision-making processes \cite[pp. 61-64]{russell2022ArtificialIntelligenceModern}, described as follows. 

In agent-environment interactions, observability is crucial. Fully observable environments allow agents to see all states, while partially observable ones limit their view, with complete unobservability as the extreme case. Single-agent environments remain simple and avoid social complexities. Multiagent systems present challenges such as cooperation and competition, which makes outcomes more complicated to predict. Environments that are deterministic offer predictable results and allow precise planning. Conversely, nondeterministic environments introduce uncertainty and force agents to adapt.

Based on the temporal structure of the environment, episodic environments separate tasks into distinct interactions that allow agents to reset strategies between different states. Sequential environments link actions over time and enable long-term planning. Static environments stay stable, enabling careful thought, whereas dynamic ones change independently and require quick decisions.

State representation divides environments into discrete and continuous types. Discrete systems have clearly defined finite states suitable for rule-based navigation. Environments that are continuous change constantly and can often be described using mathematical models. Finally, known environments provide precise action-outcome mappings, allowing agents to focus on efficiency. Unknown environments require exploration and learning, prioritising knowledge gain.



\subsection{Intelligent Virtual Environment}

An environment hosting both intelligent artificial agents and humans can be observed as an intelligent virtual environment (IVE), which is focused on simulating a physical world inhabited by autonomous intelligent entities \cite{okresaduric2019MAMbO5NewOntology,Barella2012mam5,luck2000ApplyingArtificialIntelligence}. Building on the set of features usually used to describe an environment, it should be noted here that an IVE can be described using any of them since it is defined by incorporating different types of agents, i.e. artificial and human, situated and virtual, and not the nature or behaviour of that environment. The autonomous intelligent entities of an IVE can be classified into artificial agents and artefacts. Both of these concepts can be observed as either virtual or situated in the environment, i.e. having a physical presence in the observed environment.



\subsection{Agent Implementation Framework}

Many different frameworks, libraries, and approaches are available in the context of implementing the multiagent systems described above. State-of-the-art frameworks combine agents and large language models to automate tasks and streamline communication with such and similar artificial intelligence models. Other approaches allow the implementation of agents, focusing on their behaviour, social features, and interaction with other agents. This paper focuses specifically on the Smart Python Agent Development Environment (SPADE) \cite{gregori2006JabberbasedMultiagentSystem,palanca2025SmartPythonAgent}, a Python library hosting a platform for developing multiagent systems utilising instant messaging protocol XMPP.



\section{Related Work}\label{sec: Related Work}

Ontologies are often used in the domain of multiagent systems in a layered architecture, where the domain ontology layer is used to encode knowledge related to a specific domain \cite{skobelev2020OntologybasedOpenMultiagent,schmidt2015OntologyCollaborativeTasks}, the layer concerned with the reasoning processes of an agent is used for mapping concepts to agent behaviour, and their reasoning and inference engines \cite{freitas2015IntegratingOntologiesMultiAgent,lavbic2010OntologybasedMultiagentSystem}. In contrast, the coordination layer is used for managing communication between agents while using a defined messaging protocol \cite{breitman2005UsingOntologiesFormalize}.

Specific effort has been made in recent history to model and implement a complete lifecycle of a multiagent system based on or utilising an ontology, where ontology is used as a medium of describing a domain by instantiating individuals populating templates pertaining to the given domain \cite{okresaduric2019MAMbO5NewOntology,freitas2017ModelDrivenEngineeringMultiAgent}, followed by a model verification process using automated reasoners to verify and validate the built models \cite{skobelev2020OntologybasedOpenMultiagent,schmidt2015OntologyCollaborativeTasks,warden2010OntologyBasedMultiagentSimulations}, finally arriving to code generation based on the defined templates transformed by the defined engines and frameworks into agent architectures and environments of usually specific languages \cite{freitas2015IntegratingOntologiesMultiAgent,freitas2017ModelDrivenEngineeringMultiAgent,aghdam2022NewOntologyBasedMultiAgent}.

Some of the challenges encountered in recent literature can be observed in the context of e.g. temporal reasoning, where the focus is given to processing complex queries over time intervals \cite{warden2010OntologyBasedMultiagentSimulations} and distributed ontologies that can be queried across edge devices using the concept of federated querying \cite{aghdam2022NewOntologyBasedMultiAgent}. Despite these challenges, various solutions are being presented rapidly, such as new lazy loading architectures that agents can use for fetching ontology fragments on demand \cite{ottens2009MultiAgentSystemDynamic} or mechanisms for approximate reasoning that would trade precision for performance using different algorithms \cite{freitas2017ModelDrivenEngineeringMultiAgent}. 

It is not often found in the available literature that the presented approaches utilising frameworks, environments, or libraries would propose an extensive approach which would encompass all three stages mentioned in the paragraph above. Some attempts can be observed, and improvements can be identified at either end of the pipeline. However, some approaches do provide an ontology to be used to model the system, a tool to perform it with, and a process to utilise that model to implement a part of the modelled system \cite{okresaduric2019OrganizationalModelingLargeScale}. This paper aims to provide a glimpse at the work in progress towards enriching the offer of the latter.



\section{The Proposed MAGO-Ag Ontology}\label{sec: The Ontology}

\begin{figure}
  \centering
  \includegraphics[width=\linewidth]{MAGO-Ag ontology.png}
  \caption{Class hierarchy of the MAGO-Ag ontology and the imported ontologies; bolded concepts are added or modified}
  \label{fig: MAGO-Ag ontology}
\end{figure}

The ontology proposed here, MAGO-Ag, details and extends the MAMbO5 ontology \cite{okresaduric2019MAMbO5NewOntology}. The additional concepts and properties are intended explicitly for modelling knowledge about implementing a multiagent system. The framework uses this to generate implementation templates. MAMbO5 ontology, in turn, extends an ontology for describing IVEs \cite{Barella2012mam5} and combines it with an ontology comprising concepts relevant to the organisational modelling of large-scale multiagent systems \cite{Schatten2014towardsLSMAS,Schatten2014initialLSMAS}.

The additions on top of the MAMbO5 ontology, provided by the MAGO-Ag ontology, include several concepts (shown in Fig. \ref{fig: MAGO-Ag ontology}) and a set of data and object properties. All of these entities have been added in order to facilitate the use of the framework for transforming the modelled system into an implementation blueprint. Therefore, the following concepts have been added or modified. The \texttt{Agent} class is the new concept that encompasses specific types of agents defined in the imported ontologies. A MAGO-Ag agent is defined as an individual of either the Inhabitant\_Agent class or the OrganizationalUnit class, both of which are imported.

\begin{figure*}
    \centering
    % \includegraphics[width=\linewidth]{framework structure.png}
    \resizebox{\linewidth}{!}{
        \input{framework}
    }
    \caption{Visual representation of the workings of the proposed MAGO-Ag frameworks, populating the empty workspace}
    \label{fig: framework structure}
\end{figure*}

The \texttt{Artefact} class comprises artefacts that agents within an IVE can interact with. It is used in the transformation framework to model resources agents can access. A new subclass, \texttt{Software Artefact}, has been added since it was important to designate the XMPP server hosting SPADE agents. However, it can also contain other software artefacts, e.g., available remote servers and similar. Additional novelty in this class is explicitly stated individual and organisational knowledge artefacts. While the individual knowledge artefact is available exclusively to an individual agent, an organisational knowledge artefact contains pieces of knowledge shared amongst a group of agents, such as a collection of common concepts and shared knowledge within a company, a group, or an organisational unit in general. A further distinction between the two can be considered via an example wherein an individual agent has access to shared knowledge, providing it with concepts. Still, it keeps all the individuals of these concepts, for example, visible in the current environment, to itself, stored in its private individual of the individual knowledge artefact class. The Artefact Resource class is designed to collect all the individuals that represent different resources provided by various artefact individuals. For example, a web application individual of the Artefact class may provide several REST API endpoints modelled as individuals of the Artefact Resource class. 

Individuals of the \texttt{Objective} class are specific objectives to be reached. Objectives are connected to activities and behaviours, i.e. individuals of the \texttt{Activity} and \texttt{Behaviour} classes, in a dependence relation. In other words, an Objective individual represents a goal that a specific set of activities can reach. An activity individual denotes an activity as an abstract concept that is implemented as a set of instances of the Behaviour class, meaning that implemented agent behaviours can be abstracted as activities that can be used to achieve a specific objective.

Individuals of the \texttt{System} class are considered as concepts grouping together all the elements modelled as pertaining to an individual environment, i.e. all the entities of the modelled system that can be found in a shared environment.

In addition to the new or slightly updated classes, such as adding a new label for the purposes of identifying classes in the transformation process of the framework, described here in Sec. \ref{sec: The Framework}., several object and data properties have been added to the ontology, utilising imported and newly created classes. The addition of several properties was motivated by the purpose of the ontology, i.e. to foster the creation of a model that will feature concepts that can make it easier to generate a working implementation template. For example, properties such as \texttt{has initial state}, \texttt{is before state}, and \texttt{has final state} are used for modelling a finite-state machine behaviour individual alongside the state behaviour individuals.



\section{The Proposed MAGO-Ag Framework}\label{sec: The Framework}

Once the ontology contains all the necessary information to model the wanted multiagent system using the concepts provided in the MAGO-Ag ontology and the concept individuals provided by the system designer, it is possible to use the MAGO-Ag framework\footnote{Implementation available on \href{https://github.com/AILab-FOI/MAGO/tree/main/Deliverables/Phase\%201}{GitHub}} to translate the modelled system into an implementation template. This implementation template consists of several Python files, each containing one of the following: implemented agent class that can be instantiated to individual agents; implemented behaviours that agent individuals can use; and code that can be used to run the implementation template, i.e. the agents of the modelled system. The keyword "template" used throughout this paper when the output of the framework is referenced denotes that the generated code does not contain a finalised implementation of agents and the modelled system but provides the user with foundations whereupon they can build the rest of the implementation for the observed system.

The process of attaining the implementation template of the modelled system is initiated by importing the MAGO-Ag ontology and populating it with individuals and concepts, thus defining and creating a model of the observed system located in an IVE. The input ontology must be stored in a file format that can be read by the \texttt{owlready2} Python library, which is used for reading the contents of the input ontology. Once the ontology is read, the framework follows the designed algorithm, shown in Fig. \ref{fig: framework structure}, that first starts creating a single empty workspace. The following step is to render all the behaviour implementations. Once the behaviours are translated, agent implementation templates are rendered. Role-behaviour relations, as well as role and knowledge artefact availability, are rendered as a part of rendering agent implementation templates. Finally, the workspace is finalised, ultimately containing the code for running the translated system.



\subsection{Use-Case Example}\label{sec: use case example}

\begin{figure*}
  \centering
  \includegraphics[width=\linewidth]{MAGO-Ag example individuals.png}
  \caption{A selection of individuals and their relationships in the example ontology described in the use-case example in Sec. \ref{sec: use case example}}
  \label{fig: MAGO-Ag example individuals}
\end{figure*}

The following simple use-case example shows how a minimal system comprising four agents pertaining to two distinct agent classes can be transformed into an implementation template using the MAGO-Ag framework.

This agricultural multiagent system coordinates farmers and drones to optimise crop management. Two Agent Farmer individuals monitor soil conditions using periodic checks and plan tasks using a finite state machine behaviour consisting of the following states: Assessing, Deciding, and Implementing. Two Agent Drone class individuals undertake aerial surveys via event-triggered one-shot behaviours and navigate fields through flight patterns controlled by a finite state machine behaviour comprising three states: Idle, Surveying, and Returning. 

The modelled system\footnote{Example ontology available on \href{https://github.com/AILab-FOI/MAGO/tree/main/Documents/250602\%20MIPRO\%20Opatija\%20HR}{GitHub}} aims to achieve two objectives: maximising yield through coordinated crop analysis and minimising resource waste via irrigation scheduling. Farmer agents can access individual weather data, while drone agents store topographic map data. Both types of agents share a crop growth knowledge base. The defined roles provide behaviour allocation: The Agronomist role provides behaviours useful to the Agent Farmer individuals, the Surveyor role enables using behaviours helpful to individuals of class DroneAgent, and the Field manager role provides the FSM behaviour for planning tasks.

The described example balances perspectives of specialisation and coordination in the modelled system. First, the two agent classes reflect distinct physical capabilities: ground-based decision-making agents (farmers) versus aerial data collection agents (drones). Finite state machine behaviour implementations ensure deterministic state transitions for critical processes (task planning and navigation), reducing unpredictable emergent behaviour. Using the role-based behaviour assignment (Agronomist role enabling soil monitoring behaviour, Surveyor role providing aerial survey behaviour, etc.) is in accordance with the principle of modular responsibility separation, allowing future expansion in the set of available roles or behaviours without system redesign. The knowledge model combines individual artefacts with organisation-wide shared knowledge to prevent data duplication where possible while maintaining context-specific expertise. The shared knowledge artefact contains information that can be used as both a collaboration anchor and a conflict resolution mechanism, forcing consensus through versioned updates. The design described above satisfies core multiagent system requirements: decentralised control (via role autonomy), goal-oriented coordination (through activities linked to objectives and implemented by behaviours), and environment awareness (via artifact-mediated sensing). A subset of the modelled individuals based on the provided description is shown in Fig. \ref{fig: MAGO-Ag example individuals}.

\begin{figure}
  \centering
  \includegraphics[width=\linewidth]{code example behaviour.png}
  \caption{Python code implementing a SPADE finite-state machine behaviour consisting of three interconnected states}
  \label{fig: code example behaviour}
\end{figure}

\begin{figure}
  \centering
  \includegraphics[width=\linewidth]{code example agent.png}
  \caption{Python code instantiating a SPADE agent with data on the individual's URI, available knowledge artefacts, available roles and related behaviours, and the defined system features; placeholder strings within symbols \texttt{<} and \texttt{>} are used here for entity URIs, for the sake of legibility}
  \label{fig: code example agent}
\end{figure}

After running the framework, the modelled system is translated into a set of Python files containing its spread-out implementation template\footnote{Example available on \href{https://github.com/AILab-FOI/MAGO/tree/main/Documents/250602\%20MIPRO\%20Opatija\%20HR/Example\%20implementation\%20template}{GitHub}.}. The generated system can be run immediately, although the agents at this stage do not perform anything significant, and problems might arise if knowledge artefacts are given non-existent URIs. This implementation template can now be further detailed to become entirely appropriate for implementing the observed system. SPADE implementation template example of the modelled Task planning behaviour finite-state machine behaviour is shown in Fig. \ref{fig: code example behaviour}. Finally, Fig. \ref{fig: code example agent} contains SPADE implementation instantiating the Agent class and setting up an agent individual with its starting knowledge.



\section{Discussion and Conclusion}\label{sec: Discussion and Conclusion}

The framework presented in this paper shows a pragmatic view of developing multiagent systems coupled with ontologies. The systematic mapping of domain concepts to, e.g., agent classes, roles, and behaviours, using the provided applicable ontology ensures that the rendered collection of implementation templates maintains conceptual integrity, from modelling to implementing a multiagent system. The described approach reduces the need for extensive initial manual coding, thus decreasing potential errors in the implementation of the modelled system. However, the currently available implementation templates require further coding. The integration of shared domain semantics through knowledge artefacts fosters clear and unambiguous inter-agent communication and collaboration. The presented case study shows how the framework may be used to accelerate the multiagent system design cycle while upholding the principles of decentralised control, goal-oriented coordination, and environment awareness in the modelled system. The clear separation of roles and similar promotes the modularity of the modelled system.

This paper provides a view on practically combining conceptual modelling and usable implementation of multiagent systems. The presented ontology-driven approach facilitates the transformation of a conceptual model into executable implementation templates. Further refinement is possible, as it should explore incorporating additional agent development frameworks, mechanisms of dynamic adaptation, integration with large language models, and use in agent communication and migration between workspaces. Overall, the presented framework offers valuable insights for both researchers and practitioners and contributes to the evolving field of multiagent system engineering.

\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
