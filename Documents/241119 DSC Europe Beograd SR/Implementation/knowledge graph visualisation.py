from graphviz import Digraph

# Initialize a directed graph
dot = Digraph(format="png")
dot.attr(size="14,14")  # Define a square canvas
dot.attr(layout="neato")  # Use 'neato' layout for a balanced, square arrangement
dot.attr(overlap="false")
# dot.attr(sep="-5")

# Define nodes and relationships
characters = {
    "Hobbits": ["<PERSON>od<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>pp<PERSON>"],
    "Elf": ["Legolas"],
    "Humans": ["Aragorn", "Boromir"],
    "Dwarf": ["<PERSON>im<PERSON>"],
    "Wizard": ["Gandalf"],
}

titles = {
    "Frodo": "Ring-bearer",
    "<PERSON><PERSON>": "Gardener",
    "<PERSON>": "Knight of Rohan",
    "<PERSON><PERSON><PERSON>": "Guard of Gondor",
    "<PERSON>gol<PERSON>": "Prince of Mirkwood",
    "Aragorn": "King of Gondor",
    "<PERSON>rom<PERSON>": "Captain of Gondor",
    "<PERSON><PERSON><PERSON>": "Warrior",
    "<PERSON><PERSON><PERSON>": "<PERSON>",
}

weapons = {
    "Frodo": "<PERSON>",
    "Samwise": "Sword",
    "<PERSON>": "Sword",
    "<PERSON><PERSON><PERSON>": "Sword",
    "Legolas": "Bow",
    "<PERSON>gor<PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>romir": "Sword and Shield",
    "<PERSON><PERSON><PERSON>": "Axe",
    "<PERSON>andalf": "<PERSON> and <PERSON>lamdring",
}

origins = {
    "Frodo": "Shire",
    "<PERSON>wise": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>ppin": "Shire",
    "<PERSON>golas": "<PERSON>kwood",
    "<PERSON>gorn": "Dún<PERSON>in",
    "<PERSON>romir": "Gondor",
    "<PERSON>imli": "<PERSON> <PERSON>",
    "<PERSON>andalf": "<PERSON><PERSON>r",
}

# <PERSON>d character nodes and their group labels
for group, members in characters.items():
    dot.node(group, shape="box", style="filled", color="skyblue")
    for name in members:
        dot.node(name, shape="ellipse", style="filled", color="lightgreen")
        dot.edge(group, name)

# Add titles, weapons, and origins as separate concepts
for character, title in titles.items():
    dot.node(title, shape="diamond", style="filled", color="lightcoral")
    dot.edge(character, title)

for character, weapon in weapons.items():
    dot.node(weapon, shape="hexagon", style="filled", color="gold")
    dot.edge(character, weapon)

for character, origin in origins.items():
    dot.node(origin, shape="circle", style="filled", color="plum")
    dot.edge(character, origin)

dot.node("Race", shape="box", style="filled", color="blue")
for character in characters.keys():
    dot.edge("Race", character)

# Render the graph
dot.render("lotr_taxonomy_square", view=True)
