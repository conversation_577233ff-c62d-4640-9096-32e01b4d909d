\begin{tikzpicture}
\node [anchor = north west] at (-2,2) {Agent};

\onslide<2->{
    \node [draw, rounded corners, minimum width = 4cm, minimum height = 4cm] (agent) at (0,0) {};
    \node (inside) at (1,0) {\Huge ?};
}


\onslide<3-5>{
    \node [draw, rounded corners, minimum width = 7cm, minimum height = 5cm] (env) at (1,0) {};
    \node [rotate = -90] at (4,0) {Environment};
}

\onslide<4->{
    \node (s) at (1,1.5) {\scriptsize Sensors};
    \draw [-Latex, bend right=10] (4,1.5) to node [midway, above] {\tiny percepts} (s.east) ;
    \draw [-Latex] (s) to (inside);
}

\onslide<5->{
    \node (a) at (1,-1.5) {\scriptsize Actuators};
    \draw [Latex-, bend left=10] (4,-1.5) to node [midway, below] {\tiny actions} (a.east) ;
    \draw [-Latex] (inside) to (a);
}

\onslide<6>{
    \node [draw, dashed, rounded corners, minimum width = 7cm, minimum height = 5cm] (env) at (1,0) {};
    \node [rotate = -90] at (4,0) {Environment};
}
\end{tikzpicture}