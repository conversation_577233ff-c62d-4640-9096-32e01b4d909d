{"cells": [{"cell_type": "markdown", "id": "4e1e582d-1212-44a0-844d-41295ef48773", "metadata": {}, "source": ["# [SPADE](https://spade-mas.readthedocs.io/en/latest/index.html): Smart Python Agent Development Environment "]}, {"cell_type": "markdown", "id": "784924dd", "metadata": {}, "source": ["# BOWLDI"]}, {"cell_type": "markdown", "id": "818ef95f", "metadata": {}, "source": ["The **BOWLDI** idea is to combine ontologies (OWL) and BDI agents. In particular, it is envisioned to work with the **SPADE** platform, i.e. the BDI implementation used by SPADE, which is based on the AgentSpeak language. Therefore, BOWLDI allows to use OWL ontologies in the BDI agents implemented in SPADE. This is achieved by translating OWL/RDF files into AgentSpeak code and vice versa.\n", "\n", "The conversion is performed within the `BOWLDIConverter` Python class that uses `owlready2` library to read ontology files or store information into them. Inversely, the said class can also convert AgentSpeak code into OWL/RDF files. The conversion is based on the mapping of OWL classes and properties to AgentSpeak plans and beliefs.\n", "\n", "The class is envisioned as a modular construct that supports various functionalities, each of which may be used on their own as well. The main functionalities are:\n", "- **Ontology to AgentSpeak conversion**: The conversion of OWL ontologies into AgentSpeak code.\n", "- **AgentSpeak to Ontology conversion**: The conversion of AgentSpeak code into OWL ontologies.\n", "\n", "Using the `BOWLDIConverter` class can be as easy as creating an instance of the class and calling the desired method."]}, {"cell_type": "markdown", "id": "d6c6608c", "metadata": {}, "source": ["The implementation of the `BOWLDIConverter` class is publicly available on GitHub [repository](https://github.com/AILab-FOI/MAGO/blob/main/Extra/BOWLDI/bowldi.py).\n", "\n", "You can download it e.g. by using `wget`."]}, {"cell_type": "code", "execution_count": null, "id": "986e1308-1c48-47b2-94ff-c69fa65e0cf6", "metadata": {}, "outputs": [], "source": ["!wget https://raw.githubusercontent.com/AILab-FOI/MAGO/main/Extra/BOWLDI/bowldi.py"]}, {"cell_type": "markdown", "id": "da5d6d89", "metadata": {}, "source": ["The only library necessary for `BOWLDIConverter` to work is `owlready2`. You can install it by running the following command:"]}, {"cell_type": "code", "execution_count": null, "id": "faa893c1", "metadata": {}, "outputs": [], "source": ["try:\n", "    import owlready2\n", "except:\n", "    !pip install owlready2"]}, {"cell_type": "markdown", "id": "484a521a", "metadata": {}, "source": ["Otherwise, you can create a new Python environment with the necessary libraries. One possible solution is to create a Conda environment using the following environment definition:\n", "\n", "```yaml\n", "name: bowl<PERSON><PERSON>\n", "\n", "dependencies:\n", "  - python\n", "  - conda-forge::owlready2\n", "  - conda-forge::pexpect\n", "  - pip\n", "  - pip:\n", "    - spade==4.0.2\n", "    - spade_bdi\n", "\n", "# conda env create -f env.yml\n", "# conda env update -f env.yml --prune\n", "```\n", "\n", "This file is also available publicly at GitHub [repository](https://github.com/AILab-FOI/MAGO/blob/main/Documents/250320%20Class/env.yml), and can be downloaded using `wget` as well:\n", "\n", "```bash\n", "wget https://raw.githubusercontent.com/AILab-FOI/MAGO/main/Documents/250320%20Class/env.yml\n", "```"]}, {"cell_type": "markdown", "id": "5991028a-8bd9-4ecd-9ad6-37f8fc13f610", "metadata": {}, "source": ["## Example 1: Converting AgentSpeak to OWL\n", "\n", "`BOWLDIConverter` can take a string input of AgentSpeak code and convert it to an ontology file. The following is a simple example containing: a couple of beliefs. These can be mapped to:\n", "- two ontology concepts (lines 5--7),\n", "- one individual (line 10),\n", "- two properties (lines 13 and 16)."]}, {"cell_type": "code", "execution_count": null, "id": "2378aabc", "metadata": {}, "outputs": [], "source": ["from bowldi import BOWLDIConverter\n", "\n", "input_data = \"\"\"\n", "// Concepts and Hierarchies\n", "concept(person).\n", "concept(wizard).\n", "is_a(wizard, person).\n", "\n", "// Individual\n", "individual(ganda<PERSON>, wizard).\n", "\n", "// Object Property\n", "object_property(person, is_friend_with, person).\n", "\n", "// Data Property\n", "data_property(person, has name, string).\"\"\"\n", "\n", "converter = BOWLDIConverter(\n", "    input_data=input_data,\n", ")\n", "\n", "converter.get_response()"]}, {"cell_type": "code", "execution_count": null, "id": "7e040831-c38a-4622-bff1-7d80fa225041", "metadata": {}, "outputs": [], "source": ["!cat output.owl"]}, {"cell_type": "markdown", "id": "844b0090", "metadata": {}, "source": ["The response received on line 22 should read:\n", "\n", "```json\n", "{\n", "  \"status\": \"success\",\n", "  \"message\": \"Conversion complete.\",\n", "  \"file\": \"<path to file>/output.owl\"\n", "}\n", "```"]}, {"cell_type": "markdown", "id": "043e6d65", "metadata": {}, "source": ["Converting AgentSpeak to an ontology file will always yield a file output, i.e. will always write the output to a file. The generated file `output.owl` should have the following content, based on the input AgentSpeak code:\n", "\n", "```xml\n", "<?xml version=\"1.0\"?>\n", "<rdf:RDF xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\"\n", "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema#\"\n", "    xmlns:rdfs=\"http://www.w3.org/2000/01/rdf-schema#\"\n", "    xmlns:owl=\"http://www.w3.org/2002/07/owl#\" xml:base=\"file://<path to file>/output.owl\"\n", "    xmlns=\"file://<path to file>/output.owl#\">\n", "\n", "    <owl:Ontology rdf:about=\"file://<path to file>/output.owl\"/>\n", "\n", "    <owl:ObjectProperty rdf:about=\"#is_friend_with\">\n", "        <rdfs:domain rdf:resource=\"#person\"/>\n", "        <rdfs:range rdf:resource=\"#person\"/>\n", "        <rdfs:label xml:lang=\"en-gb\">is friend with</rdfs:label>\n", "        <rdfs:isDefinedBy rdf:datatype=\"http://www.w3.org/2001/XMLSchema#string\">self</rdfs:isDefinedBy>\n", "    </owl:ObjectProperty>\n", "\n", "    <owl:DatatypeProperty rdf:about=\"#has_name\">\n", "        <rdfs:domain rdf:resource=\"#person\"/>\n", "        <rdfs:range rdf:resource=\"http://www.w3.org/2001/XMLSchema#string\"/>\n", "        <rdfs:label xml:lang=\"en-gb\">has name</rdfs:label>\n", "        <rdfs:isDefinedBy rdf:datatype=\"http://www.w3.org/2001/XMLSchema#string\">self</rdfs:isDefinedBy>\n", "    </owl:DatatypeProperty>\n", "\n", "    <owl:Class rdf:about=\"#person\">\n", "        <rdfs:subClassOf rdf:resource=\"http://www.w3.org/2002/07/owl#Thing\"/>\n", "        <rdfs:label xml:lang=\"en-gb\">person</rdfs:label>\n", "        <rdfs:isDefinedBy rdf:datatype=\"http://www.w3.org/2001/XMLSchema#string\">self</rdfs:isDefinedBy>\n", "    </owl:Class>\n", "\n", "    <owl:Class rdf:about=\"#wizard\">\n", "        <rdfs:subClassOf rdf:resource=\"#person\"/>\n", "        <rdfs:label xml:lang=\"en-gb\">wizard</rdfs:label>\n", "        <rdfs:isDefinedBy rdf:datatype=\"http://www.w3.org/2001/XMLSchema#string\">self</rdfs:isDefinedBy>\n", "    </owl:Class>\n", "\n", "    <owl:NamedIndividual rdf:about=\"#gandalf\">\n", "        <rdf:type rdf:resource=\"#wizard\"/>\n", "        <rdfs:label xml:lang=\"en-gb\">gandalf</rdfs:label>\n", "        <rdfs:isDefinedBy rdf:datatype=\"http://www.w3.org/2001/XMLSchema#string\">self</rdfs:isDefinedBy>\n", "    </owl:NamedIndividual>\n", "\n", "</rdf:RDF>\n", "```"]}, {"cell_type": "markdown", "id": "af62656c", "metadata": {}, "source": ["## Example 2: Converting OWL to AgentSpeak\n", "\n", "`BOWLDIConverter` class can take an ontology file and convert it to AgentSpeak code. The following is a simple example of reading the ontology created in the previous example and converting it to AgentSpeak code."]}, {"cell_type": "code", "execution_count": null, "id": "63bd939c", "metadata": {}, "outputs": [], "source": ["from bowldi import BOWLDIConverter\n", "\n", "converter = BOWLDIConverter(\n", "    input_data_path=\"output.owl\",\n", "    output_data_path=\"example_output.asl\",\n", ")\n", "\n", "if converter.get_response().get(\"status\") == \"success\":\n", "    print(converter.agentspeak_output)"]}, {"cell_type": "code", "execution_count": null, "id": "52692c70-9f86-4f72-ace6-38ec<PERSON>eee48", "metadata": {}, "outputs": [], "source": ["!cat example_output.asl"]}, {"cell_type": "markdown", "id": "c5a0aca8", "metadata": {}, "source": ["The above code will take the ontology stored in the file `output.owl` and convert it to AgentSpeak code. The output is saved in the provided `output_data_path` file. In addition to receiving the output saved into the given file, the output can also be retrieved as a string using the `agentspeak_output` attribute.\n", "\n", "The rendered AgentSpeak code should look as follows, based on the expected input:\n", "\n", "```prolog\n", "concept(person)[source(['self'])].\n", "concept(wizard)[source(['self'])].\n", "is_a(wizard, person)[source(['self'])].\n", "object_property(person, is_friend_with, person)[source(['self'])].\n", "data_property(person, has_name, str)[source(['self'])].\n", "individual(gandalf, wizard)[source(['self'])].\n", "```"]}, {"cell_type": "markdown", "id": "fdb002bc", "metadata": {}, "source": ["This last output features a list of beliefs, but this time each of those is given a `source` attribute. This attribute should be used to tell the agent where the particular piece of knowledge comes from, i.e. from which agent or knowledge source (e.g. an ontology) it originates. In this instance, all the knowledge comes from the agent itself, hence the `self` value."]}, {"cell_type": "markdown", "id": "34151df4", "metadata": {}, "source": ["## Example 3: Converting OWL to AgentSpeak with an external source\n", "\n", "A simple ontology has been prepared, featuring a couple of concepts and properties, related to the domain of the Lord of the Rings novel. The ontology is available publicly on GitHub [repository](https://github.com/AILab-FOI/MAGO/blob/main/Documents/250320%20Class/lotr_example.owl), and it can be visualized using WebOWL in this [link](https://service.tib.eu/webvowl/#iri=https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250320%20Class/onto_example.rdf#)\n", "\n", "The linked ontology contains the following information:\n", "\n", "**Classes**\n", "\n", "- `person`\n", "- `wizard` (subclass of `person`)\n", "- `elf` (subclass of `person`)\n", "- `human` (subclass of `person`)\n", "- `dwarf` (subclass of `person`)\n", "- `hobbit` (subclass of `person`)\n", "- `kingdom`\n", "- `ring_of_power`\n", "\n", "**Object Properties**\n", "\n", "- `has_king` (domain: `kingdom`, range: `person`)\n", "- `is_friend_with` (domain: `person`, range: `person`)\n", "- `is_in_team_with` (domain: `person`, range: `person`)\n", "- `has_ring` (domain: `person`, range: `ring_of_power`)\n", "- `fights_against` (domain: `person`, range: `person`)\n", "\n", "**Datatype Properties**\n", "\n", "- `has_name` (domain: `person`, range: `string`)\n", "- `description` (domain: `ring_of_power`, range: `string`)"]}, {"cell_type": "markdown", "id": "0234259e", "metadata": {}, "source": ["Importing the above ontology, let us prepare another ontology that uses the concepts of the imported ontology to construct additional data. The developed example ontology that imports the `lotr_ontology.owl` is available [online](https://github.com/AILab-FOI/MAGO/blob/main/Documents/250320%20Class/onto_example.rdf) as well. It may be downloaded e.g. by using `wget`."]}, {"cell_type": "code", "execution_count": null, "id": "1e8bd796", "metadata": {}, "outputs": [], "source": ["!wget https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250320%20Class/onto_example.rdf"]}, {"cell_type": "markdown", "id": "02456c59", "metadata": {}, "source": ["This example ontology extends the `lotr_ontology.owl` and defines individuals of the classes stored therein.\n", "\n", "The three defined individuals are described as follows, using the RDF/XML syntax generated by Protégé:\n", "\n", "```xml\n", "    <!-- <path to onto>/onto_example.rdf#OWLNamedIndividual_443b4123_3a46_49a2_b8c2_29ced3899779 -->\n", "\n", "    <owl:NamedIndividual rdf:about=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_443b4123_3a46_49a2_b8c2_29ced3899779\">\n", "        <rdf:type rdf:resource=\"<path to onto>/lotr_example.owl#wizard\"/>\n", "        <lotr:has_name><PERSON><PERSON><PERSON> the <PERSON></lotr:has_name>\n", "        <lotr:has_name><PERSON><PERSON><PERSON><PERSON></lotr:has_name>\n", "        <rdfs:label xml:lang=\"en-gb\"><PERSON><PERSON><PERSON> the <PERSON></rdfs:label>\n", "    </owl:NamedIndividual>\n", "    <owl:Axiom>\n", "        <owl:annotatedSource rdf:resource=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_443b4123_3a46_49a2_b8c2_29ced3899779\"/>\n", "        <owl:annotatedProperty rdf:resource=\"<path to onto>/lotr_example.owl#has_name\"/>\n", "        <owl:annotatedTarget><PERSON><PERSON><PERSON> the <PERSON></owl:annotatedTarget>\n", "        <rdfs:isDefinedBy>Bogdan</rdfs:isDefinedBy>\n", "    </owl:Axiom>\n", "    \n", "\n", "\n", "    <!-- <path to onto>/onto_example.rdf#OWLNamedIndividual_8584c614_acbd_4080_9032_a3a6dd9d1a9b -->\n", "\n", "    <owl:NamedIndividual rdf:about=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_8584c614_acbd_4080_9032_a3a6dd9d1a9b\">\n", "        <rdf:type rdf:resource=\"<path to onto>/lotr_example.owl#human\"/>\n", "        <lotr:is_friend_with rdf:resource=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_443b4123_3a46_49a2_b8c2_29ced3899779\"/>\n", "        <lotr:is_friend_with rdf:resource=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_96afddcd_81ae_467a_9468_225b426210f4\"/>\n", "        <lotr:has_name><PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON></lotr:has_name>\n", "        <rdfs:label xml:lang=\"en-gb\"><PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON></rdfs:label>\n", "    </owl:NamedIndividual>\n", "    \n", "\n", "\n", "    <!-- <path to onto>/onto_example.rdf#OWLNamedIndividual_96afddcd_81ae_467a_9468_225b426210f4 -->\n", "\n", "    <owl:NamedIndividual rdf:about=\"<path to onto>/onto_example.rdf#OWLNamedIndividual_96afddcd_81ae_467a_9468_225b426210f4\">\n", "        <rdf:type rdf:resource=\"<path to onto>/lotr_example.owl#elf\"/>\n", "        <lotr:has_name>Legolas Greenleaf</lotr:has_name>\n", "        <rdfs:label xml:lang=\"en-gb\">Legolas</rdfs:label>\n", "    </owl:NamedIndividual>\n", "```"]}, {"cell_type": "markdown", "id": "1176b60d", "metadata": {}, "source": ["When we use the `BOWLDIConvert` class to convert the information of `onto_example.rdf` into AgentSpeak code, the output includes a defined source value for every belief. Information that was defined in the `onto_example.rdf` ontology will be designated as `self`, while the information imported from the `lotr_example.owl` ontology will be marked using the imported ontology's IRI. The latter makes it possible to import the relevant ontology when converting the AgentSpeak code back to an ontology. Since we want the information that is transferred into AgentSpeak code to be complete, the various concepts of the imported ontology are included in the output."]}, {"cell_type": "code", "execution_count": null, "id": "b1d68a34", "metadata": {}, "outputs": [], "source": ["from bowldi import BOWLDIConverter\n", "\n", "converter = BOWLDIConverter(\n", "    input_data_path=\"onto_example.rdf\",\n", ")\n", "\n", "if converter.get_response().get(\"status\") == \"success\":\n", "    print(converter.agentspeak_output)\n", "else:\n", "    print(converter.get_response())"]}, {"cell_type": "markdown", "id": "f9105986", "metadata": {}, "source": ["If everything was as expected, the AgentSpeak output should be saved in the `output.asl` file and look like follows:\n", "\n", "```prolog\n", "concept(wizard)[source(['<path to ontology>/lotr_example.owl'])].\n", "is_a(wizard, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(human)[source(['<path to ontology>/lotr_example.owl'])].\n", "is_a(human, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(elf)[source(['<path to ontology>/lotr_example.owl'])].\n", "is_a(elf, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(kingdom)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(person)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(ring)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(dwarf)[source(['<path to ontology>/lotr_example.owl'])].\n", "is_a(dwarf, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "concept(hobbit)[source(['<path to ontology>/lotr_example.owl'])].\n", "is_a(hobbit, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "object_property(person, is_friend_with, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "object_property(kingdom, has_king, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "object_property(person, is_in_team_with, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "object_property(person, has_ring, ring)[source(['<path to ontology>/lotr_example.owl'])].\n", "object_property(person, fights_against, person)[source(['<path to ontology>/lotr_example.owl'])].\n", "data_property(person, has_name, str)[source(['<path to ontology>/lotr_example.owl'])].\n", "data_property(ring, description, str)[source(['<path to ontology>/lotr_example.owl'])].\n", "individual(gandalf_the_grey, wizard)[source(['self'])].\n", "individual(arag<PERSON>_son_of_a<PERSON><PERSON>, human)[source(['self'])].\n", "individual(legolas, elf)[source(['self'])].\n", "relation(gandalf_the_grey, has_name, \"<PERSON><PERSON><PERSON> the Grey\")[source(<PERSON><PERSON><PERSON>)].\n", "relation(gandalf_the_grey, has_name, \"<PERSON><PERSON><PERSON><PERSON>\")[source(self)].\n", "relation(a<PERSON><PERSON>_son_of_a<PERSON><PERSON>, is_friend_with, gandalf_the_grey)[source(self)].\n", "relation(arag<PERSON>_son_of_a<PERSON><PERSON>, is_friend_with, legolas)[source(self)].\n", "relation(a<PERSON><PERSON>_son_of_a<PERSON><PERSON>, has_name, \"<PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON>\")[source(self)].\n", "relation(legolas, has_name, \"Legolas Greenleaf\")[source(self)].\n", "```"]}, {"cell_type": "markdown", "id": "5422fb82", "metadata": {}, "source": ["## Example 4: Converting AgentSpeak with external source to OWL\n", "\n", "The `BOWLDIConverter` class can convert AgentSpeak code into an ontology file even when source argument of some beliefs references another source. If that other source is an ontology (i.e. if the source is a link it is assumed to be an ontology), then the translation process will try to import the ontology before processing the rest of the contents of the AgentSpeak code. The following is a simple example of reading the AgentSpeak code created in the previous example and converting it to an ontology file."]}, {"cell_type": "code", "execution_count": null, "id": "e202f38c", "metadata": {}, "outputs": [], "source": ["from bowldi import BOWLDIConverter\n", "\n", "converter = BOWLDIConverter(\n", "    input_data_path=\"output.asl\",\n", ")\n", "\n", "print(converter.get_response())"]}, {"cell_type": "markdown", "id": "664ea475", "metadata": {}, "source": ["The output of the last line should be indicating success:\n", "\n", "```json\n", "{\n", "  \"status\": \"success\",\n", "  \"message\": \"Conversion complete.\",\n", "  \"file\": \"<path to file>/output.owl\"\n", "}\n", "```"]}, {"cell_type": "markdown", "id": "3a1c4132", "metadata": {}, "source": ["The rendered file should not contain definitions of the concepts defined in the imported ontology. Instead, only the concepts designated as not being sourced by an external ontology should be included.\n", "\n", "One major difference between the initial ontolgoy of Example 3 above, and the output ontology of this example is that all the concepts that are \"native\" to the observed ontology (and not to the imported ontology) have one new property added, namely the `isDefinedBy` annotation property. The value of this annotation data property is sourced in the `source` argument of a specific belief. This property is used to indicate the source of the information, i.e. the agent that provided the information."]}, {"cell_type": "markdown", "id": "d742bd3d-614a-4861-ae55-567896a555dc", "metadata": {}, "source": ["| Ejer<PERSON>cio 6 --- OPCIONAL |\n", "|------------:|\n", "| Modifica el ejemplo de `BOWLDI` para que sea un diálogo entre dos agentes `SPADE``.|"]}, {"cell_type": "markdown", "id": "580448c3", "metadata": {}, "source": ["## Example 5: Loading rendered AgentSpeak code into SPADE"]}, {"cell_type": "code", "execution_count": null, "id": "c2a2c6b3", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import time\n", "import spade\n", "from spade_bdi.bdi import BDIAgent\n", "\n", "a = BDIAgent(\"BasicAgent_BDI@localhost\", \"SPADE\", \"output.asl\")\n", "\n", "await a.start()\n", "await asyncio.sleep(1)\n", "\n", "a.bdi.print_beliefs()\n", "\n", "time.sleep(1)\n", "await a.stop()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "bowldiclass", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}