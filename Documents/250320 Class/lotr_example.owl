<?xml version="1.0"?>
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:owl="http://www.w3.org/2002/07/owl#"
         xml:base="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250320%20Class/lotr_example.owl"
         xmlns="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250320%20Class/lotr_example.owl#">

<owl:Ontology rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Documents/250320%20Class/lotr_example.owl"/>

<owl:ObjectProperty rdf:about="#has_king">
  <rdfs:domain rdf:resource="#kingdom"/>
  <rdfs:range rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">has king</rdfs:label>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_friend_with">
  <rdfs:domain rdf:resource="#person"/>
  <rdfs:range rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">is friend with</rdfs:label>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#is_in_team_with">
  <rdfs:domain rdf:resource="#person"/>
  <rdfs:range rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">is in team with</rdfs:label>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#has_ring">
  <rdfs:domain rdf:resource="#person"/>
  <rdfs:range rdf:resource="#ring_of_power"/>
  <rdfs:label xml:lang="en-gb">has ring</rdfs:label>
</owl:ObjectProperty>

<owl:ObjectProperty rdf:about="#fights_against">
  <rdfs:domain rdf:resource="#person"/>
  <rdfs:range rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">fights against</rdfs:label>
</owl:ObjectProperty>

<owl:DatatypeProperty rdf:about="#has_name">
  <rdfs:domain rdf:resource="#person"/>
  <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
  <rdfs:label xml:lang="en-gb">has name</rdfs:label>
</owl:DatatypeProperty>

<owl:DatatypeProperty rdf:about="#description">
  <rdfs:domain rdf:resource="#ring_of_power"/>
  <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
  <rdfs:label xml:lang="en-gb">description</rdfs:label>
</owl:DatatypeProperty>

<owl:Class rdf:about="#person">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">person</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#wizard">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">wizard</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#elf">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">elf</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#human">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">human</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#dwarf">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">dwarf</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#hobbit">
  <rdfs:subClassOf rdf:resource="#person"/>
  <rdfs:label xml:lang="en-gb">hobbit</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#kingdom">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">kingdom</rdfs:label>
</owl:Class>

<owl:Class rdf:about="#ring_of_power">
  <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
  <rdfs:label xml:lang="en-gb">ring</rdfs:label>
</owl:Class>

</rdf:RDF>
