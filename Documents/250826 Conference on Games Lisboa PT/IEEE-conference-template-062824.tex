\documentclass[conference]{IEEEtran}
% \documentclass[peerreviewca]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
%Template version as of 6/27/2024

\usepackage[T1]{fontenc}
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{csquotes}
% \def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{Using Gamification to Influence Non-Player Character's Artificial Agent Behaviour\\
% \thanks{This research is supported by the project MOBODL-2023-08-5618 funded by the European Union and the Croatian Science Foundation.}
}

% \author{\IEEEauthorblockN{<PERSON><PERSON><PERSON>\v{s}a \DJ uri\'{c}}
% \IEEEauthorblockA{\textit{Artificial Intelligence Laboratory} \\
% \textit{University of Zagreb Faculty of Organization and Informatics}\\
% Vara\v{z}din, Croatia \\
% email <EMAIL>; ORCID 0000-0001-5653-6646}
% }

\maketitle
% \IEEEpeerreviewmaketitle

\begin{abstract}
    Creating believable and engaging non-player characters (NPCs) in video games requires sophisticated techniques to influence their behaviours. This paper introduces a method that leverages the Big Five personality traits to psychologically profile artificial agents, subsequently using tailored gamification techniques to shape their behaviour. By correlating personality traits, e.g. extraversion or conscientiousness, with appropriate gamification strategies, we propose a systematic framework that enhances NPC interactions, aligning them dynamically with the game's narrative and mechanics. Unlike traditional approaches reliant on scripted behaviours or computationally intensive reinforcement learning, this method provides immediate, nuanced behavioural adaptations suitable for real-time gaming scenarios. We outline the theoretical basis, discuss possible gamification mechanisms, and illustrate use cases ranging from quest-giving NPCs to enemy characters. This approach is argued to enhance gaming experiences and have implications for broader agent-based social simulations.
\end{abstract}

\begin{IEEEkeywords}
non-player characters, artificial agents, multiagent systems, gamification, personality traits, large language models.
\end{IEEEkeywords}

\section{Introduction}

Video games are a great application domain for multiagent systems, comprising multiple autonomous agents interacting with each other and their own environment. An artificial agent is defined as anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators \cite[p. 54]{russell2022ArtificialIntelligenceModern}. In video games, artificial agents may be viewed as the non-player characters (NPCs) that populate the game world and interact with the player and other NPCs. The behaviour of these agents is often scripted, i.e. predefined sequences of actions that the agent performs in response to specific events, such as patrolling the grounds while the player is out of sight. However, in recent years, there has been a growing interest in creating more dynamic and adaptive NPCs that can exhibit emergent behaviour and respond to the player and other NPCs more naturally and unpredictably. The stated can be achieved by equipping NPCs with artificial intelligence (AI) capabilities, such as machine learning and decision-making algorithms that allow them to learn from their experiences and adapt their behaviour accordingly.

\section{Related Work}

The recent advent of large language models (LLMs) and the accompanying research has opened up new possibilities for creating more dynamic and adaptive NPCs. LLMs can generate human-like text based on the input they receive, and they can be used to generate the dialogue and behaviour of NPCs in a game. The emergence of reasoning LLMs \cite{ferrag2025LLMReasoningAutonomous,zhang2024LLMMastermindSurvey} further improved the possibilities of using such models to generate logical and coherent behaviour for NPCs. However, using LLMs in game development is still in its early stages, and several challenges need to be addressed \cite{gallotta2024LargeLanguageModels}. One of the main challenges is the lack of control over the generated content, which can lead to NPCs exhibiting behaviour not aligned with the game's narrative or mechanics. Another challenge is the computational cost of using LLMs, which can be prohibitive for real-time applications.

One way of influencing agents' behaviour is reinforcement learning, i.e. providing rewards and punishments for specific actions \cite{sukmana2024ReinforcementLearningAI}. Such an approach can shape the behaviour of NPCs in a game, making them more aligned with the game's narrative and mechanics. However, designing efficient utility functions is challenging, and it is often difficult to achieve the desired behaviour without resorting to trial and error or relying on human feedback and long training times.

Another way of influencing the behaviour of agents is through the use of gamification techniques. Gamification uses game elements in non-game contexts  \cite{deterding2011GameDesignElements,matallaoui2017IntroductionGamificationFoundation}. In the context of artificial agents, gamification can shape NPCs' behaviour by providing them with rewards and punishments for specific actions, similar to reinforcement learning. However, gamification can also be used to influence the behaviour of NPCs more subtly by providing them with incentives aligned with the game's narrative and mechanics. For example, an NPC might be rewarded for exhibiting bravery or loyalty. The described can be achieved using gamification techniques such as leaderboards, badges, challenges, and many more \cite{mora2017GamificationSystematicReview}, which can motivate NPCs to exhibit certain behaviours.

Since gamification is heavily based on psychological research and how humans respond to certain stimuli, it is a challenging way of influencing the behaviour of artificial agents. There is a lack of research on applying gamification techniques effectively to artificial agents. Since artificial agents do not contain a psychological profile and do not inherently behave in a way aligned with human psychology, it is difficult to determine which gamification techniques would effectively influence their behaviour. However, humans can be psychologically profiled. There are multiple approaches to dispositional theory or trait theory, i.e. the approach to the study of human personality. 

Using the Big Five personality traits is a prevalent method \cite{cummings2019IntroductionPsychology}. According to this approach, there are five personality traits: extraversion, agreeableness, conscientiousness, emotional stability, and openness to experience. Every personality trait can be further detailed using trait facets. According to \cite{mccrae1989ReinterpretingMyersBriggsType}, each trait can be described using 5--6 facets that capture the nuance of personality. For example, the extraversion trait can be described using the following facets: warmth, gregariousness, assertiveness, activity, excitement-seeking, positive emotions \cite{mccrae1989ReinterpretingMyersBriggsType,cummings2019IntroductionPsychology}. 

Humans and their psychological profiles can be used to determine which gamification techniques would be effective in influencing their behaviour \cite{denden2024OpeningGamificationBlack, kirchner-krath2024UncoveringTheoreticalBasis, oliveira2019TailoredGamificationEducational}. 
Artificial agents have been psychologically profiled, e.g. \cite{castelfranchi1998PersonalityTraitsSocial, ren2025ImpactBigFive}. With the power of LLMs, it is possible to influence the behaviour of artificial agents powered by LLMs using specific details in system prompts. Such prompts often include instructions like \enquote{Act as a loyal friend.} or \enquote{Act as an experienced professional psychiatrist.} that are used to provide the agent with a more detailed personality requested by the user. Using similar but more complex prompts, developers create LLMs with customised behaviour, e.g. \cite{nie2024LLMbasedConversationalAI}.



\section{Proposed Approach}

It is argued that artificial agents can be psychologically profiled using the Big Five personality traits and their facets. This psychological profile can then be used to determine which gamification techniques would effectively influence the behaviour of each particular agent. Gamification techniques can then be used to shape the behaviour of the agent.



\subsection{Defining Personality Traits of Agents}

Without the power of LLMs, agents developed using agent implementation frameworks such as Smart Python Agent Development Environment (SPADE) \cite{gregori2006JabberbasedMultiagentSystem, palanca2020SPADE3Supporting}, may be modelled using traits expressed as a vector. Such a vector would feature as many values as the number of all the facets of all the traits in the chosen model. Such a vector can be used to define an agent's personality traits and personality profiles. Each vector value may be expressed using a number $[0, 1]$, where -1 represents the absence of the trait facet, and 1 represents the presence of the trait facet. Other approaches have been used in published literature, e.g. using machine learning methods to emulate personality traits \cite{liapis2024MachineLearningMethods}.

Consider $\Phi$ a set of all trait facets $\phi$ used to describe personality traits, and $\alpha$ is an agent in $\mathcal{A}$. The personality profile of the agent can be defined as a vector such as the following:
\begin{equation}
    \label{eq: personality Profile}
    \vec{\alpha}_{1} = \begin{bmatrix}x_{\phi_1}, \ldots, x_{\phi_{29}}\end{bmatrix}: \phi_{1 \ldots 29} \in \Phi \wedge x = [0, 1] \wedge \alpha_{1} \in \mathcal{A}
\end{equation}

Since not all the behaviour can be modelled using personality traits only, an additional value (shown here as $\chi$) is added to the definition of an agent's personality, representing other factors, such as the agent's experience, rebelliousness, etc. Therefore, the personality profile of the agent can be defined as follows:
\begin{equation}
    \label{eq: personality Profile extended}
    \vec{\alpha}_{1 \ldots n} = (\chi, \begin{bmatrix}x_{\phi_1}, \ldots, x_{\phi_{29}}\end{bmatrix})
\end{equation}

where $\phi_{1 \ldots 29} \in \Phi$ with values $x = [0, 1]$, and $\alpha_{1 \ldots n} \in \mathcal{A}$.

If an LLM powers the artificial agent, the system prompt should contain the data on the presence of each trait facet and thus define the personality profile of the agent. The output provided by the LLM, i.e. the behaviour of the artificial agent, should be influenced by the defined trait facets.



\subsection{Gamification Techniques}

Gamification methods and techniques can be profiled based on the personality traits they influence the most. Some gamification techniques may be more effective in influencing the behaviour of agents with certain personality traits \cite{ertansevk2024ReviewPersonalizationGamified, kirchner-krath2024UncoveringTheoreticalBasis, phosanarack2025UserCenteredPersonalizedGamification, ray2024ImpactPersonalityTraits}. For example, leaderboards may be more effective in influencing the behaviour of agents with a high extraversion trait. At the same time, badges may be more effective in influencing the behaviour of agents with a high agreeableness trait.

The initial version of the approach proposed herein should include information on gamification techniques and their influence on the behaviour of agents with certain personality traits. These values shall be defined based on the available published review papers. Each identified gamification technique should be assigned a value for each trait facet, representing how applicable the use of the gamification technique is to the behaviour of agents with the given trait facet. In order to make these values comparable to the personality profiles of agents, they should be expressed as a vector of the same length as the personality profile vector of the agent, i.e. a vector containing a value for each of the trait facets, following \eqref{eq: personality Profile}. Further research and literature review should be conducted to formulate the more precise values of each trait facet for each gamification technique included in the system. The next step would be to implement the approach in a proof-of-concept system and evaluate its effectiveness.



\subsection{Determining the Effectiveness of Gamification Techniques}

The distance between the two expressions is to be calculated when determining how effective a gamification technique would be for a specific agent. The distance can be calculated using various methods, such as Euclidean distance, Manhattan distance, or cosine similarity. The method to be used should be determined based on the system's specific requirements. Depending on how the distance is calculated, the gamification techniques can be sorted in ascending or descending order, providing a list of the most effective gamification techniques for a specific agent, i.e., gamification techniques expected to influence the agent's behaviour the most.

The information on gamification techniques and their influence on the behaviour of agents with certain personality traits can be represented as a knowledge graph, where each gamification technique is a node, and the edges between the nodes represent the influence of one gamification technique on the behaviour of agents with certain personality traits. The values representing the influence of each gamification technique on the behaviour of agents with certain personality traits can be represented as weights of the edges. Alternatively, the information can be represented as a matrix, where the rows represent the gamification techniques, the columns represent the personality traits, and the values in the matrix represent the influence of each gamification technique on the behaviour of agents with certain personality traits. Finally, the information can be modelled as an ontology, where the gamification techniques are represented as classes, and the personality traits are represented as properties of the classes. The latter would allow reasoning based on the information, e.g., determining which gamification techniques are most effective for agents with certain personality traits. Additionally, storing such information in an ontology would provide a highly structured data source for LLMs to consume and use.



\section{Use Cases}

Modelling NPCs using this approach in an adventure game might allow the implementation of an NPC that will give quests for the player based on the subject NPC's traits. For example, an NPC with a high extraversion trait might be likelier to give quests involving social interaction. In contrast, an NPC with a high agreeableness trait might be likelier to give quests that involve helping others. An example of NPC could be a smith to exemplify this use case further. They are given a simple gamified system where they unlock new recipes after a specific increasing number of quests given to and solved by the player. The quests this NPC gave could be of different types, e.g. slaying a monster, gathering resources, helping another NPC, etc. The smith with a high extraversion trait would be more likely to give quests that involve social interaction, such as solving a quest of another NPC. Conversely, the smith with a high agreeableness trait might be more likely to give quests that involve helping others, such as gathering resources. The smith could also have a high conscientiousness trait, making them more likely to give quests that involve a certain level of effort, such as slaying a monster. With a high emotional stability trait, the smith would be more likely to give quests that are not too dangerous. Finally, the smith with high openness to experience trait might be more likely to give unique and non-repetitive quests.

In a role-playing game, bard characters might be incentivised to meet new characters across the in-game world because the more characters they know, the more patrons they might have, and thus the more gold they might earn. Some bard characters might join the player's party and thus travel and be able to meet new characters more easily. Others might prefer to stay in their home town and meet new characters by chance. Others yet might not care about making money at all.

In a combat- or war-based game, enemy NPCs might behave differently based on the active rewards system. Some enemies might be more eager to enter combat and fight players and other NPCs if the reward is monetary. Others might be more willing if the combat is challenging, regardless of the expected and awarded achievement. Others might still be more motivated by the challenge of combat rather than the reward. Finally, some enemy NPCs might be more interested in the social aspects of the game and might be more willing to engage in combat if it means they can interact with other characters, e.g. by recruiting them to their side or teaming up with other enemy NPCs.



\section{Discussion}

% How is this related to NPCs and video games?

Influencing the behaviour of NPCs in video games is a challenging task. The proposed approach might be used to influence the behaviour of NPCs in video games by using gamification techniques that are most effective for the personality traits of the NPC. This approach can make the NPCs more believable and engaging for the players. As opposed to machine learning methods, such as reinforcement learning, the proposed approach does not require extensive training and can be used to influence the behaviour of NPCs in real time. Furthermore, it might be possible to sway the behaviour of NPCs in a more natural and nuanced way than simply providing rewards and punishments for specific actions.

The idea of modifying the behaviour of NPCs through time and having them \enquote{live} their lives and evolve is not new \textit{per se}, as shown, e.g. in Middle Earth: Shadow of War (2017) developed by Monolith Productions using the Nemesys System \cite{parosu2022NemesisSystemHow}. The NPCs have their own goals, motivations, and agendas, and they interact with each other and the player's character in complex ways. The NPCs are not just mindless drones; they have their own personalities and quirks, making them more memorable and engaging for the players.

Though the simplest gamification techniques are points, badges, and leaderboards, some more complex techniques can influence NPCs' behaviour. For example, the NPCs might be given quests to complete, and the nature of the quests might be adjusted based on the personality traits of the NPC. The NPCs might change their behaviour on how prominent their role is in the ever-changing story of the in-game world they are a part of, based on the player's interaction with the said world. Finally, NPCs might be able to form alliances with other NPCs.

In a different approach, the proposed system might be used to determine how effective a gamification technique would be for a specific agent and which gamification techniques would be effective in influencing the behaviour of a group of agents with certain personality traits. This approach might be helpful in a game where the player's character is a leader of a group of NPCs, and the player must choose the most effective gamification techniques to influence the behaviour of the group of NPCs. For example, the player might want to incentivise the group of NPCs to explore a new area of the game world. The player might use the proposed system to determine which gamification techniques would be most effective in influencing the behaviour of the group of NPCs based on the personality traits of the NPCs.

Furthermore, this approach might make simulating social behaviour and interaction between agents using games easier, bringing life beyond rules to social simulations. 

\section{Conclusion}

This paper explores an approach to influencing the behaviour of artificial agents powering NPCs in video games by applying gamification techniques and psychologically profiling agents based on the Big Five personality traits and their associated facets. By associating gamification techniques with personality profiles, developers can effectively tailor NPC behaviour, possibly resulting in more dynamic, believable, and engaging game interactions. Unlike reinforcement learning, this method is expected not to require extensive training and allow for real-time behavioural adaptation aligned with game narratives and mechanics. 

Future work involves constructing precise mappings of gamification techniques to personality traits, developing proof-of-concept implementations, and systematically evaluating their effectiveness. Such an approach is argued to hold potential for enriching player experiences in video games and enhancing social simulation models by providing nuanced control over agent behaviours.


% \clearpage
\IEEEtriggeratref{8}
\bibliographystyle{IEEEtran}
\bibliography{ref.bib}


\end{document}
