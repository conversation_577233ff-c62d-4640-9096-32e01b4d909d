% TikZ diagram of the proposed personality-driven gamification system
\documentclass{standalone}
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows, positioning}

% Define styles
\tikzstyle{block} = [rectangle, draw, fill=blue!20, text width=6em, text centered, rounded corners, minimum height=3em]
\tikzstyle{db} = [cylinder, cylinder uses custom fill, cylinder body fill=green!20, draw, shape border rotate=90, aspect=0.25, text centered, minimum height=3em]
\tikzstyle{process} = [rectangle, draw, fill=orange!20, text width=8em, text centered, rounded corners, minimum height=3em]
\tikzstyle{arrow} = [thick,->,>=stealth]

\begin{document}
\begin{tikzpicture}[node distance=2cm, auto]
  % Nodes
  \node [block] (agent) {Artificial Agent\\(Traits + $\chi$ Profile)};
  \node [db, right=of agent] (tech) {Gamification Techniques\\(Facet-weight vectors)};
  \node [process, below=of agent, xshift=2cm] (distance) {Distance Calculation\\(e.g. Euclidean)};
  \node [process, below=of distance] (rank) {Rank Techniques\\by Similarity/Distance};
  \node [block, below=of tech, yshift=-2cm] (apply) {Apply Top Techniques\\to Shape Behaviour};
  \node [block, below=of apply] (behavior) {Agent Behaviour\\(Aligned with Narrative)};

  % Arrows
  \draw [arrow] (agent) -- (distance);
  \draw [arrow] (tech) -- (distance);
  \draw [arrow] (distance) -- (rank);
  \draw [arrow] (rank) -- (apply);
  \draw [arrow] (apply) -- (behavior);

  % LLM-powered branch
  \node [block, left=of behavior, xshift=-2cm] (llm) {LLM Engine\\(System Prompt with Profile)};
  \draw [arrow] (agent.west) -| node [near start, left] {if LLM-powered} (llm.north);
  \draw [arrow] (llm) -- (behavior.west);

  % Optional knowledge representation
  \node [db, right=of tech, xshift=1cm] (kg) {Knowledge Base\\(Graph / Matrix / Ontology)};
  \draw [arrow, dashed] (tech) -- (kg) node[midway, above] {store mappings};
  \draw [arrow, dashed] (kg) -- (distance) node[midway, right] {retrieve weights};

\end{tikzpicture}
\end{document}
