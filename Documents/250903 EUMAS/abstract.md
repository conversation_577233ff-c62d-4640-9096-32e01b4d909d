# Abstract

The behaviour of intelligent autonomous agents is often trained using reinforcement learning, i.e., providing rewards and punishments for specific actions. However, designing efficient utility functions is challenging, and the training process is time-consuming. Meanwhile, behavioural adaptation in humans is efficiently influenced by gamification, i.e. the application of game design elements to non-game contexts. Research shows that gamification techniques can be profiled based on the personality traits they influence the most. This paper explores an approach to psychologically profiling agents based on the Five-Factor Model of personality traits and their associated facets, as well as influencing their behaviour by adjusting their behaviour through gamification mappings. By associating gamification techniques with personality profiles, developers can effectively modulate agent behaviour in real-time, possibly resulting in more dynamic, believable, and engaging agent interactions. Unlike reinforcement learning, this method is expected not to require extensive training and to allow for more subtle real-time behavioural adaptation. The strength of the change is expected to depend on the techniques' and agents' psychological profiles. Two scenarios are provided to demonstrate this approach. The first is an implementation based on the recipeWorld, featuring agents that may be abstracted to service consumers and providers. The second use case is based on a more specific and real-life scenario that affects the choice of a large language model based on the provided task. The proposed approach is argued to hold potential for enriching agent-based simulations and enhancing social simulation models by providing nuanced control over agent behaviours.
