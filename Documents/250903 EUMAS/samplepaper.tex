% This is samplepaper.tex, a sample chapter demonstrating the% LLNCS macro package for Springer Computer Science proceedings;
% Version 2.21 of 2022/01/12
%
\documentclass[runningheads]{llncs}
%
\usepackage[T1]{fontenc}
% T1 fonts will be used to generate the final print and online PDFs,
% so please use T1 fonts in your manuscript whenever possible.
% Other font encondings may result in incorrect characters.
%
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{csquotes}
\usepackage{hyperref}
\usepackage{listings}
\lstset{
    basicstyle=\ttfamily\scriptsize,
    numbers=left,
    numberstyle=\tiny,%\color{gray},
    numbersep=5pt,
    frame=tb,
    breaklines=true,
    breakatwhitespace=false,
    showstringspaces=false,
    captionpos=b,
    tabsize=2,
}
% Used for displaying a sample figure. If possible, figure files should
% be included in EPS format.
%
% If you use the hyperref package, please uncomment the following two lines
% to display URLs in blue roman font according to Springer's eBook style:
\usepackage{color}
\renewcommand\UrlFont{\color{blue}\rmfamily}
\urlstyle{rm}
%
\begin{document}
%
\title{Dynamic Behavioural Adaptation of Agents via Personality-Based Gamification Techniques}
%
%\titlerunning{Abbreviated paper title}
% If the paper title is too long for the running head, you can set
% an abbreviated paper title here
%
\author{Bogdan Okreša Đurić\inst{1}\orcidID{0000-0001-5653-6646} \and
Tomislav Peharda\inst{1}\orcidID{0000-0002-2081-4816} \and
Markus Schatten\inst{1}\orcidID{0000-0001-6910-8675}}
%
\authorrunning{B. Okreša Đurić et al.}
% First names are abbreviated in the running head.
% If there are more than two authors, 'et al.' is used.
%
\institute{University of Zagreb Faculty of Organization and Informatics,\\Pavlinska 2, 42000 Varaždin, Croatia\\
\email{\{dokresa,tpeharda,mschatte\}@foi.unizg.hr}\\
\url{https://www.foi.unizg.hr}}
%
\maketitle              % typeset the header of the contribution
%
\begin{abstract}
The behaviour of intelligent autonomous agents is often trained using reinforcement learning, i.e., providing rewards and punishments for specific actions. However, designing efficient utility functions is challenging, and the training process is time-consuming. Meanwhile, behavioural adaptation in humans is efficiently influenced by gamification, i.e. the application of game design elements to non-game contexts. Research shows that gamification techniques can be profiled based on the personality traits they influence the most. This paper explores an approach to psychologically profiling agents based on the Five-Factor Model of personality traits and their associated facets, as well as influencing their behaviour by adjusting their behaviour through gamification mappings. By associating gamification techniques with personality profiles, developers can effectively modulate agent behaviour in real-time, possibly resulting in more dynamic, believable, and engaging agent interactions. Unlike reinforcement learning, this method is expected not to require extensive training and to allow for more subtle real-time behavioural adaptation. The strength of the change is expected to depend on the techniques' and agents' psychological profiles. Two scenarios are provided to demonstrate this approach. The first is an implementation based on the recipeWorld, featuring agents that may be abstracted to service consumers and providers. The second use case is based on a more specific and real-life scenario that affects the choice of a large language model based on the provided task. The proposed approach is argued to hold potential for enriching agent-based simulations and enhancing social simulation models by providing nuanced control over agent behaviours.

\keywords{multiagent systems \and gamification \and behavioural adaptation \and five-factor model \and agent-based modelling \and psychological profiling.}
\end{abstract}
%
%
%
\section{Introduction}\label{sec: intro}

Multiagent systems (MAS) consist of autonomous agents that interact and collaborate to accomplish complex goals that exceed the capabilities of any single agent. Traditional methods for shaping agent behaviour predominantly utilize reinforcement learning, which requires extensive training and carefully designed reward structures. Although these approaches can be effective, they tend to be time-consuming, resource-intensive, and often lack the nuanced adaptability needed for real-time scenarios.

Recent advancements emphasize the potential of gamification—the use of game mechanics in non-game environments—to efficiently and effectively influence human behaviour. Notably, the impact of gamification techniques varies according to personality traits, indicating that similar principles might enhance agent behaviour within MASs. This paper proposes the integration of gamification strategies with personality profiling grounded in the well-established Five-Factor Model.

The approach seeks to facilitate dynamic, authentic, and personalized interactions with agents without relying on extensive pre-training. This is achieved by linking specific gamification techniques to distinct agent personality profiles. Through meticulously designed scenarios, which encompass both abstract service-oriented interactions and practical applications involving large language models (LLMs), this paper assesses the feasibility and effectiveness of personality-based gamification for behavioural adaptation in MASs. The potential implications range from enhanced realism in agent-based simulations to more enriching user-agent interactions across various domains.

\section{Related Work}\label{sec: related work}

% Većinu ovdje referenciranih radova stavio sam ovdje:
% \begin{itemize}
%     \item \url{https://www.jottacloud.com/s/3889a13ac6d77b94055834323d56702fbef}
% \end{itemize}

An agent is an autonomous software entity that observes its environment and takes actions to achieve defined goals. It functions independently, makes decisions based on its perceptions, and adapts to changes without needing constant human input. A multi-agent system (MAS) is a group of such agents that interact with each other, either through cooperation or competition, to solve problems that are too complex for a single agent. These systems depend on coordination, communication, and negotiation among agents to fulfil both individual and collective tasks. MAS is commonly used in fields like robotics, distributed computing, simulations, and intelligent systems \cite{russell2022ArtificialIntelligenceModern,wooldridge2009IntroductionMultiAgentSystems}.

Personality refers to the consistent patterns in how individuals think, feel, and behave. It shapes how people interact with the world and others around them. The most widely accepted model in psychology is the Five-Factor Model, which organises personality into five dimensions: Extraversion, Agreeableness, Conscientiousness, Neuroticism, and Openness to Experience. These traits exist on a spectrum and offer a nuanced view of human differences. The Myers-Briggs Type Indicator (MBTI) is another popular model that classifies people into 16 types based on four binary preferences, though research suggests it oversimplifies personality by forcing continuous traits into fixed categories \cite{cummings2019IntroductionPsychology,mccrae1989ReinterpretingMyersBriggsType,mccrae2006PersonalityAdulthoodFiveFactor}.

Personality is typically measured through self-report questionnaires such as the NEO Personality Inventory or the MBTI. While these methods are common, they can feel repetitive or rigid. Newer approaches are using technology to make assessment more engaging and accessible. For example, the PsychoGAT framework transforms personality tests into interactive fiction games, where player choices reflect underlying psychological traits. Powered by large language models, this method maintains scientific reliability while making the experience more immersive and user-friendly \cite{cummings2019IntroductionPsychology,mccrae1989ReinterpretingMyersBriggsType,yang2024PsychoGATNovelPsychological}.

Recent research has explored how embedding personality traits into AI agents affects their behaviour and decision-making in social and strategic contexts. In simulated classroom environments, agents with differing personalities showed clear variation in how they responded to misinformation. For instance, agents with traits aligned to curiosity were more receptive to new information, while those with more cautious traits tended to reject it. Other traits influenced not only what agents chose to say publicly, but also how they privately evaluated information, revealing internal-external mismatches driven by social dynamics. Traits associated with sociability and friendliness were particularly prone to expressing views that differed from internal beliefs, highlighting how the environment can influence outward behaviour \cite{ren2025impact,ong2025identifying}.

In strategic multi-agent scenarios, such as repeated cooperation games, steering agents toward specific personality profiles led to distinct behavioural outcomes. Agents oriented toward long-term planning and empathy demonstrated greater willingness to cooperate and less inclination to deceive. However, this also made them more susceptible to exploitation by less cooperative agents. These findings suggest that while personality shaping can promote prosocial behaviour and group benefit, it comes with trade-offs that must be carefully managed when deploying AI agents in dynamic or adversarial environments \cite{ren2025impact,ong2025identifying}.

Gamification is the practice of applying game design elements such as points, levels, badges, or leaderboards in settings that are not traditionally games, with the goal of boosting user engagement, motivation, and participation. Instead of developing complete games, it integrates features commonly found in games into areas like education, healthcare, or workplace systems. Unlike playful design, which focuses on open-ended exploration, gamification encourages structured and goal-driven interaction. This approach highlights the value of game-like experiences in everyday activities by creating motivation through clear rules and rewards \cite{deterding2011GameDesignElements,deterding2011GamificationDefinition}.

Gamification is increasingly applied in education and corporate training to enhance engagement, motivation, and performance. In science education, it supports active learning by integrating game elements such as points, badges, quizzes, and progress bars into lessons. These features help students explore scientific concepts in interactive ways, often through virtual labs or simulations that allow for experimentation without real-world risks. Tools like Kahoot, ClassDojo, and custom-developed platforms are commonly used to promote participation, provide immediate feedback, and encourage collaborative problem-solving \cite{iacono2020GamificationCorporateTraining}.

In corporate environments, gamification addresses the challenge of low engagement in training programs. Various tech-first enterprise companies have adopted game mechanics to make learning more appealing and goal-oriented. These include weekly-reset leaderboards, mission-based tasks, and personalised progress tracking. By tapping into motivational drivers like achievement, competition, and autonomy, gamified training has led to improved retention, increased course completion rates, and greater overall participation \cite{kalogiannakis2021GamificationScienceEducation,matallaoui2017IntroductionGamificationFoundation}.

Tailored gamification focuses on adapting game elements to suit individual users' traits, preferences, and behaviors. Unlike generic systems that offer the same experience to everyone, tailored gamification uses personalisation, adaptation, or recommendation strategies grounded in user models. These models account for factors such as player types, personality, and performance history. For instance, a competitive user might respond well to leaderboards, while a more exploratory user may prefer open-ended challenges or achievement badges. In educational settings, this approach helps align the system with diverse learner needs, increasing motivation and reducing disengagement \cite{khoshkangini2021AutomaticGenerationRecommendation,klock2020TailoredGamificationReview}.
Beyond static customisation, more advanced systems now generate content dynamically in response to real-time user data. In domains such as smart cities, personalised challenges have been created and assigned automatically, improving long-term engagement and behaviour change. \cite{klock2020TailoredGamificationReview,oliveira2023TailoredGamificationEducation}.

Gamification based on personality focuses on aligning game elements with individual traits to improve engagement and motivation. Different personalities respond to game mechanics in distinct ways. Extraverts are more motivated by elements like points, levels, and leaderboards because they enjoy competition and public recognition. They tend to seek excitement and are drawn to visible achievements and social comparison. In contrast, introverts prefer mechanics that allow for self-paced interaction and private feedback, such as progress bars and personal goal setting. They are less motivated by public exposure and more by internal satisfaction. Badges and rewards also show varying effects. Extraverts find them more enjoyable when delivered in social contexts, while introverts respond more positively when these elements are tied to personal achievement. People high in openness often find standard avatar implementations unoriginal and disengaging. Those with higher emotional stability tend to be less influenced by gamified elements overall, showing lower interest in external motivators like points or badges. Designing gamified systems with these differences in mind leads to more personalised and effective user experiences by matching game mechanics to individual motivational patterns \cite{codish2014personality,jia2016personality,denden2021EffectsGenderPersonality,denden2022UseNotUse,denden2024OpeningGamificationBlack,limonova2022ImpactPersonalityGamification,ray2024ImpactPersonalityTraits}.

Agent-based systems can enhance gamification by enabling adaptive, personalised, and responsive experiences. Intelligent agents are autonomous computational entities that observe, reason, and act within an environment. When integrated into gamified platforms, they can dynamically adjust challenges, feedback, and rewards based on individual user behaviour. These agents monitor engagement and performance in real time, allowing the system to maintain an optimal level of difficulty and motivation. This is essential for keeping users within their flow state.
In educational contexts, agents support tailored gamification by aligning tasks with learner preferences, skill levels, and motivational states. They can implement decision models that predict disengagement and proactively trigger interventions such as bonus rewards or modified tasks. Multi-agent systems can further coordinate different functions, including guiding the learner, tracking progress, and optimising incentive structures. This modular and flexible architecture supports scalable personalisation and helps sustain long-term engagement across diverse user groups \cite{wlodarski2025LevelPeerReview,costa2024GamificationAIEnhancing}.



\section{The Proposed Approach}\label{sec: theory}

The approach presented in this paper seeks to provide a system of artificial agents whose behaviour is influenced by gamification techniques, based on their psychological profile using personality facets. The agents are psychologically profiled using the Big Five personality traits and their associated facets. The gamification techniques are associated with personality profiles, enabling the effective tailoring of agent behaviour. Furthermore, agent personality affects some other choices the agents make, such as the prices they are willing to accept.

In this paper, we present the formalised theoretical framework for the proposed approach. The framework is implemented and evaluated in a simulated environment. The evaluation results are presented and discussed in the context of the proposed approach.



\subsection{Theoretical Foundation of the Proposal and Formalisation}\label{subsec:GamificationFormalisation}

The foundational elements of this system are agents, personality traits and facets, and gamification techniques. Agents are described using personality traits, which are in turn described using personality facets. Gamification techniques are related to specific personality profiles also described using personality facets. The compatibility between gamification techniques and agents is determined based on the compatibility between their personality profiles. In this context, compatibility denotes the degree to which a gamification technique is suitable for an agent, based on their personality, i.e. how effective is a gamification technique in influencing the behaviour of an agent with a specific personality. Therefore, some agent are more susceptible to the influence of a gamification technique, while others are not.

Let \(\mathcal{A} = \{\alpha_1, \alpha_2, \dots, \alpha_n\}\) be the set of all agents, where each \(\alpha_i \in \mathcal{A}\) represents an individual agent.

The set of personality facets constituting a personality profile is chosen based on the research of McCrae and Costa \cite{mccrae2006PersonalityAdulthoodFiveFactor}. The five personality traits in their Five-Factor Model are openness, conscientiousness, extraversion, agreeableness, and neuroticism. Each of these traits is further divided into six facets that influence each trait:

\begin{itemize}
    \item Openness: Fantasy, Aesthetics, Feelings, Actions, Ideas, Values;
    \item Conscientiousness: Competence, Order, Dutifulness, Achievement striving, Self-Discipline, Deliberation;
    \item Extraversion: Warmth, Gregariousness, Assertiveness, Activity, Excitement seeking, Positive emotions;
    \item Agreeableness: Trust, Straightforwardness, Altruism, Compliance, Modesty, Tender-mindedness;
    \item Neuroticism: Anxiety, Angry hostility, Depression, Self-Consciousness, Impulsiveness, Vulnerability.
\end{itemize}

For example, an agent with a high score in the \enquote{Warmth} facet of the \enquote{Extraversion} trait is more likely to be influenced by gamification techniques that rely on social interaction. On the other hand, an agent with a high score in the \enquote{Anxiety} facet of the \enquote{Neuroticism} trait is more likely to be influenced by gamification techniques that rely on individual performance.

Let \(\Phi = \{\phi_1, \phi_2, \dots, \phi_{30}\}\) be the set of all personality facets, where each \(\phi_i \in \Phi\) corresponds to a distinct facet (e.g., \emph{Feelings} is \(\phi_3\), \emph{Vulnerability} is \(\phi_{30}\)).

Let \(\Psi = \{\vec{\psi}_1, \vec{\psi}_2, \dots, \vec{\psi}_m\}\) be the set of all personality profiles, where each \(\vec{\psi}_i \in \Psi\) represents a unique personality profile. Personality profiles are mined from empirical data and are not necessarily unique to each agent, so \(m\) need not equal the number of agents \(n\). A personality profile \(\vec{\psi}_i\) represents an individual personality instance as a 30-dimensional real-valued vector \eqref{eq: personality_profile}, where each component \(x_{\phi_i} \in [0,1]\) indicates the intensity of the corresponding facet \(\phi_i\).

\begin{equation}
    \label{eq: personality_profile}
    \vec{\psi}_i = \begin{bmatrix}x_{\phi_1}, x_{\phi_2}, \ldots, x_{\phi_{30}}\end{bmatrix} \in [0,1]^{30}
\end{equation}

Each agent $\alpha_i \in \mathcal{A}$ is given a personality profile $\vec{\psi}_i \in [0,1]^{30}$. However, personality is not a simple concept to model. To account for additional factors, e.g. experience or rebelliousness, a scalar value $\chi_i \in [0,2]$ is added. Thus, the overall personality representation of an agent \(i\), i.e. \(p_i\), is defined as a tuple \eqref{eq: agent_personality}.

\begin{equation}
    \label{eq: agent_personality}
    p_i = (\chi_i, \vec{\psi}_i)
\end{equation}

The personality modifier \(\chi_i\) acts as a scaling factor that can either reduce (\(< 1\)) or enhance (\(> 1\)) the compatibility score.
For the sake of simplicity, we assume that the personality profile of an agent is fixed and does not change over time.

Gamification techniques are various approaches used in a gamified system to influence the behaviour of users. As discussed in Sec. \ref{sec: related work}, certain gamification techniques have a specific degree of compatibility or influence over specific personality profiles. Therefore, it is proposed that gamification techniques can be related to specific personality profiles, also described using personality facets. Additionally, each gamification technique is given a name and is described using two more features: a goal and a reward. The goal represents the condition that must be met to earn the reward, while the reward is the item or benefit that is awarded when the goal is achieved.

Let \(\Gamma = \{\gamma_1, \gamma_2, \dots, \gamma_r\}\) be the set of all gamification techniques, where each \(\gamma_i \in \Gamma\) is defined as a tuple \eqref{eq: gamification_technique}.
\begin{equation}
    \label{eq: gamification_technique}
    \gamma_i = (\texttt{name}_i, \texttt{goal}_i, \texttt{reward}_i, \vec{\psi}_i)
\end{equation}

Here, \(\vec{\psi}_i \in \Psi\) denotes the personality profile that the technique \(\gamma_i\) is most likely to influence. Therefore, \(\gamma_i \in \mathcal{N} \times \mathcal{G} \times \mathcal{R} \times \Psi\), where \(\mathcal{N}\) is the set of all possible names (e.g., strings or labels), \(\mathcal{G}\) is the space of goals, and \(\mathcal{R}\) is the space of rewards.

For example, the technique \(\gamma_1 = (\texttt{Leaderboard}, g_1, r_1, \vec{\psi}_1)\) might specify that achieving the top 3 rank (goal \(g_1\)) grants a badge (reward \(r_1\)), and is most influential on individuals with high competitiveness (\(\vec{\psi}_1\)). Personality profile \(\vec{\psi}_1\) may already be defined in the set of personality profiles \(\Psi\) and associated with some other gamification techniques or agents, or it may be a new profile that is added to the set.

The compatibility between a gamification technique \(\gamma_i\) and an agent \(\alpha_j\) is determined based on the Euclidean similarity of their personality profiles \(\vec{\psi}_i\) and \(\vec{\psi}_j\), adjusted by the agent's scalar personality modifier \(\chi_j \in [0,1]\). The compatibility is calculated using the Euclidean distance between the two personality profiles, as shown in \eqref{eq: compatibility}. 

\begin{equation}
    \label{eq: compatibility}
    c(\gamma_i, \alpha_j) = \min\left(1,\ \chi_j \cdot \left(1 - \frac{\|\vec{\psi}_i - \vec{\psi}_j\|_2}{\sqrt{30}}\right)\right),\quad c: \Gamma \times \mathcal{A} \to [0,1]
\end{equation}

Here, \(\|\cdot\|\) denotes the Euclidean norm, and the denominator \(\sqrt{30}\) normalises the distance so that the similarity term lies in \([0,1]\). 
\(\chi_j \in [0, \lambda]\) is an agent's personality modifier. If \(\chi_j > 1\), compatibility is amplified; if \(\chi_j < 1\), it is suppressed. The result is clamped to ensure \(c(\gamma_i, \alpha_j) \in [0,1]\). This yields a compatibility score \(c(\gamma_i, \alpha_j) \in [0,1]\), where 1 indicates maximum compatibility and 0 indicates no compatibility.




\subsection{Model Implementation}

The implemented system\footnote{Full implementation available at \url{https://github.com/AILab-FOI/GEAR}.} to showcase the approach proposed in this paper is based on the recipeWorld model \cite{Fontana2015recipeWorld}. The agents in the system are service providers and consumers, and the gamification techniques are used to influence their behaviour in that context. The personality profiles of the agents are based on the Big Five personality traits and their associated facets \cite{mccrae2006PersonalityAdulthoodFiveFactor}. The compatibility between gamification techniques and agents is calculated using the formula presented in \eqref{eq: compatibility}.

The proposed system is implemented in Python, using the NumPy library for vector and matrix operations. The implementation includes functions for calculating the compatibility between gamification techniques and agents, as well as for simulating the behaviour of agents in a gamified system. The implementation is designed to be modular and extensible, allowing for the addition of new gamification techniques, agents, and personality profiles.

Agents of the proposed system are implemented using the SPADE framework \cite{gregori2006JabberbasedMultiagentSystem,palanca2020SPADE3Supporting,palanca2025SmartPythonAgent}. Each agent is extended using a personality module that includes at least the personality profile. The same personality profile class is used to instantiate personality profiles for agents and gamification techniques.

Some instances of how an agent behaves are influenced by the agent's personality. For example, the value of an offer an agent is observing is influenced by the agent's personality. The value of an offer is calculated based on the offered service's price and duration, influenced by the number of awards showcased by the service provider, as well as the agent's personality traits related to price sensitivity (self-discipline), quality focus (dutifulness), badge appreciation (values), and risk tolerance (excitement seeking). The implementation of the offer value calculation is shown in listing \ref{lst: price acceptance}.

\begin{lstlisting}[language=Python, caption={Determining value of an offer based on personality}, label={lst: price acceptance}]
price_sensitivity = agent_personality.get_facet_score("Self-Discipline")
quality_focus = agent_personality.get_facet_score("Dutifulness")
badge_appreciation = agent_personality.get_facet_score("Values")
risk_tolerance = agent_personality.get_facet_score("Excitement seeking")

offer_values = []
for offer in offers:
    ...
    price_value = service.price
    duration_value = service.duration
    awards_value = awards

    price_component = price_value * (0.5 + price_sensitivity * 0.5)
    duration_component = -duration_value * (quality_focus * 0.5)
    badge_component = -awards_value * (badge_appreciation * 10)
    risk_component = (random.random() * 2 - 1) * risk_tolerance * 5

    weighted_value = (
        price_component + duration_component + badge_component + risk_component)
    offer_values.append(
        {"provider": provider, "service": service, "value": weighted_value})
\end{lstlisting}

As mentioned earlier, compatibility between gamification techniques and agents is calculated using the formula presented in \eqref{eq: compatibility}. The implementation of the body of the function performing the compatibility calculation is shown in listing \ref{lst: compatibility}.

\begin{lstlisting}[language=Python, caption={Compatibility calculation wherein \texttt{self} is an instance of the gamification technique class and \texttt{agent\_personality} is an instance of the personality profile class}, label={lst: compatibility}]
technique_vector = self.personality_profile.get_personality_vector()
agent_vector = agent_personality.get_personality_vector()
agent_personality_modifier = agent_personality.get_personality_descriptor().get("personality modifier")

distance = np.linalg.norm(technique_vector - agent_vector)
max_distance = np.sqrt(30)
compatibility = min(1,
    agent_personality_modifier * (1 - (distance / max_distance)))

return compatibility
\end{lstlisting}

An individual personality profile is implemented as a 30-dimensional NumPy vector, where each dimension represents the score of a personality facet. The personality profile class includes methods for getting and setting the scores of individual facets, as well as for getting and setting the scores of all facets at once. A personality profile example, that of a personality describable as a \emph{creative innovator}, is shown in listing \ref{lst: personality profile}.

\begin{lstlisting}[language=Python, caption={Personality profile}, label={lst: personality profile}]
creative_innovator = PersonalityProfile(np.array([
    0.90, 0.85, 0.80, 0.85, 0.90, 0.88,    # Openness
    0.70, 0.60, 0.65, 0.80, 0.70, 0.60,    # Conscientiousness
    0.60, 0.65, 0.70, 0.75, 0.80, 0.70,    # Extraversion
    0.70, 0.65, 0.70, 0.68, 0.60, 0.75,    # Agreeableness
    0.30, 0.25, 0.20, 0.30, 0.40, 0.30     # Neuroticism
]))
\end{lstlisting}

In addition to calculating a general compatibility of a gamification technique to a given agent based solely on their associated personality profile, the compatibility is also affected by the reward that is offered for the service. The reward compatibility is calculated based on the type of reward and the personality of the agent. For example, an agent with a high score in the \emph{Achievement striving} facet of the \emph{Conscientiousness} trait is more likely to be influenced by a gamification technique that offers a reward. If the agent has a high score in \emph{Assertiveness}, it may be derived that it is oriented towards building status and is therefore again likely to be influenced by a gamification technique that offers a reward. These two personalities are currently set up to value badges and trophy rewards the most. A part of the body of the function performing the reward compatibility calculation is shown in listing \ref{lst: reward compatibility}.

\begin{lstlisting}[language=Python, caption={Reward compatibility calculation}, label={lst: reward compatibility}]
achievement_drive = agent_personality.get_facet_score("Achievement striving")
status_orientation = agent_personality.get_facet_score("Assertiveness")
novelty_seeking = agent_personality.get_facet_score("Actions")

reward_compatibility = 0.5

reward_name = reward_item.name.lower()

if "trophy" in reward_name or "badge" in reward_name:
    trophy_factor = 0.7 * achievement_drive + 0.3 * status_orientation
    reward_compatibility = 0.6 + (trophy_factor * 0.4)
else:
    other_factor = 0.6 * novelty_seeking + 0.4 * achievement_drive
    reward_compatibility = 0.4 + (other_factor * 0.4)
return reward_compatibility
\end{lstlisting}

Ultimately, the final compatibility between a gamification technique and an agent is calculated as a weighted average of the general compatibility and the reward compatibility, as shown in listing \ref{lst: combined compatibility}. In the particular example shown in listing \ref{lst: combined compatibility}, the general compatibility is weighted more heavily than the reward compatibility, as the general compatibility is a better indicator of how well a gamification technique will work for an agent.

\begin{lstlisting}[language=Python, caption={Combined compatibility calculation}, label={lst: combined compatibility}]
technique_compatibility = self.calculate_compatibility(agent.personality)
reward_compatibility = self.calculate_reward_compatibility(agent, self.reward_item)

combined_compatibility = (technique_compatibility * 0.7) + (reward_compatibility * 0.3)
\end{lstlisting}



\section{Example Scenarios}\label{sec: examples}

The following two scenarios are used to exemplify the proposed approach and show additional details of the implemented system. The first scenario is more abstract, while the second scenario is a more specific one. Their purpose is to showcase the proposed approach in different contexts and to demonstrate its flexibility.



\subsection{Abstract Scenario}\label{sec: example recipeworld}

As mentioned earlier, the implemented system is based on the recipeWorld model \cite{Fontana2015recipeWorld}, which is \enquote{an agent-based model that simulates the emergence of networks out of a decentralized autonomous interaction}. The main types of agents in the recipeWorld can be abstracted to the two types of agents present in the implemented system: service providers and service consumers. Service providers offer services to service consumers, who in turn request services from the providers. A list of services required by the consumers is defined as a recipe, and the consumers move from one provider to another based on their respective recipes and the required services. Not all services need to be provided by a single agent, and agents can specialise in providing one or more services. Due to this interaction, a social network emerges.

At the current state of the implemented system, the services that can be found in the system are defined using three attributes: a name, a price, and a duration. The name is a unique identifier for the service, the price is the baseline amount of money the service provider requires to offer the service, and the duration is the baseline amount of time the service provider requires to complete the service. The services are offered by service providers. On the side of service consumers, the services are aggregated into recipes, which are lists of services required by the consumers. The consumers then request services from the providers based on their respective recipes. A recipe can be considered a list of tasks that need to be completed in order to reach an objective -- a finished recipe.

The communication between service consumers and service providers, which is the main interaction in the system, is shown in Fig. \ref{fig: sequence diagram}. While the service consumer agent has a recipe that is not yet completed, they will call for proposals from the service providers that offer the service required by the current recipe element. The service providers that offer the required service will then send a proposal to the service consumer, which includes the price and duration of the service. The service consumer will then select the best offer based on the price and duration of the service and number of rewards received by the service provider agent, and send a request to the selected service provider. The service provider will then either accept or reject the request. This decision is based on the service provider's capacity to provide the service, e.g. if the service provider is currently busy with another service request, they will reject the incoming request. If the request is accepted, the service provider will start providing the service, and the service consumer will wait for the service to be completed. Once the service is completed, the service consumer will move on to the next element in their recipe.

This communication flow, depicted in the sequence diagram in Fig. \ref{fig: sequence diagram}, is controlled by a finite-state machine behaviour, shown in Fig. \ref{fig: FSM}. This finite-state machine controls the behaviour of the service consumer agent.

\begin{figure}
    \centering
    \includegraphics[width=0.7\linewidth]{sequence diagram.jpeg}
    \caption{Sequence diagram of the service consumer agent}
    \label{fig: sequence diagram}
\end{figure}

\begin{figure}
    \centering
    \includegraphics[width=0.9\linewidth]{consumer fsm.jpeg}
    \caption{Finite-state machine of the service consumer agent}
    \label{fig: FSM}
\end{figure}

Should a service required by the service consumer not be provided by any of the known service providers in the system, the service consumer will look for new providers. If no new service providers are found, the service consumer will stop searching for new providers and the recipe will be considered failed.

Service providers are designated specific services they offer when they are created, as shown in listing \ref{lst: service provider creation}. The services are defined using the three attributes mentioned earlier. Service providers can offer multiple services, and a service can be offered by multiple service providers.

\begin{lstlisting}[language=Python, caption={Service provider agent creation}, label={lst: service provider creation}]
provider2_services = [Service("A", 8, 4), Service("C", 20, 2)]

ServiceProviderAgent(
    "provider2@localhost",
    "password",
    provider2_services,
    personality={
        "personality profile": personality_profiles.creative_innovator
    },
)
\end{lstlisting}

If a service provider is faced with a gamification technique while it is making an offer to be sent as a proposal to a service consumer, the gamification technique may be applied to the offer. In the case of a gamification technique of type \emph{service milestone}, which is instantiated as shown in listing \ref{lst: service milestone instantiation}, the offer is not necessarily changed, as per the definition of the gamification technique In particular, this technique rewards the agent with a trophy for reaching a certain number of services provided. Based on the defined personality profile of the \emph{service milestone} gamification technique, shown in listing \ref{lst: service milestone personality}, it is most effective on agents with a high conscientiousness trait. The personality profile of the service provider agent created in listing \ref{lst: service provider creation}, with personality profile shown in listing \ref{lst: personality profile}, has a compatibility score of 0.82 with the \emph{service milestone} gamification technique. Since this particular technique offers a reward that is a trophy, the final compatibility score is 0.85, as shown in listing \ref{lst: service milestone compatibility}.

\begin{lstlisting}[language=Python, caption={Service milestone gamification technique instantiation}, label={lst: service milestone instantiation}]
{"service milestone": ServiceMilestone(
    "service milestone",
    pprofile.service_milestone,
    goal={"metric": "total_services", "target": 2, "comparison": "gte"},
    reward_item=Item(
        "Service Trophy", {"effect": "reputation_boost", "value": 10}))}
\end{lstlisting}

\begin{lstlisting}[language=Python, caption={Service milestone gamification technique personality profile}, label={lst: service milestone personality}]
service_milestone = PersonalityProfile(np.array([
    0.50, 0.45, 0.55, 0.65, 0.50, 0.55,    # Openness
    0.85, 0.80, 0.75, 0.95, 0.90, 0.80,    # Conscientiousness
    0.70, 0.65, 0.60, 0.70, 0.65, 0.75,    # Extraversion
    0.75, 0.70, 0.65, 0.70, 0.60, 0.70,    # Agreeableness
    0.25, 0.20, 0.20, 0.30, 0.30, 0.25     # Neuroticism
]))
\end{lstlisting}

\begin{lstlisting}[language=Python, caption={Service milestone gamification technique compatibility calculation}, label={lst: service milestone compatibility}]
INFO:utils.logger: [service milestone] Compatibility: 0.822818360620144
INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider2@localhost: 0.91
INFO:utils.logger: [service milestone] Combined compatibility: 0.85
\end{lstlisting}

On the other hand, some of the implemented gamification techniques can affect the offer in a more significant way. For example, the \emph{quality focus} gamification technique, which may be considered as an instance of role-playing, increases the duration of the service by a specific amount that depends on the agent's \emph{Dutifulness} facet. Longer service duration in this case denotes better quality. On the other hand, the agent's capacity to provide the service is limited by the number of concurrent services the agent can provide. After calculating the compatibility, agents will ignore this particular gamification technique if the combined compatibility is below a certain threshold.

In the implemented system, we have experimented with various compatibility thresholds and settled on a basic threshold of 0.6. This choice emerged from preliminary experiments demonstrating that thresholds lower than 0.6 led to overly frequent behavioural adjustments, while thresholds above 0.6 failed to significantly influence agent interactions. Therefore, 0.6 provided a balanced approach ensuring meaningful yet not excessive adaptations. What is more, the reward system directly impacts agent behaviour by interacting distinctly with personality facets (particularly \emph{Achievement striving} and \emph{Assertiveness}), modifying decisions beyond basic compatibility. For example, agents with high \emph{Achievement striving} are considerably more responsive to gamification techniques providing trophy-based rewards, explicitly motivating behaviours oriented towards goal completion.


\subsection{Specific Scenario}\label{sec: example llm}

The variety of use cases for the proposed approach is vast. To further exemplify the proposed approach, we present a specific scenario where the proposed approach is used to influence the behaviour of agents in a system that utilises large language models (LLMs). The basic setup of this scenario includes agents who are given specific tasks from their human users. The agents divide the given task into smaller subtasks, i.e. they create a recipe that can be used to achieve the goal of the received task upon its completion. Completing each subtask requires the agent to seek the help of other agents. The agents are powered by different kinds of LLMs, varying in computational and real cost and the anticipated time it would take them to finish the subtask. This specification can clearly be abstracted to services described in the previous section.

% The agents with recipes will, therefore, be able to complete the task in a more efficient way by utilising the help of other agents.

\section{Discussion}\label{sec: discussion}

The method proposed in this paper leverages personality profiling based on the Five-Factor Model to achieve nuanced, real-time behavioural modulation.

The empirical evaluation, exemplified through both abstract and concrete scenarios, highlighted the flexibility and efficiency of the approach. In particular, the adaptation of gamification elements to specific personality profiles allowed agents to engage more authentically, reflecting a diverse range of realistic behaviors. Furthermore, the compatibility calculation introduced in the theoretical framework provided clear quantitative guidance for the dynamic application of gamification techniques.

However, while the initial implementation and demonstrations show considerable promise, several challenges remain. The static nature of personality profiles may limit adaptability over prolonged interactions, necessitating further investigation into how dynamic personality shifts could enhance realism and engagement. Scalability and complexity in larger, more dynamic multiagent scenarios may introduce computational and coordination challenges that require further refinement of both the theoretical and practical aspects of the system.

\section{Conclusion}\label{sec: conclusion}

This paper introduced an innovative method for real-time, dynamic adaptation of agent behaviours using personality-based gamification in multiagent systems. The association of specific gamification techniques with nuanced personality profiles derived from the Five-Factor Model, the proposed approach suggests a different approach to modelling agent behaviour, as opposed to the traditionally demanding process of behavioural adaptation found in reinforcement learning.

The demonstrated scenarios validate the method's efficacy in creating believable and engaging agent interactions, highlighting its applicability across various domains: from abstract service-oriented scenarios to more practical, complex applications involving large language models.

Future research directions include exploring dynamic personality evolution, expanding compatibility models, and assessing the approach in more complex, real-world multiagent environments. Further future work encompasses providing agents with language models to further personality profile simulation and adapting the implemented system to various domains and uses suitable for agent-based modelling or multiagent systems, e.g. video games and social simulations. Ultimately, this methodology holds considerable potential for enriching agent-based simulations and advancing personalised, interactive artificial intelligence systems.

\begin{credits}
\subsubsection{\ackname}
This study is supported by the project MOBODL-2023-08-5618
funded by the European Union and the Croatian Science Foundation.

\subsubsection{\discintname}
The authors have no competing interests to declare that are relevant to the content of this article.
\end{credits}
%
% ---- Bibliography ----
%
% BibTeX users should specify bibliography style 'splncs04'.
% References will then be sorted and formatted in the correct style.
%
\bibliographystyle{splncs04}
\bibliography{MyLibraryBibTex}
%
\end{document}
