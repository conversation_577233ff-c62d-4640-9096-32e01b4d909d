# Framework for Agent Gamification Based on Ontologies (MAGO)

This repository is a part of the research, activities, and results of the research visit of <PERSON><PERSON><PERSON> of [University of Zagreb Faculty of Organization and Informatics](https://www.foi.unizg.hr) to [Universitat Politècnica de València](http://www.upv.es) [Valencian Research Institute for Artificial Intelligence](https://vrain.upv.es) under the mobility grant MOBODL-2023-08-5618 of the [Croatian Science Foundation](https://hrzz.hr). This research visit began on 15 February 2024 and is planned to end on 14 June 2025.

## Overview of the Planned Research

The planned research is divided into two parts, featuring two integral phases, one continuing and building on the other.

The main focus of **Part I** is on multiagent systems (MASs) in general, i.e. developing a framework for instantiating a multiagent system based on the given ontology, which is developed during this first part of this research as well.

- **Phase A**: Modelling an ontology featuring organisational concepts towards implementing a framework for instantiating agents based on the contents of the ontology and running an MAS based on a knowledge base.
- **Phase B**: Designing, developing and implementing the framework for instantiating and running a MAS described using an ontology and implementing a testbed environment for applying, testing and evaluating the developed ontology.

**Part II** is focused on gamification and developing a framework for gamifying artificial agents.

- **Phase A**: Modelling, developing, and implementing an ontology for describing video games as an intelligent virtual environment (IVE). Modelling, developing, and implementing an ontology for describing gamification and gamified systems, focusing on applicability to artificial agents.
- **Phase B**: Designing, developing and implementing the ontology-based agent gamification framework as an upgrade of the framework from Part I-B and implementing a testbed environment for applying, testing and evaluating the developed ontology.

## Acknowledgement

![alt text](<LaTeX Templates/Common Figures/Vidljivost s pozadinom.png>)
