ConceptName;Definition;Description;Domain;Range
Gamification technique;A method or mechanism used to introduce game elements or principles into a non-game context for agents;This class represents a specific gamification method applied in the multiagent environment. A gamification technique can include elements like point systems, badges, challenges, or other game-like incentives tailored for artificial agents. It is considered a knowledge artefact in the ontology, meaning it is a defined strategy or design that can be implemented to influence agent behaviour and motivation.;;
Gamification goal;The objective that must be achieved in order to fulfil a gamification technique;A gamification goal is the quantified objective of the agent that the agent must achieve in order to fulfil a gamification technique. For example, a gamification goal might be to increase the number of interactions between agents, to improve the quality of their interactions, or to achieve a specific task such as producing a certain amount of a certain resource.;;
Personality model;An abstract framework defining a set of personality features and their relationships;A personality model provides the theoretical structure for organising personality traits in the ontology. For example, the Five-Factor Model (Big Five) is a personality model that includes traits like Openness and Extraversion. In this ontology, the personality model class encompasses such frameworks and specifies which personality features (traits or facets for the Five-Factor Model) belong to the model. It allows the system to interpret an agent's personality profile according to a well-defined set of traits.;;
Personality feature;A characteristic of personality, such as a trait or a facet, that can be used to describe personality of an agent;A personality feature represents here any measurable aspect of personality that an agent can have defined within a personality model. It serves as a general category encompassing broad traits (like Extraversion or Conscientiousness) and more specific facets. Personality features form the building blocks of personality profiles and are defined within a personality model. By capturing these features, the system can adapt gamification elements to match individual personality differences.;;
Trait;A major personality trait representing a broad dimension of personality in the Five-Factor Model;Trait refers to a high-level personality characteristic of an agent. Traits are typically the primary factors in a personality model, such as the Big Five traits (Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism). Each trait encompasses several facets and describes consistent patterns in an agent's behaviour or disposition. In this ontology, traits help structure the agent's personality profile, allowing broad-strokes customisation of gamification strategies based on an agent's dominant personality characteristics.;;
Facet;An aspect of a personality trait;In personality modelling, facets are finer-grained characteristics that make up a larger trait. They provide a detailed breakdown of personality features. For example, within the trait of Extraversion, facets might include sociability or assertiveness. Facets in the ontology allow for more granular distinctions in an agent's personality profile, supporting more precise gamification personalisation and determining the strength of the effect of gamification techniques on agents.;;
Personality profile;A collection of personality features and their associated weights that characterises a specific agent;This class represents the personalised set of personality attributes for an individual agent. A personality profile consists of multiple personality features (e.g. traits or facets), each with an associated feature weight, effectively mapping out the agent's personality. It is used by the framework to tailor gamification techniques to the agent. For instance, an agent's profile may indicate high Openness and low Neuroticism, which can guide the selection of suitable motivational strategies. Furthermore, the personality profile can be used to determine the effect of gamification techniques on agents, as different techniques might have different effects on agents with different personality profiles.;;
Feature weight;A numeric weighting assigned to a personality feature, indicating its intensity;This class represents the quantified level of a particular personality feature in an agent's profile. Each feature weight links a personality feature (specifically a facet in the developed framework) to a value (often a floating-point number) that reflects how strongly the agent exhibits that feature. It is used to create a detailed personality profile, enabling the gamification framework to adjust strategies based on individual differences.;;
Age;A demographic attribute representing how old an agent (or user) is;In this ontology, age is modelled as a type of feature associated with an agent. It denotes the temporal duration of an agent's existence (for example, in years) or an age category. Age can be used to tailor gamification elements, as different age groups might respond to game mechanics differently.;;
Gender;A personal attribute representing the gender of an agent;Gender is modelled as a feature of an agent. It represents an agent's gender identity (such as male, female, or other categories) and is included as a part of the agent's profile. This attribute can be relevant in contexts where gender might influence preferences or behaviour, thereby informing how gamification is tailored to different agents.;;
defines feature;Indicates that a personality model includes a given personality feature as part of its defined set;This object property is the inverse of "is a part of model". It links a personality model to a personality feature, meaning the feature is defined by or within that model. For example, the Five-Factor Model defines features such as Openness and Extraversion. Using "defines feature", it can be designated which traits or facets a particular personality model encompasses.;Personality Model;Personality Feature
has event object;Links an observable event to a physical artefact that is involved in that event;This property associates an event occurring in the environment with the object (artefact) that the event involves. In a multiagent or virtual environment, an "Observable Event" might have a "Physical Artefact" as its object. For instance, a "pick-up" event has the picked item as the event object. "Has event object" thus helps describe the context of events by specifying what object is affected or used.;Observable Event;Physical Artefact
has gamification goal;Associates a gamification technique with the gamification goal it is intended to fulfil;This property ties a gamification technique to its purpose or intended objective. The gamification goal is an abstract target (such as improving collaboration or increasing engagement) that the player must achieve in order to fulfil the gamification technique. By linking techniques to goals, the ontology clarifies why each gamification method is applied.;Gamification Technique;Gamification Goal
has gamification outcome;Associates a gamification technique with an outcome event that results from its application;When a gamification technique is applied, it produces a certain outcome or reward event. The "has gamification outcome" property connects the technique to that resulting "Gain Event". For instance, a points-award technique will have a point-gain event as its outcome, whereas a badge-award technique will have a badge-gain event as its outcome. This link allows the system to know what immediate effect or reward is generated by the gamification mechanism in the environment.;Gamification Technique;Gain Event
has observable property;Links an agent to a property of that agent that can be observed in the environment;This property denotes that an agent has a certain attribute which is observable by other agents or the system. An observable property might be something like the agent's current score, location, or status. By using "has observable property", the ontology can represent which aspects of an agent's state are externally visible, enabling agents or systems to respond to those observable attributes.;Agent;Observable Property
has personality feature;Connects a feature weight to the specific personality feature it represents;This object property links a feature weight instance to the personality feature (trait or facet) that the weight measures. Essentially, it specifies which personality characteristic a given numeric weight corresponds to in an agent's profile. For example, a weight value might be connected to the feature "Extraversion", indicating that the number quantifies the agent's level of Extraversion.;Feature Weight;Personality Feature
has personality profile;Associates an agent, a gamification technique, or a feature weight) with a particular personality profile;This functional property is primarily used to link an agent or a gamification technique to their personality profile. It can also connect a feature weight to the personality profile it belongs to, grouping individual trait measurements under a single profile. By using "has personality profile", the ontology identifies which profile describes an agent's personality, enabling the system to treat that set of traits as a unit when adapting gamification strategies.;Agent or Feature Weight;Personality Profile
has weight value;Specifies the numeric value of a feature weight, typically as a floating-point number;This data property holds the quantitative value for a feature weight entry. The value is usually a float that indicates how strongly an agent possesses a particular personality feature. Through "has weight value", each trait in an agent's personality profile is given a magnitude, which can be used by the gamification logic to adjust game elements (such as difficulty or rewards) according to the agent's personality.;Feature Weight;Float
is a part of model;Indicates that a personality feature is a component of a larger personality model;This property represents the relationship where a personality feature (e.g. a trait or a facet) belongs to a personality model. In other words, it declares that a given trait is part of the structure of a particular personality framework. For example, \enquote{Extraversion is a part of the Five-Factor model.} "Is a part of model" is the inverse of "defines feature" and helps organise features under their respective models.;Personality Feature;Personality Model
has name;Assigns a textual name to an entity in the ontology;"Has name" is a data property used to provide a human-readable name or title for an individual or entity in the ontology. It serves as an identifier that is easier to read and communicate than an IRI. For example, a particular gamification technique instance could be given a name like "Price Optimiser" using this property. This makes it convenient to reference entities in interfaces or documentation.;;String
