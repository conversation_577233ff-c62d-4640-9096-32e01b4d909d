Include;ConceptName;Synonyms;Symbol;Description;WordsDes;Definition;WordsDef;Instances;ClassAttributes;InstanceAttributes;TexSyn;TexAcr;TexDesc;TexDef;TexIns;TexCAT;TexIAT;TexName
1;Acquisition;;;An acquisition is, in economical terms, described as, in layman's terms, one company buying another. This is usually done using stocks - the buyer buys most of the target company's ownership stakes to assume control of it\footnote{For more information visit https://www.investopedia.com/terms/a/acquisition.asp}. Reasons for performing acquisitions are numerous, including to achieve economies of scale, greater market share, increased synergy, cost reductions, or new niche offerings.;64;An acquisition is the purchase of all or a portion of a corporate asset or target company\footnote{http://www.investinganswers.com/financial-dictionary/stock-valuation/acquisition-2224}.;17;;;;;;\item[\textbf{DES}] An acquisition is, in economical terms, described as, in layman's terms, one company buying another. This is usually done using stocks - the buyer buys most of the target company's ownership stakes to assume control of it\footnote{For more information visit https://www.investopedia.com/terms/a/acquisition.asp}. Reasons for performing acquisitions are numerous, including to achieve economies of scale, greater market share, increased synergy, cost reductions, or new niche offerings.;\item[\textbf{DEF}] An acquisition is the purchase of all or a portion of a corporate asset or target company\footnote{http://www.investinganswers.com/financial-dictionary/stock-valuation/acquisition-2224}.;;;;Acquisition
1;Action;Activity, Behaviour, Agent Action;C;An action is esentially an agent's response to tasks. Whereby tasks are created to be met or reached, an action is the atomic concept for achieving tasks. In the context of this document, an action is the building block of a process, and agents' ability to act towards its environment in general. Every action can be used to fulfill at least one task.;64;An action is the building block of agents' activities.;17;Attack, PickItem, GoToLocation, BrewPotion, MakeItem;;;\item[\textbf{SYN}] Activity, Behaviour, Agent Action;(C);\item[\textbf{DES}] An action is esentially an agent's response to tasks. Whereby tasks are created to be met or reached, an action is the atomic concept for achieving tasks. In the context of this document, an action is the building block of a process, and agents' ability to act towards its environment in general. Every action can be used to fulfill at least one task.;\item[\textbf{DEF}] An action is the building block of agents' activities.;\item[\textbf{INS}] Attack, PickItem, GoToLocation, BrewPotion, MakeItem;;;Action
1;Agent;Organisational Individual;A;An agent in the context of this document is a piece of software that can interact with its environment, act upon it, and, in case of an intelligent agent, reason upon their accessible knowledge. Indeed, an agent is \textit{anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.} \cite{Russell2010AImodern} In the organisational context of this document, a software agent is essentially a model of a real-life person.;64;A piece of software that can act upon its environment and perceive it.;17;;;;\item[\textbf{SYN}] Organisational Individual;(A);\item[\textbf{DES}] An agent in the context of this document is a piece of software that can interact with its environment, act upon it, and, in case of an intelligent agent, reason upon their accessible knowledge. Indeed, an agent is \textit{anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.} \cite{Russell2010AImodern} In the organisational context of this document, a software agent is essentially a model of a real-life person.;\item[\textbf{DEF}] A piece of software that can act upon its environment and perceive it.;;;;Agent
1;Artefact;;;An artefact is, as of yet, a somewhat undefined concept, in the context of specifying its domain. Essentially, an artefact can be anything that is not classified using the other classes of this ontology. Furthermore, an artefact can be phisically representative (e.g. a chair), or an unphisical concept (e.g. knowledge). Artefacts therefore represent various concepts that the agents can interact with, or that affect the given environment or the given system, i.e. objects forming the environment.;64;An artefact is an otherwise unclassified element of an organisation system.;17;;;;;;\item[\textbf{DES}] An artefact is, as of yet, a somewhat undefined concept, in the context of specifying its domain. Essentially, an artefact can be anything that is not classified using the other classes of this ontology. Furthermore, an artefact can be phisically representative (e.g. a chair), or an unphisical concept (e.g. knowledge). Artefacts therefore represent various concepts that the agents can interact with, or that affect the given environment or the given system, i.e. objects forming the environment.;\item[\textbf{DEF}] An artefact is an otherwise unclassified element of an organisation system.;;;;Artefact
1;Criteria of Organising;;;This concept comes from the OOVASIS ontology \cite{Schatten2014initialLSMAS,Schatten2014towardsLSMAS} where it represents varius criteria of organising agents within an organisation. One of the criteria is ..., another ... . Therefore, this concept determines what are the grounds for creating the given organisation in the first place, and governs the decision flow in the context of deciding which organisational features (starting from architecture) are most suitable for the given criteria of organising.;64;;17;;;;;;\item[\textbf{DES}] This concept comes from the OOVASIS ontology \cite{Schatten2014initialLSMAS,Schatten2014towardsLSMAS} where it represents varius criteria of organising agents within an organisation. One of the criteria is ..., another ... . Therefore, this concept determines what are the grounds for creating the given organisation in the first place, and governs the decision flow in the context of deciding which organisational features (starting from architecture) are most suitable for the given criteria of organising.;;;;;CriteriaofOrganising
0;Culture;;;;64;;17;;;;;;;;;;;Culture
1;Design Factor;;;Everything that influences the design of an organisation on a non-neglectable level is considered a design factor. Design factors can be internal and external, relative to the given organisation. \cite{Schatten2014towardsLSMAS}.;64;A design factor is an internal or an external factor with significant influence on the design of an organisation.;17;development of science and technology, human resources, market, size of organisation, strategy, etc.;;;;;\item[\textbf{DES}] Everything that influences the design of an organisation on a non-neglectable level is considered a design factor. Design factors can be internal and external, relative to the given organisation. \cite{Schatten2014towardsLSMAS}.;\item[\textbf{DEF}] A design factor is an internal or an external factor with significant influence on the design of an organisation.;\item[\textbf{INS}] development of science and technology, human resources, market, size of organisation, strategy, etc.;;;DesignFactor
1;Design Method;Organisational Design Method;;Every design method addresses a number of aspects of organisational architecture. A design method is esentially a common organisational design practice. \cite{Schatten2014towardsLSMAS};64;A design method is a common organisational design practice dealing with various aspects of organisational architecture.;17;business process reingeneering, kaizen, six sigma, lean management, knowledge management, etc.;;;\item[\textbf{SYN}] Organisational Design Method;;\item[\textbf{DES}] Every design method addresses a number of aspects of organisational architecture. A design method is esentially a common organisational design practice. \cite{Schatten2014towardsLSMAS};\item[\textbf{DEF}] A design method is a common organisational design practice dealing with various aspects of organisational architecture.;\item[\textbf{INS}] business process reingeneering, kaizen, six sigma, lean management, knowledge management, etc.;;;DesignMethod
1;Goal;;G;A goal is broadly defined as a result or achievement towards which effort is directed\footnote{http://www.dictionary.com/browse/goal}. In the context of this document, a goal is a form of an objective. A goal is an end to be met or reached, and can consist of several sub-goals.;64;A goal is a result towards which effort is directed - an end to be met.;17;;;;;(G);\item[\textbf{DES}] A goal is broadly defined as a result or achievement towards which effort is directed\footnote{http://www.dictionary.com/browse/goal}. In the context of this document, a goal is a form of an objective. A goal is an end to be met or reached, and can consist of several sub-goals.;\item[\textbf{DEF}] A goal is a result towards which effort is directed - an end to be met.;;;;Goal
1;Heterarchical Organisational Structure;;;When there is no single clear pyramid-like line of control in an organisation, the given organisation can be described as having a heterarchical organisational structure. As opposed to hierarchical organisational structure, heterarchical organisational structure can be visualised as an oriented forest \cite{Argente2007}, or essentially using a network-based visualisation \cite{Schatten2014towardsLSMAS}.;64;Heterarchical organisational structure is an organisational structude without a single clearly defined pyramid-like structure.;17;fishnet structure, Hollywood structure, spaghetti structure, etc.;;;;;\item[\textbf{DES}] When there is no single clear pyramid-like line of control in an organisation, the given organisation can be described as having a heterarchical organisational structure. As opposed to hierarchical organisational structure, heterarchical organisational structure can be visualised as an oriented forest \cite{Argente2007}, or essentially using a network-based visualisation \cite{Schatten2014towardsLSMAS}.;\item[\textbf{DEF}] Heterarchical organisational structure is an organisational structude without a single clearly defined pyramid-like structure.;\item[\textbf{INS}] fishnet structure, Hollywood structure, spaghetti structure, etc.;;;HeterarchicalOrganisationalStructure
1;Hierarchical Organisational Structure;;;In contrast to the heterarchical organisational structure, hierarchical organisational structure can be identified by its basic pyramid-like form fostering hierarchical relations between organisation units. Such an organisational structure can be visualised using an oriented tree \cite{Argente2007}. ;64;Hierarchical organisational structure is an organisational structude with a single clearly defined pyramid-like structure.;17;functional structure, project-oriented structure, matrix, etc.;;;;;\item[\textbf{DES}] In contrast to the heterarchical organisational structure, hierarchical organisational structure can be identified by its basic pyramid-like form fostering hierarchical relations between organisation units. Such an organisational structure can be visualised using an oriented tree \cite{Argente2007}. ;\item[\textbf{DEF}] Hierarchical organisational structure is an organisational structude with a single clearly defined pyramid-like structure.;\item[\textbf{INS}] functional structure, project-oriented structure, matrix, etc.;;;HierarchicalOrganisationalStructure
1;Human Immersed Agent;;;Humans can be represented within \iac{IVE} and be available for interaction with the digital agents within the environment using digital aids, most prominently featured as wearable technology items, such as smartwatches and similar. Such agents are dubbed human immersed agents, since they are real-life people represented in the digital world using their attached piece of wearable discreet equipment.;64;Real-world agents that are represented in \iac{IVE} using their wearable tecchnology gadgets.;17;;;;;;\item[\textbf{DES}] Humans can be represented within \iac{IVE} and be available for interaction with the digital agents within the environment using digital aids, most prominently featured as wearable technology items, such as smartwatches and similar. Such agents are dubbed human immersed agents, since they are real-life people represented in the digital world using their attached piece of wearable discreet equipment.;\item[\textbf{DEF}] Real-world agents that are represented in \iac{IVE} using their wearable tecchnology gadgets.;;;;HumanImmersedAgent
1;Hybrid Organisational Structure;;;Having mixed aspects of both heterarchical and hierarchical organisational structures, a hybrid organisational structure is a blend of the two. ;64;Having mixed aspects of both heterarchical and hierarchical organisational structures, a hybrid organisational structure is a blend of the two. ;17;academic structure, front-back structure, inverted structure, etc.;;;;;\item[\textbf{DES}] Having mixed aspects of both heterarchical and hierarchical organisational structures, a hybrid organisational structure is a blend of the two. ;\item[\textbf{DEF}] Having mixed aspects of both heterarchical and hierarchical organisational structures, a hybrid organisational structure is a blend of the two. ;\item[\textbf{INS}] academic structure, front-back structure, inverted structure, etc.;;;HybridOrganisationalStructure
1;Inhabitant Agent;;;Agents that can be phisically represented within \iac{IVE} are called inhabitant agents. These agents can be of artificial or real-world nature. Usually various \ac{IVE} artefacts exist within the \ac{IVE} that represent various inhabitant agents \cite{Rincon2014}. It could be said that these agents have their habitats within their respective \acp{IVE}.;64;Every agent that is can be represented as phisically present in an \ac{IVE} is considered an inhabitant agent.;17;Archmage, Hermit, Sorfina, mali\_agent13;;;;;\item[\textbf{DES}] Agents that can be phisically represented within \iac{IVE} are called inhabitant agents. These agents can be of artificial or real-world nature. Usually various \ac{IVE} artefacts exist within the \ac{IVE} that represent various inhabitant agents \cite{Rincon2014}. It could be said that these agents have their habitats within their respective \acp{IVE}.;\item[\textbf{DEF}] Every agent that is can be represented as phisically present in an \ac{IVE} is considered an inhabitant agent.;\item[\textbf{INS}] Archmage, Hermit, Sorfina, mali\_agent13;;;InhabitantAgent
1;Intelligent Virtual Environment;;IVE;Intelligent virtual environments are researched as an area on the intersection of two aspects pertaining to the concept of artificial intelligence, if only but marginally: intelligent tools and techniques that are embodied in autonomous agents (real-life and digital alike), and effective ways of representing them, along with various means of achieving different kinds of interaction amongst them \cite{Rincon2014a,Aylett2000}. In other words, \iac{IVE} is a concepte that represents a virtual environment whose main goal is simulating a segment of the real world, populated by artificial autonomous entities (agents). \cite{Rincon2014a};64;An intelligent virtual environment is a virtual environment that simulates the real world, and is populated by autonomous intelligent entities. \cite{Rincon2014a};17;modified version of The Mana World;;;;(IVE);\item[\textbf{DES}] Intelligent virtual environments are researched as an area on the intersection of two aspects pertaining to the concept of artificial intelligence, if only but marginally: intelligent tools and techniques that are embodied in autonomous agents (real-life and digital alike), and effective ways of representing them, along with various means of achieving different kinds of interaction amongst them \cite{Rincon2014a,Aylett2000}. In other words, \iac{IVE} is a concepte that represents a virtual environment whose main goal is simulating a segment of the real world, populated by artificial autonomous entities (agents). \cite{Rincon2014a};\item[\textbf{DEF}] An intelligent virtual environment is a virtual environment that simulates the real world, and is populated by autonomous intelligent entities. \cite{Rincon2014a};\item[\textbf{INS}] modified version of The Mana World;;;IntelligentVirtualEnvironment
0;IVE Artefact;Physical Artefact;;;64;;17;;;;\item[\textbf{SYN}] Physical Artefact;;;;;;;IVEArtefact
1;IVE Law;;;A special kind of a norm, an \ac{IVE} law is a norm that is constrained by its applicability to a specific physical space, i.e. a specific \ac{IVE} workspace. Being applicable to only a restricted area means that every \ac{IVE} law is valid only within the bounds of the given area (\iac{IVE} workspace), and never outside of that specified space. This kind of a norm is the key constraint of the concept of a situated organisational unit.;64;\Iac{IVE} law is a norm that is valid only within a specified physical space (\iac{IVE} workspace).;17;When a character is located on a map with at least 75\% of tiles of type Frozen, they are more suspectible to Damage of type Ice.;;;;;\item[\textbf{DES}] A special kind of a norm, an \ac{IVE} law is a norm that is constrained by its applicability to a specific physical space, i.e. a specific \ac{IVE} workspace. Being applicable to only a restricted area means that every \ac{IVE} law is valid only within the bounds of the given area (\iac{IVE} workspace), and never outside of that specified space. This kind of a norm is the key constraint of the concept of a situated organisational unit.;\item[\textbf{DEF}] \Iac{IVE} law is a norm that is valid only within a specified physical space (\iac{IVE} workspace).;\item[\textbf{INS}] When a character is located on a map with at least 75\% of tiles of type Frozen, they are more suspectible to Damage of type Ice.;;;IVELaw
1;IVE Workspace;;;Complimentary to the concept of a workspace, \iac{IVE} workspace represents a physical location, or a physically describable location. ;64;;17;;;;;;\item[\textbf{DES}] Complimentary to the concept of a workspace, \iac{IVE} workspace represents a physical location, or a physically describable location. ;;;;;IVEWorkspace
1;Knowledge Artefact;;KnArt;A knowledge artefact is a piece of knowledge, or a set of knowledge terms available to agents within the system or within the \ac{IVE}. Depending on the wanted level of abstraction, a knowledge artefact may represent a database containing various pieces of knowledge accessible by sets of agents, or individual pieces of knowledge. In the terms of rather undefined artefact class, knowledge artefacts are yet to be perfected in the context of knowledge representation and their suitability for representing knowledge of \iac{IVE} or \iac{MAS}.;64;Knowledge artefact is a piece of knowledge of an agent or an organisation.;17;organisational culture rulebook;;isAccessibleTo;;(KnArt);\item[\textbf{DES}] A knowledge artefact is a piece of knowledge, or a set of knowledge terms available to agents within the system or within the \ac{IVE}. Depending on the wanted level of abstraction, a knowledge artefact may represent a database containing various pieces of knowledge accessible by sets of agents, or individual pieces of knowledge. In the terms of rather undefined artefact class, knowledge artefacts are yet to be perfected in the context of knowledge representation and their suitability for representing knowledge of \iac{IVE} or \iac{MAS}.;\item[\textbf{DEF}] Knowledge artefact is a piece of knowledge of an agent or an organisation.;\item[\textbf{INS}] organisational culture rulebook;;\item[\textbf{IAT}] isAccessibleTo;KnowledgeArtefact
1;Manual;;;A manual defines the interface between individual agents and artefacts of \iac{IVE}. Including such a concept in the description of \iac{IVE} domain helps reduce unnecessary clutter in the context of setting ground-rules of how to use an artefact up front. The agents therefore immediately learn of the possibilities and applications of a given artefact without the need for exploring its possible uses.;64;;17;;;;;;\item[\textbf{DES}] A manual defines the interface between individual agents and artefacts of \iac{IVE}. Including such a concept in the description of \iac{IVE} domain helps reduce unnecessary clutter in the context of setting ground-rules of how to use an artefact up front. The agents therefore immediately learn of the possibilities and applications of a given artefact without the need for exploring its possible uses.;;;;;Manual
1;Merger;;;In standard economical terms, a merger is a combination of more than one company by the transfer of the properties to one surviving company\footnote{For more information visit http://www.dictionary.com/browse/merger}. In the context of this document, merger can simply be regarded as an organisational integration. ;64;A merger is the process of organisational integration.;17;;;;;;\item[\textbf{DES}] In standard economical terms, a merger is a combination of more than one company by the transfer of the properties to one surviving company\footnote{For more information visit http://www.dictionary.com/browse/merger}. In the context of this document, merger can simply be regarded as an organisational integration. ;\item[\textbf{DEF}] A merger is the process of organisational integration.;;;;Merger
1;Norm;;;Norms in general are not very different from the definition of a rule, their more generic counterpart. Used in a context of a population of a community, be it a natural or an artificial one, norms are expressions of desirable behaviour generally understood as rules indicating actions that are expected to be pursued. Norms are basically divided in three types: obligatory, prohibitive, and permissive. In the context of normative \acp{MAS} though, there are three different terms associated with norms: conventions, social norms, and social laws \cite{Mahmoud2014,Villatoro2011}, and two categories \cite{coleman1994foundations}: conventions and essential norms.;64;\textit{Norms are informal rules that are socially enforced.} \cite{Mahmoud2014};17;Formal Dress Code, The Dragon Egg item is usable for at most 23 hours after being laid.;;;;;\item[\textbf{DES}] Norms in general are not very different from the definition of a rule, their more generic counterpart. Used in a context of a population of a community, be it a natural or an artificial one, norms are expressions of desirable behaviour generally understood as rules indicating actions that are expected to be pursued. Norms are basically divided in three types: obligatory, prohibitive, and permissive. In the context of normative \acp{MAS} though, there are three different terms associated with norms: conventions, social norms, and social laws \cite{Mahmoud2014,Villatoro2011}, and two categories \cite{coleman1994foundations}: conventions and essential norms.;\item[\textbf{DEF}] \textit{Norms are informal rules that are socially enforced.} \cite{Mahmoud2014};;;;Norm
1;Normative System;;;A normative system is a system built on norms and their enfonrcement upon the system, or system's definition of architecture based on the said norms. In the context of computer science, a normative system is described as a system whose behaviour is influenced by norms, and whose description or specification depencds on using normative concepts \cite{Boella2006,Meyer1994}.;64;\textit{Systems in the behaviour of which norms play a role and which need normative concepts in order to be described or specified [\ldots]} \cite{Boella2006,Meyer1994};17;;;;;;\item[\textbf{DES}] A normative system is a system built on norms and their enfonrcement upon the system, or system's definition of architecture based on the said norms. In the context of computer science, a normative system is described as a system whose behaviour is influenced by norms, and whose description or specification depencds on using normative concepts \cite{Boella2006,Meyer1994}.;\item[\textbf{DEF}] \textit{Systems in the behaviour of which norms play a role and which need normative concepts in order to be described or specified [\ldots]} \cite{Boella2007};;;;NormativeSystem
1;Objective;;O;An objective is more general than a goal, although their definitions are rather similar. Fulfilling several goals can lead an organisational unit towards fulfilling a set objective. Thus, an objective is more suitable in the context of strategic planning, while a goal is more suitably used in the context of short-term planning. ;64;An objective is a high-level goal the be met, suitable for the context of strategic planning.;17;LearnSpell, FindDragonEgg, Brew Hatching Potion;;triggers, hasCriteriaOfOrganizing, isAchievedBy;;(O);\item[\textbf{DES}] An objective is more general than a goal, although their definitions are rather similar. Fulfilling several goals can lead an organisational unit towards fulfilling a set objective. Thus, an objective is more suitable in the context of strategic planning, while a goal is more suitably used in the context of short-term planning. ;\item[\textbf{DEF}] An objective is a high-level goal the be met, suitable for the context of strategic planning.;\item[\textbf{INS}] LearnSpell, FindDragonEgg, Brew Hatching Potion;;\item[\textbf{IAT}] triggers, hasCriteriaOfOrganizing, isAchievedBy;Objective
1;Observable Property;;;This is a property of an artefact located in \iac{IVE} that is observable by other agents located within the same \ac{IVE}. These are tighly connected to the concept of observable events, and can be influenced upon by an operation.;64;An observable property is a peroperty of an artefact that can be observed by agents in the same \ac{IVE}.;17;;;;;;\item[\textbf{DES}] This is a property of an artefact located in \iac{IVE} that is observable by other agents located within the same \ac{IVE}. These are tighly connected to the concept of observable events, and can be influenced upon by an operation.;\item[\textbf{DEF}] An observable property is a peroperty of an artefact that can be observed by agents in the same \ac{IVE}.;;;;ObservableProperty
0;Operation;;;;64;;17;;;;;;;;;;;Operation
1;Organisation;;;An apt definition is given in \cite{Carley1999} where an organisation is defined using several characteristics, including large-scale problem solving technology, composition of multiple agents, systems of goal-directed activities, etc. Furthermore, an essential benefit of organisations is identified in overcoming limitations of individual agency, especially cognitive, physical, temporal, and institutional.;64;An organisation is generally a group of agents structured according to a set criteria, with the basic goal of overcoming limitations of individual agency and achieving an organisation goal.;17;;;;;;\item[\textbf{DES}] An apt definition is given in \cite{Carley1999} where an organisation is defined using several characteristics, including large-scale problem solving technology, composition of multiple agents, systems of goal-directed activities, etc. Furthermore, an essential benefit of organisations is identified in overcoming limitations of individual agency, especially cognitive, physical, temporal, and institutional.;\item[\textbf{DEF}] An organisation is generally a group of agents structured according to a set criteria, with the basic goal of overcoming limitations of individual agency and achieving an organisation goal.;;;;Organisation
1;Organisational Architecture;;;All those concepts that deal with more than one aspect of organisational architecture, i.e. are not specialised as for example concepts that describe organisational structure only, are classified as belonging to the organisational architecture concept. \cite{Schatten2014towardsLSMAS} therefore identifies 15 such concepts.;64;In the context of this document, organisational architecture is the superclass for all the organisation-related concepts that deal with more than one aspect of organisational architecture.;17;Shamrock organisation, strategic organisation, information-based organisation, learning organisation, open organisation, etc.;;;;;\item[\textbf{DES}] All those concepts that deal with more than one aspect of organisational architecture, i.e. are not specialised as for example concepts that describe organisational structure only, are classified as belonging to the organisational architecture concept. \cite{Schatten2014towardsLSMAS} therefore identifies 15 such concepts.;\item[\textbf{DEF}] In the context of this document, organisational architecture is the superclass for all the organisation-related concepts that deal with more than one aspect of organisational architecture.;\item[\textbf{INS}] Shamrock organisation, strategic organisation, information-based organisation, learning organisation, open organisation, etc.;;;OrganisationalArchitecture
1;Organisational Change;Organisational Dynamics;;The concept of organisational change is closely tied to the intension of the concept of orgnaisational dynamics, since both concepts describe change to the established agent organisations. A change in the context of organisational change definition can be influenced by an organisational design method, yet unmistakingly it affects the organisational architecture of the given organisation. A change as defined here can adhere to one of the identified types of change (e.g. structural, cultural, strategic, etc.), can be attributed an impact of change, reason why the change started, and a key influence area (e.g. organisational memory) \cite{Schatten2014towardsLSMAS}.;64;;17;;;;\item[\textbf{SYN}] Organisational Dynamics;;\item[\textbf{DES}] The concept of organisational change is closely tied to the intension of the concept of orgnaisational dynamics, since both concepts describe change to the established agent organisations. A change in the context of organisational change definition can be influenced by an organisational design method, yet unmistakingly it affects the organisational architecture of the given organisation. A change as defined here can adhere to one of the identified types of change (e.g. structural, cultural, strategic, etc.), can be attributed an impact of change, reason why the change started, and a key influence area (e.g. organisational memory) \cite{Schatten2014towardsLSMAS}.;;;;;OrganisationalChange
1;Organisational Culture;;;The concept of organisational culture encompasses all the intangible aspects of an organisation, such as knowledge, various types of norms, a system of rewards, languages used in the organisation, etc. Organisational culture is therefore a concept that is mostly based in the organisational units, i.e. in the individual agents forming the organisation, and is thus the most fuzzy concept of all the perspectives of an organisation. \cite{Schatten2016roadmapIoE,Schatten2014towardsLSMAS} provide a quick overview of various conceptualisations of organisational architecture, where it is visible that organisational culture is an important part of an organisation.;64;\textit{Organizational culture defines important intangible aspects of an organization including knowledge, social norms, reward systems, language and similar.} \cite{Schatten2016roadmapIoE,Schatten2014initialLSMAS};17;;;;;;\item[\textbf{DES}] The concept of organisational culture encompasses all the intangible aspects of an organisation, such as knowledge, various types of norms, a system of rewards, languages used in the organisation, etc. Organisational culture is therefore a concept that is mostly based in the organisational units, i.e. in the individual agents forming the organisation, and is thus the most fuzzy concept of all the perspectives of an organisation. \cite{Schatten2016roadmapIoE,Schatten2014towardsLSMAS} summarises several papers \todo{Which papers?}, and provides a quick overview of various conceptualisations of organisational architecture, where it is visible that organisational culture is an important part of an organisation.;\item[\textbf{DEF}] \textit{Organizational culture defines important intangible aspects of an organization including knowledge, social norms, reward systems, language and similar.} \cite{Schatten2016roadmapIoE,Schatten2014initialLSMAS};\item[\textbf{INS}] Formal Dress Code, The Dragon Egg item is usable for at most 23 hours after being laid.;;;OrganisationalCulture
0;Organisational Design Method;Design Method;;;64;;17;;;;\item[\textbf{SYN}] Design Method;;;;;;;OrganisationalDesignMethod
1;Organisational Environment;;;The concept of organisational environment encompasses all the concepts that represent factors external to an organisation that have a potential to influence the given organisation, such as external organisations or individuals, or external events. Main concerns when organisational environment is considered are directed towards identifying constraints imposed on the given organisation by the environment, and demands of the environment towards the given organisation. \cite{Schatten2016roadmapIoE};64;Organisational environment are all the external factors that have the capacity to influence an organisation.;17;;;;;;\item[\textbf{DES}] The concept of organisational environment encompasses all the concepts that represent factors external to an organisation that have a potential to influence the given organisation, such as external organisations or individuals, or external events. Main concerns when organisational environment is considered are directed towards identifying constraints imposed on the given organisation by the environment, and demands of the environment towards the given organisation. \cite{Schatten2016roadmapIoE};\item[\textbf{DEF}] Organisational environment are all the external factors that have the capacity to influence an organisation.;;;;OrganisationalEnvironment
0;Organisational Individual;Agent;;;64;;17;;;;\item[\textbf{SYN}] Agent;;;;;;;OrganisationalIndividual
1;Organisational Knowledge Network;;;A network connecting all the pieces of organisational knowledge is considered to build an organisational knowledge network that effectively collects and intertwines all the knowledge of an organisation, thus fostering knowledge sharing and reuse amongst the organisational units of the given organisation, i.e. ultimately individual agents.;64;Organisational knowledge network is a network created by interconnected pieces of organisational knowledge.;17;;;;;;\item[\textbf{DES}] A network connecting all the pieces of organisational knowledge is considered to build an organisational knowledge network that effectively collects and intertwines all the knowledge of an organisation, thus fostering knowledge sharing and reuse amongst the organisational units of the given organisation, i.e. ultimately individual agents.;\item[\textbf{DEF}] Organisational knowledge network is a network created by interconnected pieces of organisational knowledge.;;;;OrganisationalKnowledgeNetwork
0;Organisational Process;Process;;;64;;17;;;;\item[\textbf{SYN}] Process;;;;;;;OrganisationalProcess
0;Organisational Strategy;Strategy;;;64;;17;;;;\item[\textbf{SYN}] Strategy;;;;;;;OrganisationalStrategy
1;Organisational Structure;;;Concepts used for describing various aspects and forms of structuring organisational units are categorised as belonging to the concept of organisational structure. Based on two different approaches, two criteria for classifying concepts of organisational structuring are used. The first depends on whether the given structure is the main structure or is it laid over the organisation, as a form of a super-structure. The second is based on the form of the structure, i.e. is it a hierarchical or heterarchical, or a mix of both.;64;Organisational structure is a concept comprising various aspects and forms f structuring organisational units.;17;Hierarchical, heterarchical;;;;;\item[\textbf{DES}] Concepts used for describing various aspects and forms of structuring organisational units are categorised as belonging to the concept of organisational structure. Based on two different approaches, two criteria for classifying concepts of organisational structuring are used. The first depends on whether the given structure is the main structure or is it laid over the organisation, as a form of a super-structure. The second is based on the form of the structure, i.e. is it a hierarchical or heterarchical, or a mix of both.;\item[\textbf{DEF}] Organisational structure is a concept comprising various aspects and forms f structuring organisational units.;\item[\textbf{INS}] Hierarchical, heterarchical;;;OrganisationalStructure
1;Organisational Unit;;OU;An organisational unit is the elementary unit of an organisation that, under the influence of the other organisational concepts, forms an organisation. In the context of this document, and the area of \acp{LSMAS}, an organisational unit is usually considered to represent an individual agent. Using the recursive definition though, an organisational unit that comprises multiple organisational units can be, under circumstances specified in \cite{Schatten2014initialLSMAS}, considered as an organisational unit. Using a more graphic explanation, a department organisational unit that comprises individual agents can be considered as individual organisational unit on a higher level of organisational hierarchy, where department organisational units form a higher-level organisational unit of a faculty.;64;An organisational unit is the key elementary unit in the context of forming an organisation.;17;maliAgent13;;definesRoles, hasRelation, hasRole, hasRelationship, definesRoles, hasCriteriaOfOrganizing, consistsOf, isPartOf;;(OU);\item[\textbf{DES}] An organisational unit is the elementary unit of an organisation that, under the influence of the other organisational concepts, forms an organisation. In the context of this document, and the area of \acp{LSMAS}, an organisational unit is usually considered to represent an individual agent. Using the recursive definition though, an organisational unit that comprises multiple organisational units can be, under circumstances specified in \cite{Schatten2014initialLSMAS}, considered as an organisational unit. Using a more graphic explanation, a department organisational unit that comprises individual agents can be considered as individual organisational unit on a higher level of organisational hierarchy, where department organisational units form a higher-level organisational unit of a faculty.;\item[\textbf{DEF}] An organisational unit is the key elementary unit in the context of forming an organisation.;\item[\textbf{INS}] maliAgent13;;\item[\textbf{IAT}] definesRoles, hasRelation, hasRole, hasRelationship, definesRoles, hasCriteriaOfOrganizing, consistsOf, isPartOf;OrganisationalUnit
1;Physical Artefact;IVE Artefact;;Every concept that describes objects that can be physically represented (e.g. a top hat), i.e. embodied and positioned on a topological map, and as such included in \iac{IVE} are classified as physical artefacts. Such elements have their role to play in the given \ac{IVE} and usually contain a defined interface that governs the process of interaction of an agent with the given physical artefact. ;64;Physical artefacts are all the concepts that can be physically represented and included in \iac{IVE}.;17;;;;\item[\textbf{SYN}] IVE Artefact;;\item[\textbf{DES}] Every concept that describes objects that can be physically represented (e.g. a top hat), i.e. embodied and positioned on a topological map, and as such included in \iac{IVE} are classified as physical artefacts. Such elements have their role to play in the given \ac{IVE} and usually contain a defined interface that governs the process of interaction of an agent with the given physical artefact. ;\item[\textbf{DEF}] Physical artefacts are all the concepts that can be physically represented and included in \iac{IVE}.;;;;PhysicalArtefact
1;Physical Property;;;Physical properties are key elements of physical artefacts, i.e. artefacts that can be visualised in a physical space. Usually when an artefact is used, a physical event is generated, and a physical property is modified.;64;;17;;;;;;\item[\textbf{DES}] Physical properties are key elements of physical artefacts, i.e. artefacts that can be visualised in a physical space. Usually when an artefact is used, a physical event is generated, and a physical property is modified.;;;;;PhysicalProperty
1;Plan;;;A plan is a finite set of actions that leads to a specified goal. An optimal plan cannot be made shorter if the same goal is retained in the process. The plan concept is especially useful when observing \ac{BDI} agents, since it is driven by agents' desires and intentions. ;64;A plan is a finite set of actions that leads to a specified goal.;17;How to solve the Quest for the DragonEgg;;;;;\item[\textbf{DES}] A plan is a finite set of actions that leads to a specified goal. An optimal plan cannot be made shorter if the same goal is retained in the process. The plan concept is especially useful when observing \ac{BDI} agents, since it is driven by agents' desires and intentions. ;\item[\textbf{DEF}] A plan is a finite set of actions that leads to a specified goal.;\item[\textbf{INS}] How to solve the Quest for the DragonEgg;;;Plan
1;Process;Organisational Processes;P;A process is in the context of this document defined as a set of atomic actions. Every process itself can be a part of another process, thus creating the recursive relation. A process can be performed in order for a goal to be met. It represents an activity or a procedure of an organisation \cite{Schatten2016roadmapIoE}.;64;A set of connected atomic actions.;17;RandomWalk;;;\item[\textbf{SYN}] Organisational Processes;(P);\item[\textbf{DES}] A process is in the context of this document defined as a set of atomic actions. Every process itself can be a part of another process, thus creating the recursive relation. A process can be performed in order for a goal to be met. It represents an activity or a procedure of an organisation \cite{Schatten2016roadmapIoE}.;\item[\textbf{DEF}] A set of connected atomic actions.;\item[\textbf{INS}] RandomWalk;;;Process
1;Quest;;Q;A quest is a similar to a goal, but it has a defined beginning and a defined end, i.e. a starting situation, and an ending situation\footnote{https://medium.com/the-mission/why-you-should-change-your-goals-into-quests-2467bbef9867}. In the context of \acp{MMORPG}, a quest is what drives a story, and, in principle, motivates the player to continue playing the game. Furthermore, a quest is often given to the player by an in-game character. A quest usually has various stages, and represents a challenge for the given player, thus embarking them on an adventure. ;64;A quest is similar to a goal, but has a defined starting and ending situations.;17;The Quest for the Dragon Egg;;;;(Q);\item[\textbf{DES}] A quest is a similar to a goal, but it has a defined beginning and a defined end, i.e. a starting situation, and an ending situation\footnote{https://medium.com/the-mission/why-you-should-change-your-goals-into-quests-2467bbef9867}. In the context of \acp{MMORPG}, a quest is what drives a story, and, in principle, motivates the player to continue playing the game. Furthermore, a quest is often given to the player by an in-game character. A quest usually has various stages, and represents a challenge for the given player, thus embarking them on an adventure. ;\item[\textbf{DEF}] A quest is similar to a goal, but has a defined starting and ending situations.;\item[\textbf{INS}] The Quest for the Dragon Egg;;;Quest
1;Role;;R;In the context of this document, a role is defined as a set of normative rules that are applicable to a particular part of the given organisation. Such normative rules are parts of the organisation's normative system, and can be grouped by specific criteria, thus forming roles. Roles are played by agents. When an agent plays a role, the role's constraints are applied to them, therefore constraining their possible actions, their perceivable goals, and their possibilities in general.;64;A role is a set of norms with a common denominator.;17;Wizard, Warrior, Ranged, Rogue;;isRoleIn, isRoleOf;;(R);\item[\textbf{DES}] In the context of this document, a role is defined as a set of normative rules that are applicable to a particular part of the given organisation. Such normative rules are parts of the organisation's normative system, and can be grouped by specific criteria, thus forming roles. Roles are played by agents. When an agent plays a role, the role's constraints are applied to them, therefore constraining their possible actions, their perceivable goals, and their possibilities in general.;\item[\textbf{DEF}] A role is a set of norms with a common denominator.;\item[\textbf{INS}] Wizard, Warrior, Ranged, Rogue;;\item[\textbf{IAT}] isRoleIn, isRoleOf;Role
1;Rule;;;A rule is an atomic building block of a normative system. Rules are usually built in a general if-then form, meaning that two statements are connected with a causal link, thus regulating what happens (then part: consequent) if something else happens beforehand (if part: antecedent). Other forms of rules are possible as well, but are not used as often. For the most part, rules pose constraints on the given subject. Rules are commonly used for devising appropriate logical conditions for introducing modalities. \cite{Mahmoud2014};64;Rules are elementary forms of constraints in normative systems, as they pose a basic aspect of defining standards.;17;;;;;;\item[\textbf{DES}] A rule is an atomic building block of a normative system. Rules are usually built in a general if-then form, meaning that two statements are connected with a causal link, thus regulating what happens (then part: consequent) if something else happens beforehand (if part: antecedent). Other forms of rules are possible as well, but are not used as often. For the most part, rules pose constraints on the given subject. Rules are commonly used for devising appropriate logical conditions for introducing modalities. \cite{Mahmoud2014};\item[\textbf{DEF}] Rules are elementary forms of constraints in normative systems, as they pose a basic aspect of defining standards.;;;;Rule
1;Situated Organisational Unit;;;An organisational unit that is tied to a specific \ac{IVE}, or a specific geographic or otherwise place, is a situated organisational unit. Furthermore, such an organisational unit has some situated norms that refer to it. The place that is essential to the situated relation of a situated organisational unit can be physical or digital, but can usually be represented visually, following the description of an inhabitant agent.;64;Every organisational unit that is tied to a location through a situated norm is considered a situated organisational unit.;17;;;;;;\item[\textbf{DES}] An organisational unit that is tied to a specific \ac{IVE}, or a specific geographic or otherwise place, is a situated organisational unit. Furthermore, such an organisational unit has some situated norms that refer to it. The place that is essential to the situated relation of a situated organisational unit can be physical or digital, but can usually be represented visually, following the description of an inhabitant agent.;\item[\textbf{DEF}] Every organisational unit that is tied to a location through a situated norm is considered a situated organisational unit.;;;;SituatedOrganisationalUnit
1;Strategic Alliance;;;An alliance that is aimed at forming long-lasting partnerships consisting of organisations of various forms is dubbed a strategic alliance. A strategic alliance is formed around a strategy as a long-term objective that is shared amongst the strategic alliance members. Norms and regulations governing the expected behaviour within the strategic alliance are expected to be accepted by all the members, old and new alike.;64;Strategic alliance is a form of a long-lasting partnership of organisations of various forms, formed around a shared strategy, or a strategic goal.;17;;;;;;\item[\textbf{DES}] An alliance that is aimed at forming long-lasting partnerships consisting of organisations of various forms is dubbed a strategic alliance. A strategic alliance is formed around a strategy as a long-term objective that is shared amongst the strategic alliance members. Norms and regulations governing the expected behaviour within the strategic alliance are expected to be accepted by all the members, old and new alike.;\item[\textbf{DEF}] Strategic alliance is a form of a long-lasting partnership of organisations of various forms, formed around a shared strategy, or a strategic goal.;;;;StrategicAlliance
1;Strategy;Organisational Strategy;;A strategy is, in the context of planning and shared organisational values, a long-term objective that is specified mosotly as a vision. It may consist of a number of objectives, quests, and similar. Strategy is therefore tentative in the context of plans of achieving it, but is versatile in terms of temporal likeness to change. Since it represents a long-term planning concept, a strategy is the main driving force of strategic alliances as agent coalitions meant to provide long-term suport to its members.;64;\textit{Strategy defines the long term objectives of an organization, action plans for their realization as well as tools on how to measure success.} \cite{Schatten2016roadmapIoE,Schatten2014towardsLSMAS};17;;;;\item[\textbf{SYN}] Organisational Strategy;;\item[\textbf{DES}] A strategy is, in the context of planning and shared organisational values, a long-term objective that is specified mosotly as a vision. It may consist of a number of objectives, quests, and similar. Strategy is therefore tentative in the context of plans of achieving it, but is versatile in terms of temporal likeness to change. Since it represents a long-term planning concept, a strategy is the main driving force of strategic alliances as agent coalitions meant to provide long-term suport to its members.;\item[\textbf{DEF}] \textit{Strategy defines the long term objectives of an organization, action plans for their realization as well as tools on how to measure success.} \cite{Schatten2016roadmapIoE,Schatten2014towardsLSMAS};;;;Strategy
1;Super Structure;;;When organisations form structures comprising other organisations, a super-structure is formed. In the context of this document, a super-structure is thus described as an organisation of organisations, esentially spanning further than the usual reaches of a given average organisation. Such an inter-organisational structure is formed above the conventional organisational structure.;64;An inter-organisational structure formed above the conventional organisational structure.;17;;;;;;\item[\textbf{DES}] When organisations form structures comprising other organisations, a super-structure is formed. In the context of this document, a super-structure is thus described as an organisation of organisations, esentially spanning further than the usual reaches of a given average organisation. Such an inter-organisational structure is formed above the conventional organisational structure.;\item[\textbf{DEF}] An inter-organisational structure formed above the conventional organisational structure.;;;;SuperStructure
1;Task;;;A task is the building block of a quest, i.e. its elementary part. A quest is built of atomic tasks that are easier to follow in execution phase, rather than the overview provided by the main definition of a quest. In \acp{MMORPG} a quest could demand an item to be retrieved, yet such a simple-sounding quest could consist of various tasks that have to be fulfilled in order for the main quest to be finished. The relation of quest and task concepts can be recursive\footnote{define further, i.e. a main quest in a game can consist of several tasks, which can be quests themselves}.;64;A task is the building block of a quest.;17;;;;;;\item[\textbf{DES}] A task is the building block of a quest, i.e. its elementary part. A quest is built of atomic tasks that are easier to follow in execution phase, rather than the overview provided by the main definition of a quest. In \acp{MMORPG} a quest could demand an item to be retrieved, yet such a simple-sounding quest could consist of various tasks that have to be fulfilled in order for the main quest to be finished. The relation of quest and task concepts can be recursive\footnote{define further, i.e. a main quest in a game can consist of several tasks, which can be quests themselves}.;\item[\textbf{DEF}] A task is the building block of a quest.;;;;Task
1;Time Dependent Norm;;;A time dependent norm is essentially a norm, but with an added temporal constraint. Particularly, a time dependent norm is constrained to a specific period in time, be it for its designated activity period, period during which the given norm is applicable, or simply the timeframe or a deadline when a change of the norm, or caused by the norm, is to be expected.;64;A norm that is dependent on the temporal aspect of the world is a time dependent norm.;17;Every 24 hours the Dragon Egg item is created again, rendering the old one useless.;;;;;\item[\textbf{DES}] A time dependent norm is essentially a norm, but with an added temporal constraint. Particularly, a time dependent norm is constrained to a specific period in time, be it for its designated activity period, period during which the given norm is applicable, or simply the timeframe or a deadline when a change of the norm, or caused by the norm, is to be expected.;\item[\textbf{DEF}] A norm that is dependent on the temporal aspect of the world is a time dependent norm.;\item[\textbf{INS}] Every 24 hours the Dragon Egg item is created again, rendering the old one useless.;;;TimeDependentNorm
1;Workspace;;W;A workspace is the complete environment of a given system, including all the agents, artefacts, etc. What sets the concept of a workspace apart from the concept of an environment is the extent of the involved concepts, i.e. a workspace contains all the elements of an organisation and the whole system, while environment comprises only the elements that are external to the given organisation. It is worth noting that elements of the environment are an integral part of the whole system, since the life and activities of the given organisation are influenced by them.;64;A workspace is the union of all the elements of a system, including agents, artefacts, etc.;17;;;;;(W);\item[\textbf{DES}] A workspace is the complete environment of a given system, including all the agents, artefacts, etc. What sets the concept of a workspace apart from the concept of an environment is the extent of the involved concepts, i.e. a workspace contains all the elements of an organisation and the whole system, while environment comprises only the elements that are external to the given organisation. It is worth noting that elements of the environment are an integral part of the whole system, since the life and activities of the given organisation are influenced by them.;\item[\textbf{DEF}] A workspace is the union of all the elements of a system, including agents, artefacts, etc.;;;;Workspace
