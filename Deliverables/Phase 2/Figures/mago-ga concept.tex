\documentclass[tikz]{standalone}

\input{Tikz copy.tex}

\begin{document}

\usetikzlibrary{trees}

\begin{tikzpicture}[
    concept/.style={rectangle, draw, very thin, rounded corners, minimum width=1.42cm, minimum height=3em, inner sep=1em},
    cgo/.style={concept, fill=blue!20},
    vgo/.style={concept, fill=orange!20},
    mago/.style={concept, fill=purple!20},
    mambo/.style={concept, fill=green!20},
    given/.style={concept, fill=yellow!20},
    %
    highlight/.style={concept, very thick, dash dot, draw=red!90!black},
    addition/.style={concept, dashed},
    cgo_addition/.style={cgo},
    vgo_addition/.style={vgo},
    mago_addition/.style={addition, mago},
    given_addition/.style={given},
    excluded/.style={concept, cross out},
    cgo_excluded/.style={excluded, draw=blue!60},
    vgo_excluded/.style={excluded, draw=orange!60},
    %
    onto/.style={cloud, cloud ignores aspect, cloud puffs=21.42, cloud puff arc=84, draw, thick, dash dot, inner sep=0pt, draw=red!90!black},
    cgo_onto/.style={onto, draw=blue!60},
    vgo_onto/.style={onto, draw=orange!60},
    mambo_onto/.style={onto, draw=green!60},
    %
    arrow/.style={-{Latex[scale=1.42]}, very thin},
    optional_arrow/.style={-{Latex[open, scale=1.42]}, very thin},
    cgo_arrow/.style={arrow, blue!30!black},
    vgo_arrow/.style={arrow, orange!30!black},
    mambo_arrow/.style={arrow, green!30!black},
    addition_arrow/.style={arrow, dashed},
    %
    node distance=5em and 5em,
    label/.style={font=\footnotesize\itshape},
    edge from parent path =  {},
    grow cyclic,
    level 1/.style={level distance=27em,sibling angle=25, clockwise from=225},
    level 2/.style={level distance=16em},
    level 3/.style={level distance=14em},
    level 4/.style={level distance=10em}
]

\node [font=\huge\bfseries] (mago_root) {MAGO-Ga} [
    % clockwise from=-120,
    % sibling angle=35,
    % level distance=6.3em,
    % sibling distance = 18em
    ]
    child {node [mambo] (mambo_Observable_Property) {Observable Property} 
        [
            clockwise from=-135,
        ]
        child {node [mambo] (mambo_Physical_Property) {Physical Property} }
        child {node [vgo_addition] (vgo_Feature) {Feature}
            [
                clockwise from=-150,
            ]
            child {node [mago_addition] (mago_Personality_Profile) {Personality Profile} }
            child {node [mago_addition] (mago_Age) {Age} }
            child {node [mago_addition] (mago_Gender) {Gender} }
        }
    }
    %
    child {node [mambo] (mambo_IVE) {IVE} 
        [
            clockwise from=205,
            sibling angle=30
        ]
        child {node [vgo_addition] (vgo_Game) {Game} }
        child {node [cgo_addition] (cgo_Game) {Game} }
    }
    %
    child {node [mambo] (mambo_Workspace) {Workspace} 
        [
            clockwise from=195,
        ]
        child {node [mambo] (mambo_IVE_Workspace) {IVE Workspace} 
            [
                clockwise from=-120,
            ]
            % An IVE Workspace is a physical location, or a physically describable location.
            child {node [vgo_addition] (vgo_PlayingArea) {PlayingArea} }
        }
    }
    %
    child {node [mambo] (mambo_Artefact) {Artefact} 
        [
            % sibling distance = 22em
        clockwise from=200,
        sibling angle=42,
        % sibling angle=60,
        % level distance=10em,
        ]
        child {node [mambo] (mambo_IVE_Artefact) {IVE Artefact} 
            [
                clockwise from=205,
                sibling angle=21,
            ]
            child {node [mambo] (mambo_Physical_Artefact) {Physical Artefact} 
                [
                    clockwise from=-100,
                ]
                child {node [vgo_addition] (vgo_Item) {Item} }
            }
            child {node [cgo_addition] (cgo_Visuals) {Visuals} }
            child {node [cgo_addition] (cgo_Audio) {Audio} }
            child {node [cgo_addition] (cgo_LevelDesign) {Level Design} }
        }
        child {node [vgo_addition] (vgo_Achievement) {Achievement} 
            % An Achievement is a reward that is given to a player for accomplishing a certain task in the game. An Achievement may depend on inventory, a specific rule, an item, etc.
        }
        child {node [mambo] (mambo_Knowledge_Artefact) {Knowledge Artefact} 
            [
                % sibling distance = 30em,
                clockwise from=135,
                sibling angle=50,
            ]
            child {node [mago_addition] (mago_Gamification_Technique) {Gamification Technique} }
            child {node [given_addition] (given_Inventory) {Inventory} 
                % An Inventory is a collection of items that a player can carry with them. It is a specific type of an Artefact. More generally, inventory stores all the data relevant to an agent.
            }
            child {node [mambo] (mambo_Norm) {Norm} 
                [
                    clockwise from=70,
                    sibling angle=32,
                ]
                child {node [mambo] (mambo_Role) {Role} }
                child {node [given_addition] (given_Rule) {Rule} }
                    % A Rule is a normative rule that is not part of a role. It is more strict than a Norm, which is defined as an accepted behaviour in a given context.
                    % A Local Rule is a rule that is applicable only in a specific context, i.e. a specific location in the IVE.
                child {node [given_addition] (given_Local_Rule) {Local Rule} }
                child {node [mambo] (mambo_IVE_Law) {IVE Law} }
            }
        }
    }
    child {node [mambo] (mambo_Action) {Action} }
    %
    child {node [mambo] (mambo_Agent) {Agent} 
        [
            clockwise from=45,
        ]
        % child {node [mambo] (mambo_Inhabitant_Agent) {Inhabitant Agent} }
        child {node [vgo_addition] (vgo_Player) {Player} }
        child {node [vgo_addition] (vgo_Character) {Character} }
    }
    %
    child {node [mambo] (mambo_Strategy) {Strategy}
        [
            clockwise from=21,
        ]
        child {node [cgo_addition] (cgo_Gameplay) {Gameplay} }
    }
    %
    child {node [mambo] (mambo_Process) {Process} 
        [
            clockwise from=30,
        ]
        child {node [mambo] (mambo_Activity) {Activity} 
            [
                clockwise from=135,
            ]
            child {node [mambo] (mambo_Behaviour) {Behaviour} }
        }
    }
    %
    child {node [cgo_addition] (cgo_Narrative) {Narrative} }
    %
    child {node [given_addition] (given_Quest) {Quest} 
        [
            clockwise from=55,
        ]
        % A Quest is a goal that has a defined beginning and a defined end, i.e. a starting situation, and an ending situation. In the context of \acp{MMORPG}, a quest is what drives a story, and, in principle, motivates the player to continue playing the game. Furthermore, a quest is often given to the player by an in-game character. A quest usually has various stages, and represents a challenge for the given player, thus embarking them on an adventure. Any quest can be achieved by a succession of atomic activities. A special kind of a quest is an objective, which is a high-level goal that is to be met.
        child {node [mambo] (mambo_Objective) {Objective}
            [
                clockwise from=167,
            ]
            child {node [mago_addition] (mago_Gamification_Goal) {Gamification Goal} }
        }
            % An objective can be achieved by an atomic activity. Objectives can trigger processes.
    }
    %
    child {node [mambo] (mambo_Observable_Event) {Observable Event} 
        [
            clockwise from=-60,
        ]
        child {node [vgo_addition] (vgo_Instantaneous_Event) {Instantaneous Event}}
    }
    child {node [mago_addition] (mago_Personality_Model) {Personality Model} }
    child {node [mago_addition] (mago_Personality_Feature) {Personality Feature} 
        [
            clockwise from=5,
        ]
        child {node [mago_addition] (mago_Trait) {Trait} }
        child {node [mago_addition] (mago_Facet) {Facet} }
    }
    child {node [mago_addition] (mago_Feature_Weight) {Feature Weight} }
    ;

\node [label, below=2em of mago_root] (legend_title) {Legend};
\node [mago, minimum width=6em, below=0.5em of legend_title] (mago_legend) {MAGO-Ga};
\node [label, below=1em of mago_legend] (legend_mambo) {GIVEn};
\node [mambo, minimum width=6em, below=0.5em of legend_mambo, xshift=3.5em] (mambo_legend) {MAMbO5};
\node [cgo, minimum width=6em, left=1em of mambo_legend] (CGO_legend) {CGO};
\node [vgo, minimum width=6em, below=1em of CGO_legend] (VGO_legend) {VGO};
\node [given, minimum width=6em, below=1em of mambo_legend] (GIVEn_legend) {GIVEn};

\node [draw, fit={
(CGO_legend)
(VGO_legend)
(mambo_legend)
(GIVEn_legend)
(legend_mambo)
}] (old_box) {};

\node [draw, fit={
(legend_title)
(mago_legend)
(old_box)
}] (legend_box) {};

\draw [arrow, mambo_arrow] (mambo_Knowledge_Artefact) to [bend left=21] node [label, right, pos=0.3] {is a} (mambo_Artefact);
\draw [arrow, mambo_arrow] (mambo_IVE_Artefact) to [bend left=21] node [label, above] {is a} (mambo_Artefact);
\draw [arrow, mambo_arrow] (mambo_Physical_Artefact) to [bend left=21] node [label, above left] {is a} (mambo_IVE_Artefact);
\draw [arrow] (cgo_Visuals) to [bend left=21] node [label, above left] {is a} (mambo_IVE_Artefact);
\draw [arrow] (cgo_Audio) to [bend left=21] node [label, pos=0.3, above] {is a} (mambo_IVE_Artefact);
\draw [arrow] (cgo_LevelDesign) to [bend left=21] node [label, pos=0.7, above right] {is a} (mambo_IVE_Artefact);
\draw [arrow] (vgo_Achievement) to [bend right=11] node [label, above right] {is a} (mambo_Artefact);
\draw [arrow, optional_arrow] (vgo_Achievement) to [bend left=21] node [label, above left, pos=0.2] {affects} (given_Inventory);
\draw [arrow] (vgo_Item) to [bend left=21] node [label, above left] {is a} (mambo_Physical_Artefact);
\draw [mambo_arrow] (mambo_Artefact) to [bend left=21] node [label, below right, pos=0.8] {related to} (mambo_IVE);

\draw [arrow] (vgo_Game) to [bend left=21] node [label, pos=0.5, above left] {is a} (mambo_IVE);
\draw [arrow] (cgo_Game) to [bend left=21] node [label, pos=0.5, above] {is a} (mambo_IVE);
\draw [mambo_arrow] (mambo_IVE) to [bend left=21] node [label, left] {contains} (mambo_Workspace);

\draw [arrow, mambo_arrow] (mambo_IVE_Artefact) to [bend left=21] node [label, above right] {located in} (mambo_Workspace);
\draw [arrow, mambo_arrow, optional_arrow] (mambo_Artefact) to [bend left=21] node [label, right, pos=0.05] {has} (mambo_Observable_Property);
\draw [arrow, mambo_arrow] (mambo_Physical_Property) to [bend right=21] node [label, above left] {is a} (mambo_Observable_Property);
\draw [arrow] (vgo_Feature) to [bend left=21] node [label, above left] {is a} (mambo_Observable_Property);

\draw [arrow, mambo_arrow] (mambo_IVE_Workspace) to [bend right=21] node [label, above left] {is a} (mambo_Workspace);
\draw [arrow] (vgo_PlayingArea) to [bend left=21] node [label, above left, pos=0.1] {is a} (mambo_IVE_Workspace);

\draw [arrow, mambo_arrow, optional_arrow] (mambo_Artefact) to [bend right=21] node [label, above left, pos=0.7] {induces} (mambo_Action);

\draw [mambo_arrow] (mambo_Norm) to [bend right=21] node [label, below right] {is a} (mambo_Knowledge_Artefact);
\draw [arrow] (given_Inventory) to [bend right=21] node [label, below right] {is a} (mambo_Knowledge_Artefact);
\draw [mambo_arrow] (mambo_Role) to [bend left=13] node [label, above right, pos=0.6] {provides} (mambo_Behaviour);
\draw [mambo_arrow] (mambo_Role) to [bend right=21] node [label, right] {is a} (mambo_Norm);
\draw [arrow] (given_Rule) to [bend right=21] node [label, below right] {is a} (mambo_Norm);
\draw [mambo_arrow] (mambo_IVE_Law) to [bend right=21] node [label, below left] {is a} (mambo_Norm);
\draw [arrow] (given_Local_Rule) to [bend right=21] node [label, left] {is a} (given_Rule);
\draw [arrow] (given_Local_Rule) to [bend left=21] node [label, below right, pos=0.3] {is a} (mambo_IVE_Law);

\draw [mambo_arrow] (mambo_IVE_Law) to [bend left=21] node [label, below right, pos=0.4] {depends on} (mambo_Workspace);

\draw [arrow, mambo_arrow] (mambo_Behaviour) to [bend left=21] node [label, right] {is a} (mambo_Activity);

\draw [arrow, mambo_arrow] (mambo_Activity) to [bend left=21] node [label, above right] {achieves} (mambo_Objective);
\draw [arrow, mambo_arrow] (mambo_Activity) to [bend right=21] node [label, below] {is a} (mambo_Process);

\draw [arrow] (given_Quest) to [bend left=21] node [label, above left] {consists of} (mambo_Objective);
\draw [arrow] (mambo_Objective) to [bend left=21] node [label, below right] {is a} (given_Quest);
\draw [addition_arrow] (mago_Gamification_Goal) to [bend right=21] node [label, above right] {is a} (mambo_Objective);

\draw [arrow] (vgo_Instantaneous_Event) to [bend right=21] node [label, left] {is a} (mambo_Observable_Event);
\draw [arrow, optional_arrow] (mambo_Observable_Event) to [bend right=16] node [label, below left, pos=0.05] {provides} (vgo_Achievement);
\draw [arrow, optional_arrow] (mambo_Observable_Event) to [bend right=21] node [label, above left] {depends on} (given_Quest);
\draw [arrow, optional_arrow] (mambo_Observable_Event) to [bend left=21] node [label, left] {affects} (cgo_Narrative);

\draw [arrow] (vgo_Player) to [bend right=5] node [label, below right] {is a} (mambo_Agent);
\draw [arrow] (vgo_Character) to [bend right=10] node [label, below, pos=0.1] {is a} (mambo_Agent);
\draw [arrow] (mambo_Agent) to [bend left=15] node [label, left, pos=0.75] {is described using} (given_Inventory);
\draw [mambo_arrow] (mambo_Agent) to [bend right=55] node [label, left, pos=0.05] {can access} (mambo_Role);
\draw [mambo_arrow] (mambo_Agent) to [bend left=42] node [label, below right, pos=0.3] {related to} (mambo_IVE);
\draw [mambo_arrow, optional_arrow] (mambo_Agent) to [bend left=42] node [label, below right, pos=0.6] {located in} (mambo_Workspace);
\draw [mambo_arrow, optional_arrow] (mambo_Agent) to [bend left=50] node [label, below right, pos=0.3] {has property} (mambo_Observable_Property);

\draw [arrow] (cgo_Gameplay) to [bend left=21] node [label, below right, pos=0.3] {is a} (mambo_Strategy);

\draw [arrow, optional_arrow] (cgo_Narrative) to [bend left=13] node [label, below right, pos=0.3] {depends on} (mambo_Workspace);

\draw [addition_arrow] (mago_Gender) to [bend left=21] node [label, above right] {is a} (vgo_Feature);
\draw [addition_arrow] (mago_Age) to [bend left=21] node [label, above] {is a} (vgo_Feature);
\draw [addition_arrow] (mago_Personality_Profile) to [bend left=21] node [label, above left] {is a} (vgo_Feature);

\draw [addition_arrow] (mago_Gamification_Technique) to [bend right=85] node [label, right, pos=0.2] {has gamification outcome} (vgo_Achievement);
\draw [addition_arrow] (mago_Gamification_Technique) to [bend right=10] node [label, above right, pos=0.75] {has gamification goal} (mago_Gamification_Goal);
\draw [addition_arrow] (mago_Gamification_Technique) to [bend right=10] node [label, above right, pos=0.75] {is a} (mambo_Knowledge_Artefact);

\draw [addition_arrow] (mago_Feature_Weight) to [bend right=84] node [label, above, yshift=1em] {has personality feature} (mago_Personality_Feature);
\draw [addition_arrow] (mago_Feature_Weight) to [bend left=15] node [label, below] {has personality profile} (mago_Personality_Profile);

\draw [addition_arrow] (mago_Trait) to [bend left=21] node [label, above left] {is a} (mago_Personality_Feature);
\draw [addition_arrow] (mago_Facet) to [bend left=21] node [label, above right] {is a} (mago_Personality_Feature);

\draw [addition_arrow] (mago_Personality_Feature) to [bend left=21] node [label, above left] {is a part of model} (mago_Personality_Model);

% Areas containing concepts of the Fundamental game elements

% \node [onto, fit={(cgo_Visuals) (cgo_Audio) (cgo_LevelDesign)}, pin={[label] 78:Game Aesthetics}] (game_aesthetics) {};

% \node [onto, inner sep=-1.5em, fit={(given_Rule) (given_Local_Rule) (mambo_IVE_Law) (mambo_Norm)}, pin={[label] 160:Game Mechanics}] (game_mechanics) {};

% \node [onto, inner sep=1em, fit={(cgo_Narrative)}, pin={[label] 135:Game Narrative}] (game_narrative) {};

\end{tikzpicture}
\end{document}
