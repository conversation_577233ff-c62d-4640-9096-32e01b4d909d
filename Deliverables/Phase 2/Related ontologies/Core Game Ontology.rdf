<?xml version="1.0"?>
<rdf:RDF xmlns="http://autosemanticgame.eu/ontologies/cgo#"
     xml:base="http://autosemanticgame.eu/ontologies/cgo"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:cgo="http://autosemanticgame.eu/ontologies/cgo#"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:adms="http://www.w3.org/ns/adms#"
     xmlns:foaf="http://xmlns.com/foaf/0.1/"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     xmlns:vann="http://purl.org/vocab/vann/"
     xmlns:dcterms="http://purl.org/dc/terms/">
    <owl:Ontology rdf:about="http://autosemanticgame.eu/ontologies/cgo#">
        <dc:creator>Owen Sacco</dc:creator>
        <dc:date>2016-12-01</dc:date>
        <dc:description rdf:resource="http://autosemanticgame.eu/ontologies/cgo/diagram/cgo.svg"/>
        <dc:description xml:lang="en">The Core Game Ontology (CGO) provides a light-weight vocabulary for describing video games. Video games can be seen as multi-faceted containging the following facets: visuals, audio, narrative, gameplay, game design and level design. Each facet can be regarded as an independent model and when each model are combined together, they form a game.

Visuals contain any visual output of a game, which range from photorealistic, to caricaturised, to abstract visuals. Audio includes background music such as a fully orchestrated soundtrack, sound effects, rewarding spimds and voice-acted dialogue. Narrative contains the interactive story of a game which makes up the game&apos;s plot. Game design contains all the game&apos;s mechanics that define the game&apos;s rules, which provide the structures and frames for play (for example winning and losing conditions) and actions available to the player. Level design includes the architecure of the spatial navigation of levels which determine how the player agent can progress from one point in the game to another. Gameplay consists of the players strategies whilst playing a game.</dc:description>
        <dc:rights xml:lang="en">This ontology is distributed under a Creative Commons Attribution License - http://creativecommons.org/licenses/by/4.0</dc:rights>
        <dcterms:created rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2016-12-01</dcterms:created>
        <dcterms:description>An ontology for describing video game information</dcterms:description>
        <dcterms:modified rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2016-12-23</dcterms:modified>
        <dcterms:partOf rdf:resource="http://autosemanticgame.eu"/>
        <dcterms:status rdf:resource="http://purl.org/adms/status/UnderDevelopment"/>
        <dcterms:title>The Core Game Ontology (CGO)</dcterms:title>
        <dcterms:type rdf:resource="http://purl.org/adms/assettype/Ontology"/>
        <vann:preferredNamespacePrefix>cgo</vann:preferredNamespacePrefix>
        <vann:preferredNamespaceUri>http://autosemanticgame.eu/ontologies/cgo#</vann:preferredNamespaceUri>
        <rdfs:comment xml:lang="en">The Core Game Ontology (CGO) is an ontology for describing information about video games.</rdfs:comment>
        <rdfs:label>CGO</rdfs:label>
        <owl:versionInfo>1.0.0</owl:versionInfo>
        <foaf:homepage rdf:resource="http://autosemanticgame.eu/ontologies/cgo"/>
    </owl:Ontology>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Annotation properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://purl.org/dc/elements/1.1/creator -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/creator"/>
    


    <!-- http://purl.org/dc/elements/1.1/date -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/date"/>
    


    <!-- http://purl.org/dc/elements/1.1/description -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/description"/>
    


    <!-- http://purl.org/dc/elements/1.1/rights -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/rights"/>
    


    <!-- http://purl.org/dc/terms/created -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/created"/>
    


    <!-- http://purl.org/dc/terms/description -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/description"/>
    


    <!-- http://purl.org/dc/terms/modified -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/modified"/>
    


    <!-- http://purl.org/dc/terms/partOf -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/partOf"/>
    


    <!-- http://purl.org/dc/terms/status -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/status"/>
    


    <!-- http://purl.org/dc/terms/title -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/title"/>
    


    <!-- http://purl.org/dc/terms/type -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/terms/type"/>
    


    <!-- http://purl.org/vocab/vann/preferredNamespacePrefix -->

    <owl:AnnotationProperty rdf:about="http://purl.org/vocab/vann/preferredNamespacePrefix"/>
    


    <!-- http://purl.org/vocab/vann/preferredNamespaceUri -->

    <owl:AnnotationProperty rdf:about="http://purl.org/vocab/vann/preferredNamespaceUri"/>
    


    <!-- http://xmlns.com/foaf/0.1/homepage -->

    <owl:AnnotationProperty rdf:about="http://xmlns.com/foaf/0.1/homepage"/>
    


    <!-- http://xmlns.com/foaf/0.1/name -->

    <owl:AnnotationProperty rdf:about="http://xmlns.com/foaf/0.1/name"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Datatypes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://www.w3.org/2001/XMLSchema#date -->

    <rdfs:Datatype rdf:about="http://www.w3.org/2001/XMLSchema#date"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Object Properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasAudio -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasAudio">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Audio"/>
        <rdfs:comment xml:lang="en">Specifies the music and sound of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has audio</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasGameDesign -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasGameDesign">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#GameDesign"/>
        <rdfs:comment xml:lang="en">Specifies the rules and mechanics of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has game design</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasGameplay -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasGameplay">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Gameplay"/>
        <rdfs:comment xml:lang="en">Specifies the gameplay of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has gameplay</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasLevelDesign -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasLevelDesign">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#LevelDesign"/>
        <rdfs:comment xml:lang="en">Specifies the levels of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has level design</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasNarrative -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasNarrative">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Narrative"/>
        <rdfs:comment xml:lang="en">Specifies the story and plot of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has narrative</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasVisuals -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasVisuals">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Visuals"/>
        <rdfs:comment xml:lang="en">Specifies the visuals of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has visuals</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Audio -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Audio">
        <rdfs:comment xml:lang="en">Audio includes background music such as a fully orchestrated soundtrack, sound effects, rewarding spimds and voice-acted dialogue.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Audio</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Game -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Game">
        <rdfs:comment xml:lang="en">Specifies a video game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Game</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#GameDesign -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#GameDesign">
        <rdfs:comment xml:lang="en">Game design contains all the game&apos;s mechanics that define the game&apos;s rules, which provide the structures and frames for play (for example winning and losing conditions) and actions available to the player.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Game Design</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Gameplay -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Gameplay">
        <rdfs:comment xml:lang="en">Gameplay consists of the players strategies whilst playing a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Gameplay</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#LevelDesign -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#LevelDesign">
        <rdfs:comment xml:lang="en">Level design includes the architecure of the spatial navigation of levels which determine how the player agent can progress from one point in the game to another.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Level Design</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Narrative -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Narrative">
        <rdfs:comment xml:lang="en">Narrative contains the interactive story of a game which makes up the game&apos;s plot.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Narrative</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Visuals -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Visuals">
        <rdfs:comment xml:lang="en">Visuals contain any visual output of a game, which range from photorealistic, to caricaturised, to abstract visuals.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Visuals</rdfs:label>
    </owl:Class>
    


    <!-- http://www.w3.org/ns/adms#SemanticAsset -->

    <owl:Class rdf:about="http://www.w3.org/ns/adms#SemanticAsset"/>
    


    <!-- http://xmlns.com/foaf/0.1/Person -->

    <owl:Class rdf:about="http://xmlns.com/foaf/0.1/Person"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Individuals
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo# -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/cgo#">
        <rdf:type rdf:resource="http://www.w3.org/ns/adms#SemanticAsset"/>
    </owl:NamedIndividual>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#owensacco -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/cgo#owensacco">
        <rdf:type rdf:resource="http://xmlns.com/foaf/0.1/Person"/>
        <owl:sameAs rdf:resource="http://autosemanticgame.eu/ontologies/gco#owensacco"/>
        <rdfs:label>Owen Sacco</rdfs:label>
        <foaf:homepage rdf:resource="http://www.owensacco.com"/>
        <foaf:name>Owen Sacco</foaf:name>
    </owl:NamedIndividual>
    


    <!-- http://autosemanticgame.eu/ontologies/gco#owensacco -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/gco#owensacco"/>
    


    <!-- http://autosemanticgame.eu/ontologies/gco#owensacco -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/gco#owensacco"/>
</rdf:RDF>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

