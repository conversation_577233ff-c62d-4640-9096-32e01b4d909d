<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<catalog prefer="public" xmlns="urn:oasis:names:tc:entity:xmlns:xml:catalog">
    <group id="Folder Repository, directory=, recursive=true, Auto-Update=true, version=2" prefer="public" xml:base="">
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://autosemanticgame.eu/ontologies/cgo" uri="Core%20Game%20Ontology.rdf"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://personales.upv.es/ccarrasc/JaCalIVE_ontology" uri="JaCalIVE_Ontology.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://personales.upv.es/ccarrasc/ooooaflsmas.owl" uri="ooooaflsmas.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://purl.org/net/GameOntology" uri="Video%20Game%20Ontology.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://surveys.nees.com.br/ontologies/gado_core.owl" uri="gado_core.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://surveys.nees.com.br/ontologies/gado_full.owl" uri="gado_full.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37" uri="OntoGamif.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl" uri="MAMbO5.owl"/>
        <uri id="Automatically generated entry, Timestamp=1750688206017" name="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf" uri="GIVEn.rdf"/>
    </group>
</catalog>
