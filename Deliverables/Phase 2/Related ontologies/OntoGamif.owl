<?xml version="1.0"?>
<rdf:RDF xmlns="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#"
     xml:base="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#">
    <owl:Ontology rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Object Properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Composed_of_psychological_needs -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Composed_of_psychological_needs">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intrinsic_motivation"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines_gamification_rule -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines_gamification_rule">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines_psychological_outcome -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defines_psychological_outcome">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_outcome"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Designed_according_to -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Designed_according_to">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_system"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluate_gamification_goal -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluate_gamification_goal">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluates -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluates">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_outcome"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Focused_on -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Focused_on">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_actor -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_actor">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_behaviour -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_behaviour">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_game_type -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_game_type">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_ethical_issue -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_ethical_issue">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_evaluation_type -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_evaluation_type">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation_type"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_goal -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_goal">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_risk -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_risk">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_rule -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_gamification_rule">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_level -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_level">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_gamification_applying_level -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_gamification_applying_level">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_goal -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_goal">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_structure -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_organization_structure">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_structure"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_privacy -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_privacy">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_privacy"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_type -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Has_type">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Implement_gamification -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Implement_gamification">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_system"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Improved_by -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Improved_by">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Influences -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Influences">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Influences_gamification_outcome -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Influences_gamification_outcome">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_in -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_in">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_in_organization -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_in_organization">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_on_gamification_application_level -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_applied_on_gamification_application_level">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_based_on -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_based_on">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_composed_of_organizational_element -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_composed_of_organizational_element">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_inspired_by_game -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_inspired_by_game">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_motivated_by -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_motivated_by">
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intrinsic_motivation"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_activity -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_activity">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Activity"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_gamification_rule -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_gamification_rule">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_code_of_ethics"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_task -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_set_of_task">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Activity"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_step_in -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Is_step_in">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Meets_organization_objective -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Meets_organization_objective">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Prevent_risk -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Prevent_risk">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Protects_user -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Protects_user">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Realises_gamification_goal -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Realises_gamification_goal">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Regulate -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Regulate">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Searches_for -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Searches_for">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Solves_gamification_ethical_issue -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Solves_gamification_ethical_issue">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_analytic_tool -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_analytic_tool">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytics"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytic_tool"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_gamification_design_element -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_gamification_design_element">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element"/>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_metric -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Uses_metric">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytic_tool"/>
        <rdfs:range rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_metric"/>
    </owl:ObjectProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achievable_gamification_element -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achievable_gamification_element">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
        <rdfs:comment xml:lang="en">Achievable Gamification Elements: Gamification experts can explore user progression statistics of achievable gamification elements such as badges, levels, or missions to see the overall progression of users in the gamification design. This can help to understand how attractive particular gamification elements are and to identify aspects of the gamification design where adaptations may make sense. A gamification design might, for example, require adaptation, when already 60 % of the users have reached the highest level

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achievement -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achievement">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achiever -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achiever">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Achievers are motivated by competence. They seek to progress within a system by completing tasks, or prove themselves by tackling difficult challenges.

Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke
-----------------------
Achievers regard points-gathering and rising in levels as their main goal, and all is ultimately subservient to this

Reference: [225]
Title: HEARTS, CLUBS, DIAMONDS, SPADES: PLAYERS WHO SUIT MUDS
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Activity -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Activity">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element"/>
        <rdfs:comment xml:lang="en">rdfs:comment &quot;Reference: [210]
Title: A Method to Engage Employees using Gamification in BPO Industry
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Addiction -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Addiction">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">Based on the literature, some game features and sensations like flow can be regarded as addictive factors. Thus, addiction could be a potential problem in gamified environments.

---------------------
Reference: [168]
Title: The Bright and Dark Sides of Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Aesthetic_experience_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Aesthetic_experience_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">Developed by Dewey (1934), the TAE suggests that an aesthetic experience is an avenue to finding meaningful connections between an individual’s own interests and desired behavior; it should not be confined to the experience of viewing the highly reverenced and distant art of museums. Aesthetic experience can emerge in our ordinary lives and should be understood as more than a sensory experience, like visual beauty
------------
Reference: [101]
Title: Applying Game Design Elements in the Workplace
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Agreeableness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Agreeableness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
        <rdfs:comment xml:lang="en">Agreeableness: help others and expect help in return
----------------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Altruism -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Altruism">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Altruism can be satisfied by making it possible for the community to give and receive gifts.

Refrence: [198]
Titile: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Apprentice -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Apprentice">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile"/>
        <rdfs:comment xml:lang="en">Apprentice: After becoming familiar with the system, users will have to perform several actions in sequence that will lead them to associate the knowledge they have acquired either by experience or because they have asked for help from other users
----------------
Reference: [51]
Title: Design and evaluation of a gamified system for ERP training
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Autonomy_need -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Autonomy_need">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need"/>
        <rdfs:comment xml:lang="en">The need for autonomy refers to psychological freedom and to volition to fulfill a certain task
-----------------
Reference:[18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Avatar -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Avatar">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Avatars are visual representations of players within the game or gamification environment. Usually they are chosen or even created by the player. Avatars can be designed quite simply as a mere pictogram, or they can be complexly animated, three-dimensional representations

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Badge -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Badge">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Badges consist of optional rewards and goals, the fulfilment of which is located outside the scope of the core activities of a service. On a systemic level, a badge consists of a signifying element (the visual and textual cues of the badge), rewards (the earned badge), and the fulfilment conditions which determine how the badge can be earned.

Reference: [13]
Title: Do badges increase user activity? A field experiment on the effects of gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Basic_psychological_needs_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Basic_psychological_needs_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory"/>
        <rdfs:comment xml:lang="en">BPNT is a sub-theory of self-determination theory (SDT), which is a widely studied macro-theory of human motivation. BPNT further explains what factors make people feel that a certain activity is enjoyable. According to BPNT, conditions supporting individuals’ experience of autonomy, competence, and relatedness stimulate their intrinsic motivation, which in turn increases their levels of engagement in activities. BPNT argues that individuals’ intrinsic motivation for a particular activity can be predicated by autonomy, competence, and relatedness
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Beginner -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Beginner">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile"/>
        <rdfs:comment xml:lang="en">Beginner: In the beginning, users will want to explore and try the system. They will want to achieve goals, progress, and win rewards
----------------
Reference: [51]
Title: Design and evaluation of a gamified system for ERP training
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_outcome"/>
        <rdfs:comment xml:lang="en">Site participation, steering behavior, Impact on learning (usefulness), Level of participation and quality of participation, Learning outcomes, Effectiveness of learning, Response patterns, Quality of completed tasks, task completion speed, Amount of content contributed/users contributing, type of content distributed, individual users&apos; contribution amounts, Exploration of the campus while interacting with the application, Quality of collected calibration data, Behavior change due to receiving badges, Change in relative energy consumption, Impact on time management, carefulness and achieving learning goals, Number and duration of interactions with virtual patients, Amount and quality of user activities, Intentions to use, intentions to recommend, Performance, Task Performance, Increasing knowledge, Total number of contributions of various content types
---------------------------
Reference: [110]
Title: Does Gamification Work? — A Literature Review of Empirical Studies on
Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Boss_fight -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Boss_fight">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Boss fights: rare, extremely hard challenges.

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Causality_orientation_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Causality_orientation_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory"/>
        <rdfs:comment xml:lang="en">Sub-theory of SDT
---------------
Reference: [12]
Title: Towards understanding the effects of individual gamification elements on intrinsic motivation and performance
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Chance -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Chance">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Chance: bringing in randomness to the game.

Refrence: [223]
Title:  &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Character -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Character">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ehical_issue_in_gamification_world"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Manipulation"/>
        <rdfs:comment xml:lang="en">Character: incentives can sometimes have a negative effect upon people’s character traits. A standard example is that parents often hesitate to use candy as a reward to change their child’s behavior, not just for health related reasons, but for its negative impact upon important social character traits like autonomy, self-governance, etc.

---------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cheating -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cheating">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">If underlying rules are not clearly defined, it enables cheating, which can lead to rejection of implemented game elements by other employees.

--------------------
Reference: [114]
Title: Gamifying Information Systems – A Synthesis of Gamification Mechanics and Dynamics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cognitive_evaluation_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cognitive_evaluation_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory"/>
        <rdfs:comment xml:lang="en">Developed by Deci (1975), cognitive evaluation theory (CET) explains how individuals’ intrinsic motivations are affected by external stimuli. The CET is a sub-theory of the self-determination theory (SDT), a widely studied macro-theory of human motivation suggesting that people are more likely to be engaged in an activity when they are intrinsically motivated. CET’s contribution is that it identifies the factors that make people intrinsically motivated (feeling that a certain activity is enjoyable). According to the CET, people have basic psychological needs—autonomy, competence, and relatedness—when they are engaged in a particular activity; these three factors are the main sources of intrinsic motivation

------------
Reference: [101]
Title: Applying Game Design Elements in the Workplace
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Collection -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Collection">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Collections: sets of items/badges to accumulate.

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Combat -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Combat">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Combat : a defined battle, typically short-lived.

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Common_user_characteristic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Common_user_characteristic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Detailed_metric"/>
        <rdfs:comment xml:lang="en">User Characteristics: Some gamification elements might be more attractive to particular groups of users than to others. To identify such constellations, gamification experts can explore which properties users have in common, who share the same state on a particular gamification element. Properties can be gamification properties or user properties. Gamification properties originate from the user’s state in the game, e.g., owns badge A, while user properties originate from the information the application has about the user, e.g., from geographical region Europe.

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competence_need -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competence_need">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need"/>
        <rdfs:comment xml:lang="en">The need for competence refers to feelings of efficiency and success while interacting with the environment.
-----------------
Reference:[18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competition -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competition">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Reference: [66]
Title: Are we playing yet? A review of gamified enterprise systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competitiveness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competitiveness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Emotion"/>
        <rdfs:comment>Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Conscientiousness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Conscientiousness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
        <rdfs:comment xml:lang="en">Conscientiousness : actively plan, organize and carry out tasks
--------------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Constraint -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Constraint">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Constraints : limitations or forced trade-offs

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Content_unlocking -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Content_unlocking">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Content unlocking: additional game content available after players reach certain objectives

Refrence: [223]
Title:  &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Copyright -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Copyright">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation"/>
        <rdfs:comment xml:lang="en">Every time something is “in motion” or “user-generated” within a gamification project, copyright laws will help to govern these parts

-------------------
Reference: [103]
Title: On the Legal Implications of Using Gamifed Elements
Author: Kai Erenli</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Core_business_process -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Core_business_process">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Supporting_and_management_process"/>
        <rdfs:comment xml:lang="en">Reference:[66]
Title: Are we playing yet? a review of gamified enterprise systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Declining_effect -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Declining_effect">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">Can occur once the novelty of Gamification has worn off as, for instance, challenges might gradually be perceived as too simple.

------------------
Reference: [114]
Title: Gamifying Information Systems – A Synthesis of Gamification Mechanics and Dynamics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Deepest_level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Deepest_level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
        <rdfs:comment xml:lang="en">At the deepest level, embedded, the activity is designed based on the mechanics, for example, an activity is divided into multiple sub-tasks each providing a stage in a quest.
-------------------------------
Reference: [210]
Title: A Method to Engage Employees using Gamification in BPO Industry
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Detailed_metric -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Detailed_metric">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_metric"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Discovery -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Discovery">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic"/>
        <rdfs:comment>Rerence: [217]
Title:  MDA: A formal approach to game design and game research
Author: Hunicke, R., LeBlanc, M., &amp; Zubek, R</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Disruptor -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Disruptor">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Disruptors are motivated by the triggering of change. They tend to disrupt the system either directly or through others to force negative or positive changes. They like to test the system’s boundaries and try to push further
---------------------
Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ehical_issue_in_gamification_world -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ehical_issue_in_gamification_world">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world"/>
        <rdfs:comment xml:lang="en">Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Emotion -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Emotion">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Emotions driving players, such as: curiosity, competitiveness, frustration, happiness etc


Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
        <rdfs:comment xml:lang="en">Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts"/>
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment>Reference: [85]
Title: Gamifying Research: Strategies, Opportunities, Challenges, Ethics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_before_gamification"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_before_gamification -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_before_gamification">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_metric -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_metric">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts"/>
        <rdfs:comment xml:lang="en">Metrics: the standards of measurement by which efficiency, performance, progress, process or quality.

------------------------
Reference: [73]
Title: A literature review of gamification design frameworks
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Experience_point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Experience_point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point"/>
        <rdfs:comment>Experience points: Of the five kinds of point systems, the most important are experience points (XP). Unlike airline miles, XP do not serve as any type of currency within the system. They are how you watch, rank, and guide your player. Everything a player does within the system will earn her XP—and, in general, XP never goes down and cannot be redeemed.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Expert -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Expert">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile"/>
        <rdfs:comment xml:lang="en">Expert: Users who evolve into experts would have perfected their knowledge, so they will be looking for special activities that would keep them engaged in the system.
----------------
Reference: [51]
Title: Design and evaluation of a gamified system for ERP training
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Exploitation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Exploitation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world"/>
        <rdfs:comment xml:lang="en">Exploitation: Game designer and critic Ian Bogost dubs gamification ‘‘exploitationware.’’ He writes: ‘‘…gamification proposes to replace real incentives with fictional ones …Organizations ask for loyalty, but they reciprocate that loyalty with shames, counterfeit incentives that neither provide
value nor require investment. When seen in this light, ‘gamification’ is a misnomer. A better name for this practice is ‘exploitationware’’’

------------------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Explorer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Explorer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Explorers delight in having the game expose its internal machinations to them. They try progressively esoteric actions in wild, out-of-the-way places, looking for interesting features (i.e. bugs) and figuring out how things work.

---------------------
Reference: [225]
Title: HEARTS, CLUBS, DIAMONDS, SPADES: PLAYERS WHO SUIT MUDS
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#External_regulation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#External_regulation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level"/>
        <rdfs:comment xml:lang="en">Doing something because we must do it, sometimes getting punished if we will not do it.
-------------------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extraversion -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extraversion">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
        <rdfs:comment xml:lang="en">Extraversion: seek out new opportunities and excitement
--------------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_motivation"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intrinsic_motivation"/>
        <rdfs:comment xml:lang="en">Extrinsic motivation is defined as doing something due to a separable outcome, such as pressure or “extrinsic rewards” in the form of money or verbal feedback (e.g., praise)

Reference: [12]
Title: Towards understanding the effects of individual gamification elements on intrinsic motivation and performance
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts"/>
        <rdfs:comment xml:lang="en">There are four levels in extrinsic motivation which someone needs to go through to get intrinsically motivated

------------------------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Feedback -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Feedback">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Feedback providing players with information about how they are doing.

Refrence: [223]
Title:  &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Fellowship -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Fellowship">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic"/>
        <rdfs:comment>Rerence: [217]
Title:  MDA: A formal approach to game design and game research
Author: Hunicke, R., LeBlanc, M., &amp; Zubek, R</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Flow_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Flow_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">Flow describes a mental state where an individual is involved and engaged in an activity and where he is focused on the current activity.

Reference: [93]
Title: Creating a Theory-Based Research Agenda for Gamification
Authors: <AUTHORS>
------------------------
The flow theory sets an optimal mental status to perform a specific task. In order to achieve this status a match between the difficulty of the task and the skills of the person is required

Reference:[257]
Title: Creativity: Flow and the Psychology of Discovery and Invention
Authors: <AUTHORS>
----------------------
Seligman and Csikszentmihalyi (2000) defined it as “the study of positive emotion, positive character, and positive institutions.” Csikszentmihalyi, as one of the pioneers in the field of positive psychology, was captivated by the fact that some people, despite their tremendous losses during the war, were still happy and showed their happiness while others could not. It was then that he produced the Flow Theory (see Fig. 1.6) by treating happiness as a positive, personal state of
being

Reference: [198]
Title: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Fogg_behaviour_model -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Fogg_behaviour_model">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">Fogg Behavior Model’ (FBM)  which encompasses motivation, ability and triggers

Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
-----------------------
Reference: [258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Free_spirit -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Free_spirit">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Free Spirits are motivated by autonomy, meaning freedom to express themselves and act without external control. They like to create and explore within a system.
-------------------
Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Frustration -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Frustration">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Emotion"/>
        <rdfs:comment>Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment xml:lang="en">A system in which players engage in an artificial conflict, defined by rules, that results in a quantifiable outcome

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
-----------------------
The exercise of voluntary control systems in which there is an opposition between forces, confined by a procedure and rules in order to produce a disequilibrial outcome

Reference: [19]
Title: A definition for gamification: anchoring gamification in the service marketing literature
Authors: <AUTHORS>
-----------------------
A rule-based formal system with a variable and quantifiable outcome, where different outcomes are assigned different values, the player exerts effort in order to influence the outcome, the player feels attached to the outcome, and the consequences of the activity are optional and negotiable

Reference: [19]
Title: A definition for gamification: anchoring gamification in the service marketing literature
Authors: <AUTHORS>
-----------------------
Games are voluntary activities bounded by rules, but further require conflict between equal parties and an unequal end result

Reference: [84]
Title: Gamification in theory and action: A survey
Authors: <AUTHORS>
-----------------------
Games are representations of some reality, be predicated on interaction between the system and the user, and provide conflict but also safety through simulation

Reference: [84]
Title: Gamification in theory and action: A survey
Authors: <AUTHORS>
-----------------------
All games have the six main features: rules; variable; quantifiable outcomes; value-laden outcomes; player effort; player investment; and negotiable consequences, with respect to real life effects.

Reference: [84]
Title: Gamification in theory and action: A survey
Authors: <AUTHORS>
-----------------------
An activity that is voluntary and enjoyable, separate from the real world, uncertain, unproductive in that the activity does not produce any goods of external value, and governed by rules

Reference: [198]
Title: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game_with_purpose -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game_with_purpose">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
        <rdfs:comment xml:lang="en">Reference:[209]
Title: Gamification: Design of IT-Based Enhancing Services for Motivational Support
and Behavioral Change
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment>The use of game design elements in non-game contexts

Reference: [123]
Title: From game design elements to gamefulness: Defining &quot;gamification&quot;
Authors: <AUTHORS>

-----------------------------------
A process of enhancing a service with affordances for gameful experiences in order to support user’s overall value creation

Reference: [19]
Title: A definition for gamification: anchoring gamification in the service marketing literature
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element"/>
        <rdfs:comment>Aesthetics Of the system are how the game makes the player feel during interaction. Game aesthetics can be viewed as the composite outcome of the mechanics and dynamics as they interact with and create emotions.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytic_tool -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytic_tool">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts"/>
        <rdfs:comment xml:lang="en">Software used to measure the success of gamification projects, to analyze user behavior, and to continuously improve gamification designs. Existing software solutions promise support for analyzing game and gamification-related data [106]

Reference: [106]
Title: Tools for Gamification Analytics: A Survey
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytics -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_analytics">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation_type"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Heuristic_evaluation"/>
        <rdfs:comment xml:lang="en">Gamification analytics describe methods and tools that help to monitor the success of gamification projects, to understand a user’s behaviour, and to adapt gamification designs.

We define gamification analytics as the data-driven processes of monitoring and adapting gamification designs

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_code_of_ethics -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_code_of_ethics">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation"/>
        <rdfs:comment xml:lang="en">A gamification code of ethics proposition is available at:

https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consultant -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consultant">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: Gamifcation and Law On the Legal Implications of Using Gamifed Elements
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consumer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consumer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: Gamifcation and Law On the Legal Implications of Using Gamifed Elements
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_cost -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_cost">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
        <rdfs:comment xml:lang="en">Reference: [214]
Title: Understanding Gamification Mechanisms for Software Development 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_approach">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [49]
Title: “Don’t Whip Me With Your Games” – Investigating “Bottom-Up” Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment>The intentional use of game elements for a gameful experience of non-game tasks and contexts

Reference:[84]
Title: Gamification in theory and action: A survey
Authors: <AUTHORS>
---------------------------------
Game elements refer to specific instances of the dynamics and game mechanics. Although the number of game elements is infinite and its limit is imposed only by creativity, the most common ones are points, badges and leaderboards.

Reference:[223]
Title: For the Win: How Game Thinking Can Revolutionize Your Business
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_designer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_designer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider"/>
        <rdfs:comment xml:lang="en">A person who creates the design for a gamified system

--------------
Reference: [41]
Title: Design Patterns for Gamification of Work
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [7]
Title: Strategies for Playful Design when Gamifying Rehabilitation. A Study on User Experience
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element"/>
        <rdfs:comment>Dynamics determine individuals’ reactions as a response to using implemented mechanics (e.g., competition among players)

Reference: [66]
Title: Are we playing yet? A review of gamified enterprise systems
Authors: <AUTHORS>

-----------------------------------------------
Dynamics: big-picture aspects considered but which can never directly be applied to the game

Reference: [71]
Title: &quot;Everybody is playing the game, but nobody&apos;s rules are the same&quot;: Towards adaptation of gamification based on users&apos; characteristics
Authors: <AUTHORS>

------------------------------------------------
Dynamics, describing the run-time behavior of the mechanics acting on player inputs and each other’s outputs over time.

Reference: [73]
Title: A Literature Review of Gamification Design Frameworks
Authors: <AUTHORS>

--------------------------------------------------
Dynamics, meanwhile, are the player&apos;s interactions with those mechanics. They determine what each player is doing in response to the mechanics of the system, both individually and With Other players. Sometimes, game mechanics and game dynamics are used interchangeably, but they are markedly different.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>

-----------------------------------------------------
The dynamics of the game has to do with to empower the objectives and the potential effects on the people involved. In fact, these dynamics are highly related to the human needs and concerns that motivate people intrinsically. Thus, despite the absence of a closed taxonomy, we might find those dynamics that enhance emotions, narrative, sense of achievement, or even relationships.

Reference: [223]
Title: For the Win: How Game Thinking Can Revolutionize Your Business
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_element_state_user_distribution -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_element_state_user_distribution">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Detailed_metric"/>
        <rdfs:comment xml:lang="en">User Distribution on Gamification Element State: Users can have multiple states in relation to a particular gamification element. For missions, typical states, for example, comprise Mission Completed, Mission Active, and Not Assigned to mission

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
----------------------
Ethics: a branch of philosophy that involves systematizing, defending and recommending concepts of right and wrong conducts

Reference: [73]
Title: A literature review of gamification design frameworks
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation_type -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation_type">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_concepts"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_feedback_rate -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_feedback_rate">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
        <rdfs:comment xml:lang="en">Gamification Feedback Rate: Gamification feedback is any state change in the game that the user perceives as a success, e.g., by gaining points or receiving a badge. Correspondingly, the feedback rate describes the total amount of feedback per time users spent in the gamified application.

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_design_element"/>
        <rdfs:comment>Mechanics are functional components providing various actions and control mechanisms (e.g., point systems, leaderboards, levels, and challenges

Reference: [66]
Title: Are we playing yet? A review of gamified enterprise systems
Authors: <AUTHORS>

--------------------------------------------------
Mechanics: basic process to drive action forward and generate player engagement describing the particular components of the

Reference: [71]
Title: &quot;Everybody is playing the game, but nobody&apos;s rules are the same&quot;: Towards adaptation of gamification based on users&apos; characteristics
Authors: <AUTHORS>
--------------------------------------------------
Mechanics, describing the particular components of the game, at the level of data representation and algorithms

Reference: [73]
Title: A Literature Review of Gamification Design Frameworks
Authors: <AUTHORS>
--------------------------------------------------
Mechanics make up the functioning components of the game. At their care, they allow a designer ultimate control over the levers of the game, giving her the ability to guide player actions.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps
Authors: <AUTHORS>
--------------------------------------------------
Game mechanics can be considered as the basic actions that motivate and engage the user, and thus achieve the objectives specified by the game.

Reference: [223]
Title: For the Win: How Game Thinking Can Revolutionize Your Business
Authors: <AUTHORS>
--------------------------------------------------</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">Reference: [110]
Title: Does Gamification Work? — A Literature Review of Empirical Studies on
Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: Gamifcation and Law On the Legal Implications of Using Gamifed Elements
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts"/>
        <rdfs:comment>Potential risks that need to be considered when making a decision about the application of gamification 

Reference: [114]
Title: Gamifying Information Systems – A Synthesis of Gamification Mechanics and Dynamics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_concepts"/>
        <rdfs:comment xml:lang="en">Gamfication rules constitute the gamification code of ethics

Available at:  https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_system -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_system">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_metric"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gifting -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gifting">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Gifting: players’ ability to share their resources,

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: On the Legal Implications of Using Gamifed Elements
Author: Kai Erenli</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Goal_setting_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Goal_setting_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">Goal-setting theory was originally developed by Locke (1968), who proposed that people will be motivated to strive towards goals. This approach is effective due to the psychological process of self-regulation, which acts as a mediator (i.e., intermediary causal process) between set goals and performance
---------------------
Reference: [11]
Title: Gamification of task performance with leaderboards: A goal setting experiment
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Graph -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Graph">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment></rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Happiness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Happiness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Emotion"/>
        <rdfs:comment>Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Harm -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Harm">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world"/>
        <rdfs:comment xml:lang="en">------------------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Heuristic_evaluation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Heuristic_evaluation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_evaluation_type"/>
        <rdfs:comment xml:lang="en">Heuristics are general principles or broad usability guidelines that have been used to design and evaluate interactive systems. Heuristic evaluation is the use of said principles as a usability inspection method by experts to identify usability problems in an existing design as part of an iterative design process

-----------
Reference:[44]
Title: Heuristic Evaluation for Gameful Design
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Honesty -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Honesty">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:comment>The list of gamification rules is available at : Available at: 

 https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Identification -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Identification">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level"/>
        <rdfs:comment xml:lang="en">Doing something because we can identify with the meaning but still need some trigger.
-------------------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_altruism -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_altruism">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_brainstorming -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_brainstorming">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_company_loyalty -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_company_loyalty">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_competition -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_competition">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_effectiveness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_effectiveness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_information_dissemination -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_information_dissemination">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_motivation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_motivation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_pleasure -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_pleasure">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_productivity -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_productivity">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_self_expression -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_self_expression">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_skill_building -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_skill_building">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_social_capital -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_social_capital">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_task_engagement -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Increase_task_engagement">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Infinite_leaderboard -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Infinite_leaderboard">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Leaderboard"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#No_disincentive_leaderboard"/>
        <rdfs:comment>The infinite leaderboard: In an arcade, there are not too many ways to allow every player to exist on a given game’s leaderboard forever. At some point, a player’s score will be beaten and she will fall off—or she will hit a number and sit there for weeks until someone finally beats it. In today’s world, there are ways to control leaderboards such that no player ever falls off or gets stuck

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Information_system -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Information_system">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrated_level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrated_level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
        <rdfs:comment xml:lang="en">At integrated level, the game mechanics are integrated into the activity being performed, for example, points are provided based on the progress, quality of work etc. 
-------------------------------
Reference: [210]
Title: A Method to Engage Employees using Gamification in BPO Industry
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integration -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integration">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level"/>
        <rdfs:comment xml:lang="en">Doing something because it is related to own goals, no real need for a trigger but not yet fully intrinsic.
-------------------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrity -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrity">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:comment>The list of gamification rules is available at : Available at: 

 https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intrinsic_motivation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intrinsic_motivation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_motivation"/>
        <rdfs:comment xml:lang="en">Intrinsic motivation is supported in SDT by three components. Competence marks the feeling of having the skills needed to accomplish the task at hand. Autonomy means the more in control of a situation a person feels, the more likely they are to succeed. Finally, relatedness is the feeling of
involvement with others 

Reference: [43]
Title: The Gamification User Types Hexad Scale 
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke
----------------------
Intrinsic motivation denotes the pursuit of an activity, because it is inherently interesting or enjoyable

Reference: [12]
Title: Towards understanding the effects of individual gamification elements on intrinsic motivation and performance
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Introjection -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Introjection">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extrinsic_motivation_level"/>
        <rdfs:comment xml:lang="en">Doing something for status, self-esteem or social acceptance.
-------------------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Karma_point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Karma_point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point"/>
        <rdfs:comment>Karma points: Karma points are a unique system that rarely appear in classic games. The sole purpose of karma is to give points away. That is, players gain no benefit from keeping their karma points, only from sharing them. Often, karma points are given as part of a regular grind, or check in behavior, for example: earn 3 karma points for every monthly check in. The main purpose of karma points in your design is to create a behavioral path for altruism and user reward.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Key_performance_indicator -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Key_performance_indicator">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
        <rdfs:comment xml:lang="en">-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Killer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Killer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Killers get their kicks from imposing themselves on others. This may be &quot;nice&quot;, ie. busybody do-gooding, but few people practice such an approach because the rewards (a warm, cosy inner glow, apparently) aren&apos;t very substantial.
--------------------
Reference: [225]
Title: HEARTS, CLUBS, DIAMONDS, SPADES: PLAYERS WHO SUIT MUDS
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Leaderboard -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Leaderboard">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Leaderboards rank players according to their relative success, measuring them against a certain success criterion. As such, leaderboards can help determine who performs best in a certain activity , and are thus competitive indicators of progress that relate the player’s own performance to the performance of others. 

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Levels that define steps in players’ progression.

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Local_regulation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Local_regulation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: On the Legal Implications of Using Gamifed Elements
Author: Kai Erenli</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Long_term_goal -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Long_term_goal">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_goal"/>
        <rdfs:comment xml:lang="en">Reference: [73]
Title: A literature review of gamification design frameworks
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Long_term_result_evaluation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Long_term_result_evaluation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_result_evaluation"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Manager -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Manager">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consumer"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User"/>
        <rdfs:comment xml:lang="en">Reference: [10]
Title: Is Enterprise Gamification Being Cannibalized by Its Own
Brand?
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Manipulation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Manipulation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ehical_issue_in_gamification_world"/>
        <rdfs:comment xml:lang="en">Manipulation: Gamification is a technique to change players’ behavior. So it is prima facie open to the charge of manipulation

------------------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Master -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Master">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile"/>
        <rdfs:comment xml:lang="en">Master: The length and difficulty at this stage is higher than in the previous stages. Users that become masters will want to apply and perfect their knowledge, so they will need goals that demand from them to come up with strategies and tactics according to what they have already learned; these may be individual or team goals
----------------
Reference: [51]
Title: Design and evaluation of a gamified system for ERP training
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Meaningful_story -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Meaningful_story">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Meaningful stories are game design elements that do not relate to the player’s performance. The narrative context in which a gamified application can be embedded contextualizes activities and characters in the game, and gives them meaning beyond the mere quest for points and achievements.

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mechanics_dynamics_aesthetics_theory_MDA -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mechanics_dynamics_aesthetics_theory_MDA">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">MDAT proposed by Hunicke describes how game design elements induce particular reactions from players. MDAT consists of three components: game mechanics, game dynamics, and game aesthetics. Game mechanics refer to the tools, techniques, and widgets that are the building blocks of a game. Game dynamics refer to the run-time behavior of a game and its interaction with players. Game aesthetics refer to players’ emotional responses when they interact with a game
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Narrative -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Narrative">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Narrative: the storyline of the game

Refrence: [223]
Title:  &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Nationwide_regulation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Nationwide_regulation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
        <rdfs:comment xml:lang="en">Reference: [103]
Title: On the Legal Implications of Using Gamifed Elements
Author: Kai Erenli</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Neuroticism -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Neuroticism">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
        <rdfs:comment xml:lang="en">Neuroticism: be fearful, sad, embarrassed, distrustful, and have difficulty managing stress
--------------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#No_disincentive_leaderboard -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#No_disincentive_leaderboard">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Leaderboard"/>
        <rdfs:comment>The no-disincentive leaderboard:   In the era of Facebook and the social graph, leaderboards are mostly tools for creating social incentive, rather than disincentive. They accomplish this simply by taking the player and putting him right in the middle. It doesn’t matter where he falls in ranking order—whether he is #81 or #200,000 the player will see himself right in the middle of the leaderboard. Below him, he will see friends who are on his tail, and above him he will see exactly how close he is to the next best score. And he will know exactly what he has to do to beat it.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Observer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Observer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Observers: These are outside individuals who are passively involved and absorbed in the experience. They have no direct impact on the gamified experience and are merely able to watch it from the outside. However, the presence–—and quantity–—of observers will impact the popularity of the experience. Furthermore, observers are potential players or spectators, as they can assume new roles by seeking out ways to become more active or immersed in the experience. In a non-game setting, an observer could include employees in other departments or offices in the firm.

-----------------
Reference: [90]
Title: Is it all a game? Understanding the principles of gamification
Authors: <AUTHORS>
Leyland Pitt</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Obtain_desired_behavior -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Obtain_desired_behavior">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_objective"/>
        <rdfs:comment xml:lang="en">Reference: [65]
Title: Gamification of Service Desk Work 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Off_task_behavior -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Off_task_behavior">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">If the gamification system is untied to the educational outcomes, the game features can be a distraction to the user. In this case, even if the user likes to use the system, he will not learn more from it

---------------------
Reference: [168]
Title: The Bright and Dark Sides of Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Onboarding -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Onboarding">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Onboarding is the act of bringing a novice into your system. Itis a carefully calculated way of thinking about how someone goes from zero to five miles per hour without crashing his car.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Openness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Openness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
        <rdfs:comment xml:lang="en">Imagination/Openness: devise novel ideas, hold unconventional values, and willingly question authority
--------------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
        <rdfs:comment xml:lang="en">Reference: [9]
Title: A theory of work gamification: Something old, something new, something borrowed, something cool?
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
        <rdfs:comment xml:lang="en">In an organization, Gamification can be implemented at various levels with respect to integration of game mechanics with the underlying activities being performed.

Reference: [210]
Title: A Method to Engage Employees using Gamification in BPO Industry
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
        <rdfs:comment xml:lang="en">Business objectives (or goals) set by an organization

---------------
Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_structure -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_structure">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
        <rdfs:comment xml:lang="en">Reference:[258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
----------------------
REference: [17]
Title:Using gamification to transform the adoption of servitization
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_concepts"/>
        <rdfs:comment xml:lang="en">Traditional change management studies suggest that in order to get results from
initiatives,  related organizational elements must change.

Reference:[258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Performance_graph -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Performance_graph">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Graph"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_graph"/>
        <rdfs:comment>Performance graphs, which are often used in simulation or strategy games, provide information about the players’ performance compared to their preceding performance during a game. Thus, in contrast to leaderboards, performance graphs do not compare the player’s performance to other players, but instead evaluate the player’s own performance over time.

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Pervasive_game -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Pervasive_game">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Philanthropist -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Philanthropist">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Philanthropists are motivated by purpose. They are altruistic and willing to give without expecting a reward.
---------------------
Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Physical_harm -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Physical_harm">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Harm"/>
        <owl:disjointWith rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_harm"/>
        <rdfs:comment xml:lang="en">Physical harms: In both these examples, the gameful systems intentionally attempt to motivate players to harm others and themselves in ways subject to moral and social condemnation

------------------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Planned_behaviour_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Planned_behaviour_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">It is a well-known motivational theory, which considers intrinsic as well as extrinsic motivational aspects? Vassileva (2012) relates to SDT and TPB as “super-theories” that consider intrinsic as well as extrinsic motivation.
TPB indicates a process by which individuals form specific intentions to carry out a behaviour consistent with their self-determined motives. However, TPB has some main limitations in contrast to SDT, for example the fact that there is still much unexplained variance in the used variables or the lack of attention regarding the origins or drivers of the belief-based antecedents of behavioural intentions.

Reference: [59]
Title: How to gamify information systems - adapting gamification to individual preferences
Authors: <AUTHORS>
----------------------------
Reference: [258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Player -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Player">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Players are motivated by extrinsic rewards. They will do whatever to earn a reward within a system, independently of the type of the activity
---------------------
Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Points, which are used to reward the users through different dimensions of the system.

Refrence: [198]
Titile: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point_distribution -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point_distribution">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#General_metric"/>
        <rdfs:comment xml:lang="en">Point Distributions: Inspecting the distribution of points over users can help experts to detect flaws in the balance of point amounts for gamified actions

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element"/>
        <rdfs:comment xml:lang="en">Reference:[258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
-------------------
Reference:[66]
Title: Are we playing yet? a review of gamified enterprise systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Progression -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Progression">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Progression, measuring players’ development

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_harm -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_harm">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Harm"/>
        <rdfs:comment xml:lang="en">Psychological harms: Many gamification systems involve competition and ranking. For example, digital leaderboards showing the relative performances of players are a popular game element to adapt to the workplace. If contextually taken as a stick rather than a carrot, such features can sometimes produce ‘‘expressive harms

------------------------------
Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_concepts"/>
        <rdfs:comment>-----------------
Reference:[18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
---------------------------
Reference: [43]
Title: The Gamification User Types Hexad Scale 
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_outcome -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_outcome">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_outcome"/>
        <rdfs:comment xml:lang="en">Examples: Enjoyment, engagement, attitude towards badges, Attitude towards gamification ,Fun, Satisfaction, Intrinsic vs. extrinsic motivation to complete tasks, Perceived added value of gamification, Enjoyment of gamified activity, Social motivation, Difference in users’ approach to virtual patient interaction, Social comparison, clear goals ,Network effects, social influence, recognition, reciprocal benefits, attitude towards the service, Motivation, degree of happiness, flow, task involvement

-------------
Reference: [110]
Title: Does Gamification Work? — A Literature Review of Empirical Studies on
Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Quality -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Quality">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:comment>The list of gamification rules is available at : Available at: 

 https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Quest -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Quest">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Quests give players direction for what to do within the world of the gamified experience.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Redeemable_point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Redeemable_point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point"/>
        <rdfs:comment>Redeemable points:Unlike XP, RP can fluctuate. The expectation for most people is that these points are usable within the system in exchange for things. They are earned and cashed, similar to the frequent flyer miles we redeem for awards. The term for this loop in social games and loyalty programs is “earn and burn,” which clearly indicates the purpose of an RP system.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Relationships -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Relationships">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Relationships : players’ social interactions.

Refrence: [223]
Title:  &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Reputation_point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Reputation_point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point"/>
        <rdfs:comment>Reputation points: Finally, reputation points make up the most complex point system. Any time a system requires trust between two or more parties that you can’t explicitly guarantee or manage, a reputation system is key. Its purpose is to act as a proxy for trust

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Researcher -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Researcher">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_provider"/>
        <rdfs:comment xml:lang="en">Reference: [47]
Title: More than just a game: ethical issues in gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Resource_acquisition -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Resource_acquisition">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Resource acquisition: allowing players to gather useful or collectible items.

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Respect -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Respect">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:comment>The list of gamification rules is available at : Available at: 

 https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Reward -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Reward">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Rewards are given to human beings after performing an action or showing some behavior in order to motivate them to repeat it.

Refrence: [198]
Titile: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_psychological_theory"/>
        <rdfs:comment xml:lang="en">A widely studied macro-theory of human motivation, which suggests that people are more likely to become engaged in an activity when they feel hedonic value while performing it

Reference: [28]
Title: How gamification of an enterprise collaboration system increases knowledge contribution: an affordance approach
Authors: <AUTHORS>
------------------------------
Is a macro theory of human motivation concerning people&apos;s inherent growth tendencies and their innate psychological needs

Reference: [128]
Title: Analysis and application of gamification
Authors: <AUTHORS>
------------------------------
Reference: [258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_expression -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_expression">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Self-expression makes it possible for people to show that they are unique and distinguishable from others.

Refrence: [198]
Titile: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sensation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sensation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic"/>
        <rdfs:comment>Rerence: [217]
Title:  MDA: A formal approach to game design and game research
Author: Hunicke, R., LeBlanc, M., &amp; Zubek, R</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Serious_game -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Serious_game">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
        <rdfs:comment xml:lang="en">Whereas serious games are fully-developed games serving a specific, non-entertainment purpose gamification refers to the use of distinct game building blocks embedded in real-world contexts. 

Reference: [18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
---------------------
These are real games (e.g. management games) with a serious goal and not just entertainment. Serious Games are often used to create awareness or teach about some subject

Reference: [34]
Title: Using Game Psychology in Information System Design for Sustainable Behavior Changes
Authors: <AUTHORS>
--------------------
A serious game usually looks like a typical video game, but the goal of the game is not to achieve fictional missions in a virtual world but to acquire practical skills in the real world or to increase the knowledge for preventing certain undesirable habits

Reference: [68]
Title: One Size Does Not Fit All: Applying the Right Game Concepts for the Right Persons to Encourage Non-game Activities
Authors: <AUTHORS>
Todorka Alexandrova, and Tatsuo Nakajima

--------------------
Any form of interactive computer-based game software for one or multiple players to be  used on any platform and that has been developed with the intention to be more than entertainment

Reference: [254]
Title: The game, the player, the world: Looking for a heart of gameness
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_goal -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_goal">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal"/>
        <rdfs:comment xml:lang="en">Reference: [73]
Title: A literature review of gamification design frameworks
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_result_evaluation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Short_term_result_evaluation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_after_gamification"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Simulation_game -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Simulation_game">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Game"/>
        <rdfs:comment xml:lang="en">Reference: [14]
Title: Why do people play games? A meta-analysis 
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Skill_point -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Skill_point">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point"/>
        <rdfs:comment>Skill points: Skill points are assigned to specific activities within the game and are tangential to both XP and RP. They are a bonus set of points that allow a player to gain experience/reward for activities alongside the core.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Smart_city -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Smart_city">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_engagement_loop -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_engagement_loop">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Social engagement loops, while not exclusive to games, borrow heavily from a viral loop design. A designer must not only see the way a player engages with the system, but also how he leaves it and—perhaps even more importantly—what brings him back again.

Reference: [219]
Title: Gamification by design: Implementing game mechanics in web and mobile apps.
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_graph -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_graph">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Graph"/>
        <rdfs:comment>Social graphs that represent players’ network of contacts within the game.

Refrence: [223]
Title: &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_media -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_media">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_relatedness_need -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_relatedness_need">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Psychological_need"/>
        <rdfs:comment xml:lang="en">The need for social relatedness refers to one’s feelings of belonging, attachment, and care in relation toa group of significant others. It represents the basic desire of the individual for coherent integration with the social environment 
-----------------
Reference:[18]
Title: How gamification motivates: An experimental study of the effects of specific game design elements on psychological need satisfaction
Authors: <AUTHORS>
------------------------------
Reference: [102]
Title: The Effects of Game Dynamics on User Engagement in Gamified Systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Socializer -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Socializer">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Socializers are motivated by relatedness. They want to interact with others and create social connections

Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke
--------------
Socializers are interested in people, and what they have to say. The game is merely a backdrop, a common ground where things happen to players. Interplayer relationships are important: empathizing with people, sympathizing, joking, entertaining and listening

Reference: [225]
Title: HEARTS, CLUBS, DIAMONDS, SPADES: PLAYERS WHO SUIT MUDS
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Spectator -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Spectator">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type"/>
        <rdfs:comment xml:lang="en">Spectators are those individuals who do not directly compete in the gamified experience but whose presence will influence how the gamified experience works. Spectators are part of the gamified environment (e.g., audience members) and are therefore highly immersed in the experience. While taking a mostly passive role, they indirectly impact the experience by contributing to the atmosphere. In a non-game setting, for example, a spectator could include a supervisor who contributes to the atmosphere by serving as a visible authority or a source of support.
----------------
Reference: [90]
Title: Is it all a game? Understanding the principles of gamification
Authors: <AUTHORS>
Leyland Pitt</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Status -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Status">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
        <rdfs:comment>Status:  attention, recognition etc. are inherently needed by most humans. It is crucial for these latter to engage in some activities in order to gain the desired prestige and respect of other humans.

Refrence: [198]
Titile: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Submission -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Submission">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic"/>
        <rdfs:comment>Rerence: [217]
Title:  MDA: A formal approach to game design and game research
Author: Hunicke, R., LeBlanc, M., &amp; Zubek, R</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Superficial_level -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Superficial_level">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
        <rdfs:comment xml:lang="en">At superficial level, the game mechanics are used independent of activity of being performed, for example, 10 points for every activity undertaken. 
-------------------------------
Reference: [210]
Title: A Method to Engage Employees using Gamification in BPO Industry
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Supporting_and_management_process -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Supporting_and_management_process">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process"/>
        <rdfs:comment xml:lang="en">Reference:[66]
Title: Are we playing yet? a review of gamified enterprise systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element"/>
        <rdfs:comment xml:lang="en">Reference: [16]
Title: Usage of Gamifcation in Enterrise: A Review
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task_quality -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task_quality">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">Quality of tasks may suffer if gamified elements distract from the main purpose of activities

-------------------
Reference: [114]
Title: Gamifying Information Systems – A Synthesis of Gamification Mechanics and Dynamics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Team -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Team">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Teams: groups of cooperating players,

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Temporal_statistic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Temporal_statistic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Detailed_metric"/>
        <rdfs:comment xml:lang="en">Temporal Statistics: Experts can analyze how long users need for the completion of particular gamification elements. Relevant measures in this aspect are: Time to Completion, the time period between the start of user existence and gamification element completion; Time to Assignment, the time period between the start of user existence and its assignment to the gamification element; Time Active, the time period between assignment and completion of the gamification element

-------------------
Reference: [199]
Title: Gamification Analytics—Methods and Tools for Monitoring and Adapting
Gamification Designs
Author: Benjamin Heilbrunn, Philipp Herzig, Alexander Schill</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Terms_of_Use -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Terms_of_Use">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation"/>
        <rdfs:comment xml:lang="en">•The Terms of Use serve as a powerful tool. For a provider, they form a “line of defence” which ensures that the risk of getting sued is minimized. Moreover, they can shift the balance of the contract in favour of the provider.
•An expert or counsel should be approached before launching the project. Most often a provider can (or at least should) get this service free of charge from a local Economic Chamber of Trade, Commerce and/or Industry. The information given by such institutions can then be used to design the project within the legal boundaries. From the consumer’s perspective, the corresponding information can be obtained from consumer protection authorities. They will check Terms of Use for illegal or misleading content and are able to take action if they think a certain line has been crossed or enough consumers are affected. As stated, however, Terms of Use are not evil by nature; there are some good examples out there that may serve as a role model

------------------
Reference: [103]
Title: On the Legal Implications of Using Gamifed Elements
Author: Kai Erenli</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Transaction -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Transaction">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Transactions: allowing item trading between players.

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Transparency -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Transparency">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        <rdfs:comment>The list of gamification rules is available at : Available at: 

 https://ethics.gamified.uk/</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Turn -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Turn">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Turns: sequential participation by players,

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Two-factor_theory -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Two-factor_theory">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory"/>
        <rdfs:comment xml:lang="en">It concludes that certain factors in the workplace result in job satisfaction, but if absent, they don’t lead to dissatisfaction but no satisfaction

Reference: [77]
Title: How Gamification Helps Managers
Authors: <AUTHORS>
--------------------------------
Reference: [258]
Title: Gamification as a Disruptive Factor in Software Process Improvement Initiatives
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Undesired_competition -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Undesired_competition">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">Leader boards are a common resource to promote competition, and sense of competence. Still, it can be harmful for students with low performance and low self-efficacy, since they can feel forced in a competition with their peers, which can negatively affect their sense of competence and result in the reduction of their interest and engagement.

---------------------
Reference: [168]
Title: The Bright and Dark Sides of Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consumer"/>
        <rdfs:comment>Reference: [66]
Title:Are we playing yet? A review of gamified enterprise systems
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_change_resistance -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_change_resistance">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome"/>
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_risk"/>
        <rdfs:comment xml:lang="en">Reference: [59]
Title: How to gamify information systems - adapting gamification to individual preferences
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
        <rdfs:comment>Reference: [3]
Title:Using gamification to enhance user motivation: The influence of gender and age
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_culture -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_culture">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:comment xml:lang="en">Culture is a combination of shared habits and meanings, interpreted within a particular context. The context and environment shape different cultures.
---------------
Reference: [199]
Title: Gamification and Behaviour
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_engagement -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_engagement">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome"/>
        <rdfs:comment>User engagement is emotional commitment to the organization and its goals

Reference: [16]
Title: Usage of gamification in enterprise: A review
Authors: <AUTHORS>
--------------------------------------
User engagement entails ‘harnessing’ employees to their jobs through their involvement, satisfaction, and enthusiasm for work
Reference: [60]
Title: Game on: Engaging customers and employees through gamification
Authors: <AUTHORS>
--------------------------------------
User Engagement is the positive feeling that employees have towards their jobs and also the motivation and effort they put into it.
Reference: [255]
Title: The Meaning of Employee Engagement, Industrial and Organizational Psychology
Authors: <AUTHORS>
--------------------------------------</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_experience -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_experience">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:comment>Reference: [71]
Title: Everybody is playing the game, but nobody&apos;s rules are the same”: towards adaptation of gamification based on users’ characteristics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_motivation -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_motivation">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome"/>
        <rdfs:comment xml:lang="en">Motivation: the behaviour which causes a person to want to repeat an action and vice-versa

Reference: [73]
Title: A Literature Review of Gamification Design Frameworks
Authors: <AUTHORS>
-------------------------------------------------
Motivation is defined as the process that initiates, guides, and maintains goal-oriented behaviours. It involves the biological, emotional, social, and cognitive forces that activate behaviour

Reference: [198]
Title: Introduction to Gamification: Foundation and Underlying Theories
Authors: <AUTHORS>
-------------------------------------------------
Motivation is the desire to do something and it could be explained in two ways: intrinsic and extrinsic

Reference: [199]
Title:  Gamification Analytics—Methods and Tools for Monitoring and Adapting Gamification Designs
Authors: <AUTHORS>
-------------------------------------------------</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_need -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_need">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_objective -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_objective">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_performance -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_performance">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Behavioural_outcome"/>
        <rdfs:comment>Gamification has been considered as a useful tool to enhance performance.

Reference: [6]
Title: Gamification Solutions for Software Acceptance: A Comparative Study of Requirements Engineering and Organizational Behavior Techniques
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:comment xml:lang="en">The Big-Five factors, a descriptive model of personality, has been used extensively in previous psychology and HCI research. The Big-Five factors are: Conscientiousness, Agreeableness, Neuroticism, Extraversion, and Imagination/Openness. The model does not narrow down the personality differences to a simple set of five traits. Instead, each Big Five factor represents a collection of personality traits.
-------------
Reference: [50]
Title: Personality,targeted/Gamification:A Survey Study on  Personality Traits and Motivational Affordances
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_preference -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_preference">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_privacy -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_privacy">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethical_issue_in_real_world"/>
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
        <rdfs:comment xml:lang="en">Monitoring and surveillance based on data collected on both the activity performed and the employee performing the activity are likely to breech privacy rights.

Reference: [114]
Title: Gamifying Information Systems – A Synthesis of Gamification Mechanics and Dynamics
Authors: <AUTHORS>
--------------------
Privacy is a key concern when it comes to gamification due to the high levels of transparency. In the most sophisticated scenario, each user action in the system is tracked and can be potentially reconstructed. Although this might be of interest to the top-management, in many countries the collection of such fine-grained employee data is forbidden. For example, in German enterprises, work councils are allowed to reject the introduction of systems that elevate too many employee data. Thus, within the platform a security mechanism has to exist that allows access to fine-grained employee data to its creator only. In addition, others may only access anonymised or aggregated data.

Reference: [132]
Title: A Generic Platform for Enterprise Gamification
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_profile">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [51]
Title: Design and evaluation of a gamified system for ERP training
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_skill -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_skill">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_characteristic"/>
        <rdfs:comment xml:lang="en">Reference: [71]
Title: Everybody is playing the game, but nobody&apos;s rules are the same”: towards adaptation of gamification based on users’ characteristics
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_type">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_concepts"/>
        <rdfs:comment xml:lang="en">Reference: [43]
Title: The Gamification User Types Hexad Scale
Authors: <AUTHORS>
Andrzej Marczewski, Lennart E. Nacke</rdfs:comment>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Viability -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Viability">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Evaluation_before_gamification"/>
        <rdfs:comment xml:lang="en">Viability: a previous study, evaluation and analysis of the potential of applying gamification or refuse it

--------------------------
Reference: [73]
Title: A literature review of gamification design frameworks
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Virtual_good -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Virtual_good">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        <rdfs:comment>Virtual goods, which have perceived value.

Refrence: [223]
Title : &quot;For the Win: How Game Thinking Can Revolutionize Your Business&quot;
Authors: <AUTHORS>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_domain"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_banking -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_banking">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_commerce -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_commerce">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_learning -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_learning">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
    </owl:Class>
    


    <!-- http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_overnment_service -->

    <owl:Class rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#e_overnment_service">
        <rdfs:subClassOf rdf:resource="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
    </owl:Class>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // General axioms
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achievable_gamification_element"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_cost"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_feedback_rate"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Key_performance_indicator"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Point_distribution"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Achiever"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Disruptor"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Explorer"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Free_spirit"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Killer"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Observer"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Philanthropist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Player"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Socializer"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Spectator"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Actor"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_goal"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_structure"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Process"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Addiction"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cheating"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Declining_effect"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Off_task_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Task_quality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Undesired_competition"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_change_resistance"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_privacy"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Aesthetic_experience_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Flow_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Fogg_behaviour_model"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Goal_setting_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mechanics_dynamics_aesthetics_theory_MDA"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Planned_behaviour_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_determination_theory"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Agreeableness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Conscientiousness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Extraversion"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Neuroticism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Openness"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Apprentice"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Beginner"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Expert"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Master"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Information_system"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Smart_city"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Information_system"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Smart_city"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Information_system"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Production"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Online_survey"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Architecture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Augmented_reality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mooc"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Autonomy_need"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Competence_need"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_relatedness_need"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Banking"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Business"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Commerce"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Crowdsourcing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_loyalty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Customer_support"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Data_collection"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Defense_industry"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Education"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Enterprise_resource_planning"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Entertainment_media"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Environmental_behavior"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Government_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Health"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Human_resource_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Innovation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Intraorganizational_communication_activity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Knowledge_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Logistics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Marketing"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Mobile_tablet_software"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Project_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Scientific_research"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Self_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Social_network"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Software_engineering"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sport"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Staff_management"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Sustainable_consumption"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Tourism"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Training"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Urban_mobility"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Web_based_service"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Wellness"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Work"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Basic_psychological_needs_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Causality_orientation_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Cognitive_evaluation_theory"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Two-factor_theory"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Common_user_characteristic"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_element_state_user_distribution"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Temporal_statistic"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Copyright"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_code_of_ethics"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Terms_of_Use"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Deepest_level"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrated_level"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Superficial_level"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_Risk_concepts"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_consultant"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_designer"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Ethicist"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_core_concepts"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_ethical_issue"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_regulation_mean"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_rule"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Experience_point"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Karma_point"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Redeemable_point"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Reputation_point"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Skill_point"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Exploitation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Harm"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_privacy"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#External_regulation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Identification"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integration"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Introjection"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_aesthetic"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_dynamic"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Gamification_mechanic"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Global_regulation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Local_regulation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Nationwide_regulation"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Honesty"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Integrity"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Quality"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Respect"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Transparency"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organization_gamification_applying_level"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#Organizational_element"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_change_resistance"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_engagement"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_motivation"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_performance"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_culture"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_experience"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_need"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_objective"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_personality_type"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_preference"/>
            <rdf:Description rdf:about="http://www.semanticweb.org/admin/ontologies/2017/10/untitled-ontology-37#User_skill"/>
        </owl:members>
    </rdf:Description>
</rdf:RDF>



<!-- Generated by the OWL API (version 4.2.8.20170104-2310) https://github.com/owlcs/owlapi -->

