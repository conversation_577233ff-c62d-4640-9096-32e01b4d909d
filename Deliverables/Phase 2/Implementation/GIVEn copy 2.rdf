<?xml version="1.0"?>
<rdf:RDF xmlns="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#"
     xml:base="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:cgo="http://autosemanticgame.eu/ontologies/cgo#"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:foaf="http://xmlns.com/foaf/0.1/"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     xmlns:vann="http://purl.org/vocab/vann/"
     xmlns:terms="http://purl.org/dc/terms/">
    <owl:Ontology rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf">
        <owl:imports rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <owl:imports rdf:resource="http://purl.org/net/VideoGameOntology"/>
        <owl:imports rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Previous%20Work/MAMbO5.owl"/>
        <dc:creator>Owen Sacco</dc:creator>
        <dc:creator rdf:datatype="http://www.w3.org/2001/XMLSchema#anyURI">http://delicias.dia.fi.upm.es/members/DGarijo/#me</dc:creator>
        <dc:creator rdf:datatype="http://www.w3.org/2001/XMLSchema#anyURI">http://filip.milstan.net/</dc:creator>
        <dc:creator rdf:datatype="http://www.w3.org/2001/XMLSchema#anyURI">http://purl.org/net/mpoveda</dc:creator>
        <dc:creator rdf:datatype="http://www.w3.org/2001/XMLSchema#anyURI">http://www.mendeley.com/profiles/janne-parkkila/</dc:creator>
        <dc:date>2016-12-01</dc:date>
        <dc:description rdf:resource="http://autosemanticgame.eu/ontologies/cgo/diagram/cgo.svg"/>
        <dc:description xml:lang="en">The Core Game Ontology (CGO) provides a light-weight vocabulary for describing video games. Video games can be seen as multi-faceted containging the following facets: visuals, audio, narrative, gameplay, game design and level design. Each facet can be regarded as an independent model and when each model are combined together, they form a game.

Visuals contain any visual output of a game, which range from photorealistic, to caricaturised, to abstract visuals. Audio includes background music such as a fully orchestrated soundtrack, sound effects, rewarding spimds and voice-acted dialogue. Narrative contains the interactive story of a game which makes up the game&apos;s plot. Game design contains all the game&apos;s mechanics that define the game&apos;s rules, which provide the structures and frames for play (for example winning and losing conditions) and actions available to the player. Level design includes the architecure of the spatial navigation of levels which determine how the player agent can progress from one point in the game to another. Gameplay consists of the players strategies whilst playing a game.</dc:description>
        <dc:description xml:lang="en">The Video Game Ontology is an ontology designed for describing video games and all the resources related to their game plays.</dc:description>
        <dc:description>This is an ontology that combines video game-related concepts with concepts related to organisational modelling of multi-agent systems as intelligent virtual environments.</dc:description>
        <dc:rights xml:lang="en">This ontology is distributed under a Creative Commons Attribution License - http://creativecommons.org/licenses/by/4.0</dc:rights>
        <dc:title>Gamified Intelligent Virtual Environment Ontology (GIVEn)</dc:title>
        <dc:title xml:lang="en">The Video Game Ontology</dc:title>
        <terms:created rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2013-10-22</terms:created>
        <terms:created rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2016-12-01</terms:created>
        <terms:description>An ontology for describing video game information</terms:description>
        <terms:description>This is an ontology that combines video game-related concepts with concepts related to organisational modelling of multi-agent systems as intelligent virtual environments.</terms:description>
        <terms:license>http://creativecommons.org/licenses/by-nc-sa/2.0/</terms:license>
        <terms:modified rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2014-12-19</terms:modified>
        <terms:modified rdf:datatype="http://www.w3.org/2001/XMLSchema#date">2016-12-23</terms:modified>
        <terms:partOf rdf:resource="http://autosemanticgame.eu"/>
        <terms:partOf>This study is supported by the project MOBODL-2023-08-5618 funded by the European Union and the Croatian Science Foundation.</terms:partOf>
        <terms:status rdf:resource="http://purl.org/adms/status/UnderDevelopment"/>
        <terms:title>Gamified Intelligent Virtual Environment Ontology (GIVEn)</terms:title>
        <terms:title>The Core Game Ontology (CGO)</terms:title>
        <terms:type rdf:resource="http://purl.org/adms/assettype/Ontology"/>
        <vann:preferredNamespacePrefix>cgo</vann:preferredNamespacePrefix>
        <vann:preferredNamespacePrefix>vgo</vann:preferredNamespacePrefix>
        <vann:preferredNamespaceUri>http://autosemanticgame.eu/ontologies/cgo#</vann:preferredNamespaceUri>
        <vann:preferredNamespaceUri>http://purl.org/net/VideoGameOntology#</vann:preferredNamespaceUri>
        <rdfs:comment xml:lang="en">An ontology for describing video games and game plays. Created by Janne Parkkila, Filip Radulovic, Daniel Garijo and María Poveda.</rdfs:comment>
        <rdfs:comment xml:lang="en">The Core Game Ontology (CGO) is an ontology for describing information about video games.</rdfs:comment>
        <rdfs:comment>This is an ontology that combines video game-related concepts with concepts related to organisational modelling of multi-agent systems as intelligent virtual environments.</rdfs:comment>
        <rdfs:label>CGO</rdfs:label>
        <owl:versionInfo>1.0</owl:versionInfo>
        <owl:versionInfo>1.0.0</owl:versionInfo>
        <foaf:homepage rdf:resource="http://autosemanticgame.eu/ontologies/cgo"/>
        <foaf:homepage>https://github.com/AILab-FOI/MAGO</foaf:homepage>
        <foaf:name>Gamified Intelligent Virtual Environment Ontology (GIVEn)</foaf:name>
    </owl:Ontology>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Annotation properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://purl.org/dc/elements/1.1/contributor -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/contributor"/>
    


    <!-- http://purl.org/dc/elements/1.1/creator -->

    <owl:AnnotationProperty rdf:about="http://purl.org/dc/elements/1.1/creator"/>
    


    <!-- http://xmlns.com/foaf/0.1/homepage -->

    <owl:AnnotationProperty rdf:about="http://xmlns.com/foaf/0.1/homepage"/>
    


    <!-- http://xmlns.com/foaf/0.1/name -->

    <owl:AnnotationProperty rdf:about="http://xmlns.com/foaf/0.1/name"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Object Properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasAudio -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasAudio">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Audio"/>
        <rdfs:comment xml:lang="en">Specifies the music and sound of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has audio</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasGameDesign -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasGameDesign">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#GameDesign"/>
        <rdfs:comment xml:lang="en">Specifies the rules and mechanics of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has game design</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasGameplay -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasGameplay">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Gameplay"/>
        <rdfs:comment xml:lang="en">Specifies the gameplay of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has gameplay</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasLevelDesign -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasLevelDesign">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#LevelDesign"/>
        <rdfs:comment xml:lang="en">Specifies the levels of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has level design</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasNarrative -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasNarrative">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Narrative"/>
        <rdfs:comment xml:lang="en">Specifies the story and plot of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has narrative</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#hasVisuals -->

    <owl:ObjectProperty rdf:about="http://autosemanticgame.eu/ontologies/cgo#hasVisuals">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Game"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Visuals"/>
        <rdfs:comment xml:lang="en">Specifies the visuals of a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">has visuals</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#generates_Signal -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#generates_Signal">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Signal_generated_by"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Signal"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Acceleration -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Acceleration">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Action -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Action">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Action_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Action_Rule -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Action_Rule">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Agent -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Agent">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Agent_Action -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Agent_Action">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_Action_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Action"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Arguments -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Arguments">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Artifact -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Artifact">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Artifact_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Attribute -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Attribute">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Body_Artifact -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Body_Artifact">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Body_Artifact_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Component -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Component">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Component_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Do_Action -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Do_Action">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Artifact -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Artifact">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Artifact_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Law_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law_Cond_Type -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law_Cond_Type">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law_Type -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law_Type">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type"/>
        <rdfs:range>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#SimpleType"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:range>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Workspace -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Workspace">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Workspace_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Inh_Attribute -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Inh_Attribute">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Inhabitant_Agent -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Inhabitant_Agent">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Inhabitant_Agent_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Joint -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Joint">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Observable_Property -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Observable_Property">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Observable_Property_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Operation -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Operation">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Operation_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operation"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Physical_Event -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Physical_Event">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Event"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Physical_Property -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Physical_Property">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Physical_Property_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Plan -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Plan">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Plan_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Position -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Position">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_PreCondition -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_PreCondition">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Velocity -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Velocity">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Workspace -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Workspace">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Workspace_of"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Action_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Action_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_Action_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_Action_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Action"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Agent_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Artifact_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Artifact_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Body_Artifact_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Body_Artifact_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Component_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Component_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Artifact_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Artifact_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Law_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Law_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Workspace_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_IVE_Workspace_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Inhabitant_Agent_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Inhabitant_Agent_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Observable_Property_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Observable_Property_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Operation_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Operation_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operation"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Physical_Property_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Physical_Property_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Plan_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Plan_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Signal_generated_by -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Signal_generated_by">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Signal"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Workspace_of -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#is_Workspace_of">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#accepts -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#accepts">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAcceptedBy"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#NormativeSystem"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#achieves -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#achieves">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAchievedBy"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#definesRoles -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#definesRoles">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleIn"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasAccessTo -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasAccessTo">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAccessibleTo"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasChange -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasChange">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsChangeFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isCriteriaOfOrganizingFor"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:domain>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasCulture -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCulture">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsCultureFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasEnvironment -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasEnvironment">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsEnvironmentFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasIndividuals -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasIndividuals">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelIndividualsFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasProcesses -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasProcesses">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelProcessesFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isRelationOf"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasRole -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRole">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleOf"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AsymmetricProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasStrategy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasStrategy">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStrategyFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#hasStructure -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#hasStructure">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStructureFor"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isAcceptedBy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isAcceptedBy"/>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isAccessibleTo -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isAccessibleTo"/>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isAchievedBy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isAchievedBy"/>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isCriteriaOfOrganizingFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isCriteriaOfOrganizingFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
        <rdfs:range>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:range>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isPerformedBy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isPerformedBy">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#performs"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isRelationOf -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isRelationOf">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleIn -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleIn">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleOf -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleOf">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#isTriggeredBy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#isTriggeredBy">
        <owl:inverseOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelIndividualsFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelIndividualsFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalIndividuals"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelProcessesFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelProcessesFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalProcesses"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelsChangeFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsChangeFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalChange"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelsCultureFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsCultureFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalCulture"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelsEnvironmentFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsEnvironmentFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalEnvironment"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStrategyFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStrategyFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStrategy"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStructureFor -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStructureFor">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#performs -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#performs">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#triggers -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesAgents -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesAgents">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalIndividuals"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesChange -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesChange"/>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesCulture -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesCulture">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalCulture"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Culture"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesEnvironment -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesEnvironment">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalEnvironment"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesProcesses -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesProcesses">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalProcesses"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesStrategy -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesStrategy">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStrategy"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
    </owl:ObjectProperty>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#usesStructure -->

    <owl:ObjectProperty rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#usesStructure">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/dc/terms/creator -->

    <owl:ObjectProperty rdf:about="http://purl.org/dc/terms/creator">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://xmlns.com/foaf/0.1/Agent"/>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasAchievement -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasAchievement">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isAchievementInGame"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:hasAchievement property specifies that a game has a specific achievement. A game often contains more than one achievement that can be awarded to the players.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">has achievement</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasCharacter -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasCharacter">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isCharacterInGame"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:comment xml:lang="en">The vgo:hasCharacter property specifies that a game has a specific character. A game can have more than one characters involved.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">has character</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasFeature -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasFeature">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Item"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Feature"/>
        <rdfs:comment xml:lang="en">The vgo:hasFeature property indicates what is a vgo:Feature (or ability) of a particular vgo:Item. For example, a fire sword, a healing staff or boots of flight connects item to a feature it can have. An item with connection to potable feature would make the item potable.</rdfs:comment>
        <rdfs:label xml:lang="en">has feature</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasGameGenre -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasGameGenre">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Genre"/>
        <rdfs:comment xml:lang="en">The vgo:hasGameGenre property specifies that a game belongs to a certain game genre. For example, Pong would be an arcade game and Mario a platformer.</rdfs:comment>
        <rdfs:label xml:lang="en">has game genre</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasItem -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasItem">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isItemInGame"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Item"/>
        <rdfs:comment xml:lang="en">The vgo:hasItem property specifies that a game has a specific item. A game often contains more than one items.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">has item</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasLeaderboard -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasLeaderboard">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isLeaderboardInGame"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Leaderboard"/>
        <rdfs:comment xml:lang="en">The vgo:hasLeaderboard specifies that a leaderboard belongs to a particular game. A game can have one or more leaderboards that keep track of ranking of the players. For example a leaderboard could be ranking of who has the most soccer game victories or who has the fastest lap in a Formula 1 game.</rdfs:comment>
        <rdfs:label xml:lang="en">has leaderboard</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#hasPlayingArea -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#hasPlayingArea">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#PlayingArea"/>
        <rdfs:comment xml:lang="en">The vgo:hasPlayingArea property asserts a gaming area to a specific game. In every game, the gameplay takes place in some playing area.</rdfs:comment>
        <rdfs:label xml:lang="en">has playing area</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#involvesAchievement -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#involvesAchievement">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isAchievedInSession"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:involvesPlayer property specifies that a session involves a specific player. A session may involve more than one player.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">involves achievement</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#involvesCharacter -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#involvesCharacter">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isCharacterInSession"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:comment xml:lang="en">The vgo:involvesCharacter property specifies that a session involves a specific character.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">involves character</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#involvesPlayer -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#involvesPlayer">
        <owl:inverseOf rdf:resource="http://purl.org/net/VideoGameOntology#isPlayerInSession"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:comment xml:lang="en">The vgo:involvesPlayer property specifies that a session involves a specific player. A session may involve more than one player.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">involves player</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isAchievedInSession -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isAchievedInSession">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:comment xml:lang="en">The property vgo:isAchievedInSession asserts the receiving of an achievement to a certain gameplay session. This enables to keep track of what achievements a player has gained during one gameplay session.</rdfs:comment>
        <rdfs:label xml:lang="en">is achieved in session</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isAchievementInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isAchievementInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">The property vgo:isAchievementInGame asserts that a specific vgo:Achievement can be earned in a particular vgo:Game. An achievement must belong to a certain game.</rdfs:comment>
        <rdfs:label xml:lang="en">is achievement in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isCharacterInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isCharacterInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">vgo:isCharacterInGame property describes the relation between a vgo:Character and a vgo:Game. a vgo:Character always belongs to a certain game.</rdfs:comment>
        <rdfs:label xml:lang="en">is character in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isCharacterInSession -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isCharacterInSession">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:comment xml:lang="en">The vgo:isCharacterInSession property connects the vgo:Character to a vgo:Session. A character participates in a game session (e.g., a football match or a counter strike round) during a period of time. As players can have multiple characters, a character needs to be connected to the session, in order to know which of those characters participated in the certain session.</rdfs:comment>
        <rdfs:label xml:lang="en">is character in session</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isEventAssociatedToPlayer -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isEventAssociatedToPlayer">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:comment xml:lang="en">The vgo:isEventAssociatedToPlayer property asserts an event to a specific vgo:Player. A player may have caused an event to happen through the actions of his/her character and this property is used to connect the real person to the event.</rdfs:comment>
        <rdfs:label xml:lang="en">is event associated to player</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isEventInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isEventInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">The vgo:isEventInGame property asserts an event to a specific game. An event always happens inside a specific a game.</rdfs:comment>
        <rdfs:label xml:lang="en">is event in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isEventInSession -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isEventInSession">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:comment xml:lang="en">The vgo:isEventInSession property links an event to a specific gameplay session. An event always happens during a certain session and this property enables to link the events to that session. For example, an event of moving the queen in game of chess should be connected to a session of chess.</rdfs:comment>
        <rdfs:label xml:lang="en">is event in session</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isEventRelatedToItem -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isEventRelatedToItem">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Item"/>
        <rdfs:comment xml:lang="en">The vgo:isEventRelatedToItem property asserts an event to a specific item. This property is used to describe an event that includes an item in one way or another. For example, an event where character gains a new sword can be described with this relation.</rdfs:comment>
        <rdfs:label xml:lang="en">is event related to item</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isEventTriggeredByCharacter -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isEventTriggeredByCharacter">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:comment xml:lang="en">The vgo:isEventTriggeredByCharacter connects the vgo:InstantaneousEvent to specific vgo:Character. This describes that an event is often caused by a character. The character in question can be either a character controlled by a player or a computer. For example, both player and non-player characters can trigger a character death event.</rdfs:comment>
        <rdfs:label xml:lang="en">is event triggered by character</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isFriendWithPlayer -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isFriendWithPlayer">
        <rdfs:subPropertyOf rdf:resource="http://xmlns.com/foaf/0.1/knows"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#SymmetricProperty"/>
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:comment xml:lang="en">The vgo:isFriendWithPlayer describes a connection between players. The property is used to model the friends a player has and with whom he might be playing games with. The friends of a player are not bound necessarily to particular game, but can be describe the friendly playing relationship in overall.</rdfs:comment>
        <rdfs:label xml:lang="en">is friend with player</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isItemInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isItemInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Item"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">The vgo:isItemInGame is used to specify which item belongs to a particular game. An item cannot exist alone and thus should always be associated to a certain game.</rdfs:comment>
        <rdfs:label xml:lang="en">is item in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isLeaderboardInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isLeaderboardInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Leaderboard"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">The vgo:isLeaderboardInGame property specifies that a leaderboard is from a specific game.</rdfs:comment>
        <rdfs:comment xml:lang="en">optional</rdfs:comment>
        <rdfs:label xml:lang="en">is leaderboard in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isPlayerInSession -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isPlayerInSession">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:comment xml:lang="en">The vgo:isPlayerInSession property connects vgo:Player to a specific vgo:Session. This property is used to keep track of the gameplay sessions the player has played and what has happened in those sessions. For example vgo:Player may have participated in a this can be a one round of Counter-Strike or played one hour session of mario.</rdfs:comment>
        <rdfs:label xml:lang="en">is player in session</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#isSessionInGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#isSessionInGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">The vgo:isSessionInGame property links a vgo:Session to a certain vgo:Game. Each gameplay session must belong to a certain game.</rdfs:comment>
        <rdfs:label xml:lang="en">is session in game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#livesIn -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#livesIn">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://dbpedia.org/ontology/PopulatedPlace"/>
        <rdfs:comment xml:lang="en">The vgo:livesIn describes the connection between a player and his place of existence in the real-world. A vgo:Player is connected to wgs84:SpatialThing as that has descriptions for places where people live and contains more detailed information of them, such as continents and regions.</rdfs:comment>
        <rdfs:label xml:lang="en">lives in</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#ownsAchievement -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#ownsAchievement">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:ownsAchievement links the vgo:Player to the particular vgo:Achievement earned in a game.</rdfs:comment>
        <rdfs:label xml:lang="en">owns achievement</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#ownsCharacter -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#ownsCharacter">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:comment xml:lang="en">The vgo:ownsCharacter property asserts which characters are owned by a specific player. A player can have multiple characters in one game and this connection is used to define all the different characters a player could be playing. Even though a player deletes, trades or loses his/her character in any way, the connection can be kept to contain the player’s history of owned characters.</rdfs:comment>
        <rdfs:label xml:lang="en">owns character</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#ownsItem -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#ownsItem">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Character"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Item"/>
        <rdfs:comment xml:lang="en">The vgo:ownsItem describes ownership of an item. A vgo:Item is always owned by a certain vgo:Character. A vgo:Character can own multiple vgo:Items and this relationship is used to keep track of the character’s owned items. Even though players may lose items, the vgo:ownsItem connection is still held with the item. This approach allows to keep track of character’s history of owned items.</rdfs:comment>
        <rdfs:label xml:lang="en">owns item</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#playsGame -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#playsGame">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:comment xml:lang="en">the vgo:playsGame property asserts which games has the vgo:Player played at any point of time.</rdfs:comment>
        <rdfs:label xml:lang="en">plays game</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#purchasesGameOffering -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#purchasesGameOffering">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InAppPurchaseEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#GameProduct"/>
        <rdfs:comment xml:lang="en">The vgo:purchasesGameOffering property aseerts a vgo:InAppPurchaseEvent to a specific vgo:GameProduct. This property describes what is purchased by the in-app purchase event that the player has done.</rdfs:comment>
        <rdfs:label xml:lang="en">purchases game offering</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#unlocksAchievement -->

    <owl:ObjectProperty rdf:about="http://purl.org/net/VideoGameOntology#unlocksAchievement">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:unlocksAchievement property asserts an event to a certain achievement. An achievement is always unlocked as a consequence of some event. For example, eating the 100th piece of cake unlocks the “Cake Eater” achievement.</rdfs:comment>
        <rdfs:label xml:lang="en">unlocks achievement</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#EnvironmentIsUsedBy -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#EnvironmentIsUsedBy"/>
    


    <!-- http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#consistsOf -->

    <owl:ObjectProperty rdf:about="http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#consistsOf"/>
    


    <!-- http://xmlns.com/foaf/0.1/knows -->

    <owl:ObjectProperty rdf:about="http://xmlns.com/foaf/0.1/knows"/>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#hasActiveNorms -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#hasActiveNorms">
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#isActiveWithin"/>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#isActiveWithin -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#isActiveWithin">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_0658272e_b138_4c80_96d5_1666a2a8e370 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_0658272e_b138_4c80_96d5_1666a2a8e370">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_1dcf5fa9_2d15_4876_8912_5a3079ab8ea3"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">consists of objectives</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_0fb074df_59e7_46e3_873f_42ac624a80cc -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_0fb074df_59e7_46e3_873f_42ac624a80cc">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:label xml:lang="en-gb">provides achievement</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_1b06a07c_e2fe_4d5c_affb_36d050530a9c -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_1b06a07c_e2fe_4d5c_affb_36d050530a9c">
        <rdfs:domain rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Narrative"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
        <rdfs:label xml:lang="en-gb">depends on workspace</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_1c340508_ab6d_4a77_b307_913a61c0f36d -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_1c340508_ab6d_4a77_b307_913a61c0f36d">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_0c864038_db01_4f7d_87c0_650ad7f84af1"/>
        <rdfs:label xml:lang="en-gb">is described using</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_5230412c_73be_4291_839e_734a671a5b28 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_5230412c_73be_4291_839e_734a671a5b28">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_1dcf5fa9_2d15_4876_8912_5a3079ab8ea3"/>
        <rdfs:label xml:lang="en-gb">depends on</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_5daa59b7_9bbb_40b5_800a_2ab2f3537424 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_5daa59b7_9bbb_40b5_800a_2ab2f3537424">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_0c864038_db01_4f7d_87c0_650ad7f84af1"/>
        <rdfs:label xml:lang="en-gb">affects inventory</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_88097da3_4059_4b9c_8161_89d2417f8a5e -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLObjectProperty_88097da3_4059_4b9c_8161_89d2417f8a5e">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
        <rdfs:range rdf:resource="http://autosemanticgame.eu/ontologies/cgo#Narrative"/>
        <rdfs:label xml:lang="en-gb">affects narrative</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Data properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Code_File -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Code_File">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Angle -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Angle">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact_Code_File -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact_Code_File">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Condition -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Condition">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#File -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#File">
        <rdfs:domain rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Action -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Action">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Sentence -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Sentence">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:range>
            <rdfs:Datatype>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#boolean"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#double"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#float"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#int"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#string"/>
                </owl:unionOf>
            </rdfs:Datatype>
        </rdfs:range>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Linkeable -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Linkeable">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Manual -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Manual">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Mass -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Mass">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Name -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Name">
        <rdfs:domain rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operand_Type -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operand_Type">
        <rdfs:domain rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
        <rdfs:range>
            <rdfs:Datatype>
                <owl:oneOf>
                    <rdf:Description>
                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                        <rdf:first>ADD</rdf:first>
                        <rdf:rest>
                            <rdf:Description>
                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                <rdf:first>AND</rdf:first>
                                <rdf:rest>
                                    <rdf:Description>
                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                        <rdf:first>BOOLEAN_VAL</rdf:first>
                                        <rdf:rest>
                                            <rdf:Description>
                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                <rdf:first>DIVIDE</rdf:first>
                                                <rdf:rest>
                                                    <rdf:Description>
                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                        <rdf:first>DOUBLE_VAL</rdf:first>
                                                        <rdf:rest>
                                                            <rdf:Description>
                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                <rdf:first>ELEMENT_ATT</rdf:first>
                                                                <rdf:rest>
                                                                    <rdf:Description>
                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                        <rdf:first>ELEMENT_PROP</rdf:first>
                                                                        <rdf:rest>
                                                                            <rdf:Description>
                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                <rdf:first>EQUAL</rdf:first>
                                                                                <rdf:rest>
                                                                                    <rdf:Description>
                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                        <rdf:first>FLOAT_VAL</rdf:first>
                                                                                        <rdf:rest>
                                                                                            <rdf:Description>
                                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                <rdf:first>GREATERTHAN</rdf:first>
                                                                                                <rdf:rest>
                                                                                                    <rdf:Description>
                                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                        <rdf:first>INT_VAL</rdf:first>
                                                                                                        <rdf:rest>
                                                                                                            <rdf:Description>
                                                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                <rdf:first>LESSTHAN</rdf:first>
                                                                                                                <rdf:rest>
                                                                                                                    <rdf:Description>
                                                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                        <rdf:first>MOD</rdf:first>
                                                                                                                        <rdf:rest>
                                                                                                                            <rdf:Description>
                                                                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                <rdf:first>MULTIPLY</rdf:first>
                                                                                                                                <rdf:rest>
                                                                                                                                    <rdf:Description>
                                                                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                        <rdf:first>OR</rdf:first>
                                                                                                                                        <rdf:rest>
                                                                                                                                            <rdf:Description>
                                                                                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                                <rdf:first>PARAMETER</rdf:first>
                                                                                                                                                <rdf:rest>
                                                                                                                                                    <rdf:Description>
                                                                                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                                        <rdf:first>STRING_VAL</rdf:first>
                                                                                                                                                        <rdf:rest>
                                                                                                                                                            <rdf:Description>
                                                                                                                                                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                                                <rdf:first>SUBSTRACT</rdf:first>
                                                                                                                                                                <rdf:rest>
                                                                                                                                                                    <rdf:Description>
                                                                                                                                                                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                                                                                                                                                        <rdf:first>UNEQUAL</rdf:first>
                                                                                                                                                                        <rdf:rest rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#nil"/>
                                                                                                                                                                    </rdf:Description>
                                                                                                                                                                </rdf:rest>
                                                                                                                                                            </rdf:Description>
                                                                                                                                                        </rdf:rest>
                                                                                                                                                    </rdf:Description>
                                                                                                                                                </rdf:rest>
                                                                                                                                            </rdf:Description>
                                                                                                                                        </rdf:rest>
                                                                                                                                    </rdf:Description>
                                                                                                                                </rdf:rest>
                                                                                                                            </rdf:Description>
                                                                                                                        </rdf:rest>
                                                                                                                    </rdf:Description>
                                                                                                                </rdf:rest>
                                                                                                            </rdf:Description>
                                                                                                        </rdf:rest>
                                                                                                    </rdf:Description>
                                                                                                </rdf:rest>
                                                                                            </rdf:Description>
                                                                                        </rdf:rest>
                                                                                    </rdf:Description>
                                                                                </rdf:rest>
                                                                            </rdf:Description>
                                                                        </rdf:rest>
                                                                    </rdf:Description>
                                                                </rdf:rest>
                                                            </rdf:Description>
                                                        </rdf:rest>
                                                    </rdf:Description>
                                                </rdf:rest>
                                            </rdf:Description>
                                        </rdf:rest>
                                    </rdf:Description>
                                </rdf:rest>
                            </rdf:Description>
                        </rdf:rest>
                    </rdf:Description>
                </owl:oneOf>
            </rdfs:Datatype>
        </rdfs:range>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property_Type -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property_Type">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range>
            <rdfs:Datatype>
                <owl:oneOf>
                    <rdf:Description>
                        <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                        <rdf:first>Internal</rdf:first>
                        <rdf:rest>
                            <rdf:Description>
                                <rdf:type rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#List"/>
                                <rdf:first>Perceivable</rdf:first>
                                <rdf:rest rdf:resource="http://www.w3.org/1999/02/22-rdf-syntax-ns#nil"/>
                            </rdf:Description>
                        </rdf:rest>
                    </rdf:Description>
                </owl:oneOf>
            </rdfs:Datatype>
        </rdfs:range>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Shape -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Shape">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#X -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#X">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Y -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Y">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Z -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Z">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
    </owl:DatatypeProperty>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_SimpleValue -->

    <owl:DatatypeProperty rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_SimpleValue">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#SimpleType"/>
        <rdfs:range>
            <rdfs:Datatype>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#boolean"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#double"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#float"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#integer"/>
                    <rdf:Description rdf:about="http://www.w3.org/2001/XMLSchema#string"/>
                </owl:unionOf>
            </rdfs:Datatype>
        </rdfs:range>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#endTime -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#endTime">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#dateTime"/>
        <rdfs:comment xml:lang="en">endTime describes the ending moment in time of a single Session. endTime connects the session to a DateTime value which holds the moment when the session ended.</rdfs:comment>
        <rdfs:label xml:lang="en">end time</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#eventName -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#eventName">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <rdfs:comment xml:lang="en">name connects InstantaneousEvent a text string. This allows the event to have a name to recognize it for.</rdfs:comment>
        <rdfs:label xml:lang="en">event name</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#eventTime -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#eventTime">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#dateTime"/>
        <rdfs:comment xml:lang="en">time describes the connection of InstantaneousEvent happening at a certain moment of time. time Conncets the event to a DateTime value that describes the moment when the event happened.</rdfs:comment>
        <rdfs:label xml:lang="en">event time</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#releaseDate -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#releaseDate">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#dateTime"/>
        <rdfs:comment xml:lang="en">releaseDate connects a game to a time which describes the release date of the game.</rdfs:comment>
        <rdfs:label xml:lang="en">release date</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#startTime -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#startTime">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Session"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#dateTime"/>
        <rdfs:comment xml:lang="en">startTime describes the starting moment in time of a single Session. startTime connects the session to a DateTime value which holds the moment when the session started.</rdfs:comment>
        <rdfs:label xml:lang="en">start time</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- http://purl.org/net/VideoGameOntology#username -->

    <owl:DatatypeProperty rdf:about="http://purl.org/net/VideoGameOntology#username">
        <rdfs:domain rdf:resource="http://purl.org/net/VideoGameOntology#Player"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <rdfs:comment xml:lang="en">The username connects player to a text string which describes the username a player has.</rdfs:comment>
        <rdfs:label xml:lang="en">username</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Audio -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Audio">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:comment xml:lang="en">Audio includes background music such as a fully orchestrated soundtrack, sound effects, rewarding spimds and voice-acted dialogue.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Audio</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Game -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Game">
        <owl:equivalentClass rdf:resource="http://purl.org/net/VideoGameOntology#Game"/>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
        <rdfs:comment xml:lang="en">Specifies a video game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Game</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#GameDesign -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#GameDesign">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Norm"/>
        <rdfs:comment xml:lang="en">Game design contains all the game&apos;s mechanics that define the game&apos;s rules, which provide the structures and frames for play (for example winning and losing conditions) and actions available to the player.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Game Design</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Gameplay -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Gameplay">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
        <rdfs:comment xml:lang="en">Gameplay consists of the players strategies whilst playing a game.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Gameplay</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#LevelDesign -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#LevelDesign">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:comment xml:lang="en">Level design includes the architecure of the spatial navigation of levels which determine how the player agent can progress from one point in the game to another.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Level Design</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Narrative -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Narrative">
        <rdfs:comment xml:lang="en">Narrative contains the interactive story of a game which makes up the game&apos;s plot.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Narrative</rdfs:label>
    </owl:Class>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#Visuals -->

    <owl:Class rdf:about="http://autosemanticgame.eu/ontologies/cgo#Visuals">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
        <rdfs:comment xml:lang="en">Visuals contain any visual output of a game, which range from photorealistic, to caricaturised, to abstract visuals.</rdfs:comment>
        <rdfs:isDefinedBy rdf:resource="http://autosemanticgame.eu/ontologies/cgo#"/>
        <rdfs:label xml:lang="en">Visuals</rdfs:label>
    </owl:Class>
    


    <!-- http://dbpedia.org/ontology/PopulatedPlace -->

    <owl:Class rdf:about="http://dbpedia.org/ontology/PopulatedPlace"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action">
        <rdfs:subClassOf>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Action_Rule"/>
                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule"/>
            </owl:Restriction>
        </rdfs:subClassOf>
        <rdfs:subClassOf>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Physical_Event"/>
                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">0</owl:minQualifiedCardinality>
                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Event"/>
            </owl:Restriction>
        </rdfs:subClassOf>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action_Rule">
        <rdfs:subClassOf>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_Do_Action"/>
                <owl:minCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minCardinality>
            </owl:Restriction>
        </rdfs:subClassOf>
        <rdfs:subClassOf>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_PreCondition"/>
                <owl:minCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">0</owl:minCardinality>
            </owl:Restriction>
        </rdfs:subClassOf>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Action -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent_Action">
        <owl:equivalentClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Human_Immersed_Agent -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Human_Immersed_Agent">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE">
        <rdfs:comment>Intelligent Virtual Environment Definition</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Norm"/>
        <rdfs:subClassOf>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Action"/>
                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                <owl:onDataRange rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
            </owl:Restriction>
        </rdfs:subClassOf>
        <rdfs:comment xml:lang="en">A type of norm that is dependent on a specific Workspace, i.e. it is location-based.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Condition">
        <owl:equivalentClass rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law_Type"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Manual -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Manual"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operation -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Operation"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Artifact"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Event -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Event">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Property">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Signal -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Signal"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#SimpleType -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#SimpleType">
        <owl:equivalentClass rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Smart_Resource_Artifact -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Smart_Resource_Artifact">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact"/>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Vector3D"/>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Workspace">
        <rdfs:comment xml:lang="en">Everything that is being modelled at the moment. May contain Organizational Units (Individual and Grouped). Does not contain concepts of the system that are not being modelled at the moment.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#AcademicStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AcademicStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Akademska%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#AcquisitionStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AcquisitionStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Spajanja%20i%20preuzimanja for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Activity -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity">
        <owl:equivalentClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:subClassOf>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#achieves"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isPerformedBy"/>
                        <owl:qualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:qualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:comment>Any atomic activity performed by some individual agent
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Ad-hoc%20suprastrukture%20(ad-hoc-kracije) for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Agent -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent">
        <rdfs:subClassOf>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasAccessTo"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#performs"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasAccessTo"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#performs"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:comment>A person or thing (or piece of software of course) that takes an active role or produces a specified effect</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#AmoebaStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AmoebaStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacijska%20struktura%20amebe for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior">
        <rdfs:subClassOf>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAcceptedBy"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#NormativeSystem"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAcceptedBy"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#NormativeSystem"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <rdfs:comment>An agent behavior is some kind of activity performed by some agent. It has to be acceptable by a normative system the agent belongs to.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#BioteamingOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#BioteamingOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Biotimovi for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#BusinessProcessReengineering -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#BusinessProcessReengineering">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Rein%C5%BEenjering%20poslovnih%20procesa for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ClientServerBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ClientServerBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>Behavior which resembles the client-server model, e.g. the client sends requests, the server responds to them</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ClusterStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ClusterStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StableSuperStructure"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StarburstStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Klaster%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#CommunitiesOfPractice -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CommunitiesOfPractice">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Dru%C5%A1tva%20razmjene%20najboljih%20praksi for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ComplexAnalyticalMethod -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ComplexAnalyticalMethod">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Kompleksna%20analiti%C4%8Dka%20metoda for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing">
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:comment>A particular criteria for organizing things like processes, organizational units, strategies or cultural artifacts.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Culture -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Culture">
        <rdfs:comment>Organizational culture in organizations is a complex cybernetic system that deals with various intangible aspects of organizational behavior including but not limited to language, symbols, rituals, customs, norms, methods of problem solving, knowledge, learning etc. 
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
        <rdfs:comment>A relation between cultural artifacts (e.g. knowledge, norms etc.) in the organizational culture perspective</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#CustomerOrientedStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CustomerOrientedStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacijska%20struktura%20orijentirana%20prema%20potro%C5%A1a%C4%8Dima for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Divizionalna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#DynamicNetworkStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#DynamicNetworkStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
        <rdfs:comment>See:
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Dinami%C4%8Dna%20mre%C5%BEa
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=%C5%A0pageti%20organizacijska%20struktura
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Hollywoodska%20organizacijska%20struktura
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Umre%C5%BEena%20organizacijska%20struktura
for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#EmpoweredOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#EmpoweredOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Osna%C5%BEena%20organizacija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#FiniteStateMachineBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FiniteStateMachineBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>A behavior which resembles a finite state machine in which every node is
an activity to be performed</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#FishnetStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FishnetStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacijska%20struktura%20ribarske%20mre%C5%BEe for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#FractalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FractalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Fraktalna%20organizacijska%20struktura%20i%20koncept%20kaosa%20u%20organizaciji for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#FrontBackStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FrontBackStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Pramac/krma%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#FunctionalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FunctionalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Funkcionalna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Heterarhijske%20strukture for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Hijerarhijske%20strukture for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Hibridne%20strukture for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#HypertextOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HypertextOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Hipertekst%20organizacija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#InfiniteFlatHierarchyStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InfiniteFlatHierarchyStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Beskona%C4%8Dno%20plitka%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#InternalMarketStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InternalMarketStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Unutarnja%20tr%C5%BEi%C5%A1ta for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#InvertedStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InvertedStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Izvrnuta%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ItineraryBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ItineraryBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>Behavior which allows mobile agents to travel across various locations and perform tasks</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Kaizen -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Kaizen">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Kaizen for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:subClassOf>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAccessibleTo"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAccessibleTo"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <rdfs:comment>By knowledge artifact we understand a wide range of explicit knowledge in which we assume that it is queriable by the agent, including but not limited to data and knowledge bases, neural networks and machine learning architectures, various information services etc.
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#LeanManagement -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#LeanManagement">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Vitki%20menad%C5%BEment for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#LearningOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#LearningOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacija%20koja%20u%C4%8Di for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ListenerBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ListenerBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#ObserverBehavior"/>
        <rdfs:comment>A special type of observer behavior in which and agent awaits a message of some other agent</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#MatrixStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#MatrixStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Matri%C4%8Dna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#MergerStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#MergerStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Spajanja%20i%20preuzimanja for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Norm -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Norm">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
        <rdfs:comment>Norms are defined as (socially) accepted behavior in a defined group and represent a blueprint for behaving in said group</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#NormativeSystem -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#NormativeSystem">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalKnowledgeNetwork"/>
        <rdfs:comment>A normative system is a system of norms which apply to some organizational unit</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Objective -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective">
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_1dcf5fa9_2d15_4876_8912_5a3079ab8ea3"/>
        <rdfs:subClassOf>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isAchievedBy"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <rdfs:comment>Any measurable objective that can be achieved by an atomic activity. Objectives can trigger processes.
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ObserverBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ObserverBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>Behavior in which an agents awaits an event in order to perform its actions</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OneShotBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OneShotBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>A behavior which represents a simple task or activity which is stopped after performance</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OpenOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OpenOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Otvorena%20organizacija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasChange"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalChange"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCulture"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalCulture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasEnvironment"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalEnvironment"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasIndividuals"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalIndividuals"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasProcesses"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalProcesses"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasStrategy"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStrategy"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasStructure"/>
                        <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                        <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization consisting of various perspectives including structure, culture, processes, strategy and individuals.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalChange -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalChange">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsChangeFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesChange"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsChangeFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of organizational change in some agent organization (possibly influenced by some organizational design method)</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalCulture -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalCulture">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsCultureFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsCultureFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesCulture"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Culture"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization&apos;s culture</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod">
        <rdfs:comment>A method which brings change in and influences any part of an agent organization </rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalEnvironment -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalEnvironment">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsEnvironmentFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#EnvironmentIsUsedBy"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsEnvironmentFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of the organizational environment of some agent organization (includes besides the environemnt the organization is located in also other organizations which are engaged in some way)</rdfs:comment>
        <rdfs:comment xml:lang="en">Everything outside of the modelled system that can affect the modelled system. E.g. outside forces and agents that will not bemodelled in detail at the moment.</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalIndividuals -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalIndividuals">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelIndividualsFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelIndividualsFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesAgents"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization&apos;s individuals (agents)</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalKnowledgeNetwork -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalKnowledgeNetwork">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
                    <owl:Class>
                        <owl:intersectionOf rdf:parseType="Collection">
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing"/>
                                <owl:qualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:qualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
                            </owl:Restriction>
                        </owl:intersectionOf>
                    </owl:Class>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Culture"/>
        <rdfs:comment>Agent organizations can be seen as a network of knowledge artifacts which are accessible by particular agents. We will denote these with the label organizational knowldge network. Special cases of knowledge artifacts are norms which establish the rules of interaction between agents and values which influence decision making and selection of objectives
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalMemory -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalMemory">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacijska%20memorija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalProcesses -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalProcesses">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelProcessesFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelProcessesFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesProcesses"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization&apos;s processes</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStrategy -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStrategy">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStrategyFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStrategyFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesStrategy"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization&apos;s strategy</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStructureFor"/>
                        <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#modelsStructureFor"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesStructure"/>
                        <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>A model of an agent organization&apos;s structure</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Agent"/>
                    <owl:Class>
                        <owl:intersectionOf rdf:parseType="Collection">
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#definesRoles"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRole"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#definesRoles"/>
                                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#usesStructure"/>
                                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5#consistsOf"/>
                                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing"/>
                                <owl:qualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:qualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
                            </owl:Restriction>
                        </owl:intersectionOf>
                    </owl:Class>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:comment>An organizational unit is (1) a network of agents (or lower level units), (2) which are organized according to some organizational criteria and (3) in which roles for lower level units are defined. This definition has an important implication: it allows us to deal with agents, groups and teams of agents, organizations of agents, networks of organizations of agents (or organizations of organizations) as well as virtual organizations of agents (as overlay structures) in the same way. This in particular means that organizational units may form a lattice structure in which each unit can belong to several super-units and/or be composed of several subunits. The criteria of organizing could for example be an objective, function, goal, mission, unit name, higher-order role etc.
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ParallelBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ParallelBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>Various behaviors are run in parallel</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#PeriodicBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#PeriodicBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>A behavior which is looped possibly with a given period of time intervals between iterations</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#PlatformOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#PlatformOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Platformska%20organizacija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Process -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Process">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
                    <owl:Class>
                        <owl:intersectionOf rdf:parseType="Collection">
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isTriggeredBy"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isTriggeredBy"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing"/>
                                <owl:qualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:qualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
                            </owl:Restriction>
                        </owl:intersectionOf>
                    </owl:Class>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <owl:disjointWith rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:comment>A process is (1) a network of activities (or lower level processes) (2) according to some criteria of organizing and (3) triggered by some strategy. The given definition allows for modeling organizations as networks of processes which can be defined in a number of ways. For example, the criteria for organizing might be that one process uses inputs from another or that two processes are using the same resources, or even that two processes are performed by the same organizational unit or that they are crucial for the same organizational goal.
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
        <rdfs:comment>A relation between two processes in the processes perspective</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ProductDivisionalStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProductDivisionalStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Predmetna%20divizionalna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ProjectOrientedStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProjectOrientedStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Projektna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation"/>
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation"/>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#ValuePartition"/>
        <rdfs:comment>Value partition for the various organizational networks in some organizational architecture</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Role -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Role">
        <owl:equivalentClass>
            <owl:Restriction>
                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#isRoleIn"/>
                <owl:minQualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:minQualifiedCardinality>
                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
            </owl:Restriction>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Norm"/>
        <rdfs:comment>A prescribed or expected behavior associated with a particular position or status in a group or organization</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#RoleFactoryBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#RoleFactoryBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>Behavior added at runtime and then enacted by the agent</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#SequentialBehavior -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#SequentialBehavior">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:comment>A sequence of other behaviors</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ShamrockOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ShamrockOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See:
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacija%20djeteline
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Federalizam
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Obrnuta%20krafna
for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#SixSigma -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#SixSigma">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=6%20%CF%83%20(Six%20Sigma) for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StableSuperStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StableSuperStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Stabilne%20suprastrukture for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StarburstStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StarburstStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StableSuperStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Organizacijska%20struktura%20raspr%C5%A1ene%20zvijezde for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StaticNetworkStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StaticNetworkStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Stati%C4%8Dna%20mre%C5%BEa for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicAllianceStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicAllianceStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        <rdfs:comment>See:
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Strate%C5%A1ki%20savezi%20i%20alijanse
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Internetski%20savezi
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Keiretsu
http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Chaebol
for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicOrganization -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicOrganization">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalArchitecture"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Strategijska%20organizacija for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Strategy">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
                    <owl:Class>
                        <owl:intersectionOf rdf:parseType="Collection">
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasRelation"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#triggers"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Process"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#hasCriteriaOfOrganizing"/>
                                <owl:qualifiedCardinality rdf:datatype="http://www.w3.org/2001/XMLSchema#nonNegativeInteger">1</owl:qualifiedCardinality>
                                <owl:onClass rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#CriteriaOfOrganizing"/>
                            </owl:Restriction>
                        </owl:intersectionOf>
                    </owl:Class>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:comment>Strategy is closely bound the the Balanced ScoreCard paradigm. A strategy consists of: (1) a network of objectives (or other smaller strategies), (2) a criteria of organizing this network e.g. criteria might be influence (the outcome of one strategy influences another, for example a mathematical function), responsibility (two strategies are under the responsibility of the same organizational unit), achieveability (two strategies can be achieved by the same organizational process), etc., (3) a process which is triggered from the strategy as a response to some environmental or internal change.
</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
        <rdfs:comment>A relation between two strategies in the strategic perspective</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#RelationValuePartition"/>
        <rdfs:comment>A relation between two organizational units in the organizational structure perspective</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Suprastrukture for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#TaguchiMethod -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TaguchiMethod">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Taguchi%20metoda for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#TeamBasedStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TeamBasedStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Timska%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#TensorStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TensorStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Tenzorska%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#TeritorialStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TeritorialStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Teritorijalna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#TotalQualityManagement -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TotalQualityManagement">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalDesignMethod"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Cjelovito%20upravljanje%20kvalitetom for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#ValuePartition -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ValuePartition">
        <rdfs:comment>Value partitions</rdfs:comment>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#VirtualStructure -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#VirtualStructure">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure"/>
        <rdfs:comment>See http://ai.foi.hr/oovasis/wiki/wiki.php?name=OOVASIS&amp;parent=NULL&amp;page=Virtualna%20organizacijska%20struktura for details</rdfs:comment>
    </owl:Class>
    


    <!-- http://purl.org/goodrelations/v1#ProductOrService -->

    <owl:Class rdf:about="http://purl.org/goodrelations/v1#ProductOrService"/>
    


    <!-- http://purl.org/net/VideoGameOntology#Achievement -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Achievement">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:comment xml:lang="en">The vgo:Achievement is a reward gained in a game due to some event accomplished in the game. Achievements are commonly used in game industry to reward players for having accomplished tasks in the game. 
This ontology defines various subclasses of vgo:Achievement, which are all based on the classification presented by Markus Montola et al. [Markus Montola, Timo Nummenmaa, Andrés Lucero, Marion Boberg, and Hannu Korhonen, 2009, “Applying game achievement systems to enhance user experience in a photo sharing service”, In Proceedings of the 13th International MindTrek Conference: Everyday Life in the Ubiquitous Era (MindTrek &apos;09)] http://dl.acm.org/citation.cfm?id=1621859</rdfs:comment>
        <rdfs:label xml:lang="en">Achievement</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Character -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Character">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:comment xml:lang="en">A vgo:Character is any actor that can exists in a game. A character  can be a human-like creature as seen traditionally in video games. However, a character could also be a car, a paddle in game of Pong or spaceship of a space shooter game. This is often contextually related to the gameplay of a certain game. A character can be either controller by a player or by a computer.</rdfs:comment>
        <rdfs:label xml:lang="en">Character</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Collection -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Collection">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Collection type of achievement is typically rewarded from collecting an amount of certain items in a game. An example of vgo:Collection would be obtaining a full set of christmas clothes for a character to wear or collecting every possible flower in the game.</rdfs:comment>
        <rdfs:label xml:lang="en">Collection</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Completion -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Completion">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Completion type of achievement is usually rewarded from successfully completing a certain goal or subgoal in a game. An example of vgo:Completion would be to save a princess from a burning tower or completing all side-quests in a game.</rdfs:comment>
        <rdfs:label xml:lang="en">Completion</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Curiosity -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Curiosity">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Curiosity describes funny random things that can happen or be found in the game. An example could be jumping from the Eiffel tower without dying or following a comupter controlled character’s activities for one hour.</rdfs:comment>
        <rdfs:label xml:lang="en">Curiosity</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Fandom -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Fandom">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Fandom achievement is related to doing some true fan activities. An example of vgo:Fandom could be purchasing a collectors edition of the game or attending a fan gathering.</rdfs:comment>
        <rdfs:label xml:lang="en">Fandom</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Feature -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Feature">
        <rdfs:comment xml:lang="en">vgo:Feature describes an ability or characteristic. For example, a sword could have “damage dealing” ability and a bottle of water could be “potable”.</rdfs:comment>
        <rdfs:label xml:lang="en">Feature</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#GainEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#GainEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#GameEvent"/>
        <rdfs:comment xml:lang="en">The vgo:GainEvent describes an event that is related to character/player gaining something in a game. This is a subclass of vgo:GameEvent as gaining something is related to a specific game. For example, a player can gain a new character, achievement or item.</rdfs:comment>
        <rdfs:label xml:lang="en">gain event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Game -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Game">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE"/>
        <rdfs:comment xml:lang="en">The vgo:Game class describes a game product that can be played by a player.
Examples of games are Pong, Grand Theft Auto, Pokemon and Need for Speed.</rdfs:comment>
        <rdfs:label xml:lang="en">Game</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#GameEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#GameEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:comment xml:lang="en">The vgo:GameEvent describes an event that takes place in a game without straight player interaction. GameEvents are often very specific for each game. Examples of vgo:GameEvent could be an enemy dying, connecting to a multiplayer server, loading a new level or playing an animation.</rdfs:comment>
        <rdfs:label xml:lang="en">game event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#GameProduct -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#GameProduct">
        <rdfs:subClassOf rdf:resource="http://purl.org/goodrelations/v1#ProductOrService"/>
        <rdfs:subClassOf rdf:resource="http://schema.org/Product"/>
        <rdfs:subClassOf>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://purl.org/net/VideoGameOntology#Achievement"/>
                    <rdf:Description rdf:about="http://purl.org/net/VideoGameOntology#Character"/>
                    <rdf:Description rdf:about="http://purl.org/net/VideoGameOntology#Game"/>
                    <rdf:Description rdf:about="http://purl.org/net/VideoGameOntology#Item"/>
                    <rdf:Description rdf:about="http://purl.org/net/VideoGameOntology#PlayingArea"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:subClassOf>
        <rdfs:comment xml:lang="en">A vgo:GameProduct is anything that is for sale inside a game. These can be either normal game items purchased with in-game currency or with real world money. An example of vgo:GameProduct could be a consumable health potion bought with real money, a better weapon or some visual improvement (e.g. Hats in Steam). Basically a game product can be anything, a character, an item or an achievement.
GameProduct is a subclass of Good Relations: ProductOrService &amp; schema:Product. Since vgo:GameProduct is a type of buyable product, it reuses the properties available in the schema and Good Relations, such as currency price, validity of the offer and so on.</rdfs:comment>
        <rdfs:label xml:lang="en">game product</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Genre -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Genre">
        <rdfs:comment xml:lang="en">The vgo:Genre class describes the genre a game belongs to. All of the games have at least one genre. Examples of this are RPG, Simulator and Adventure</rdfs:comment>
        <rdfs:label xml:lang="en">Genre</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#HardMode -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#HardMode">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:HardMode achievement describes succeeding in a game on a high difficulty level. An example could be completing the “Doom” game on Nightmare difficulty level.</rdfs:comment>
        <rdfs:label xml:lang="en">hard mode</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#InAppPurchaseEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#InAppPurchaseEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:comment xml:lang="en">The vgo:InAppPurchaseEvent describes an event that is related to making a purchase with real money inside a game. This is a subclass of InstantaneousEvent because it happens at certain moment in time. An example of vgo:InAppPurchaseEvent would be unlocking secret levels with real money or purchasing better equipment with real money.</rdfs:comment>
        <rdfs:label xml:lang="en">in-app purchase event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#InstantaneousEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#InstantaneousEvent">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
        <rdfs:comment xml:lang="en">The vgo:InstantaneousEvent class describes an event that happens during the gameplay at a certain moment in time. This can be a player gaining an achievement, killing an enemy or making an in-app purchase.</rdfs:comment>
        <rdfs:label xml:lang="en">instantaneous event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Item -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Item">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact"/>
        <rdfs:comment xml:lang="en">A vgo:Item portrays any item that exists in a game. The item can either be just visual part of the game or a concrete usable item. As an example an item could be a drinkable potion, a magical sword or just a flower pot.</rdfs:comment>
        <rdfs:label xml:lang="en">Item</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Leaderboard -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Leaderboard">
        <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
        <rdfs:comment xml:lang="en">The vgo:Leaderboard class describes a ranking system of the players. There can be multiple rankings in a game, for example, the kill-count ranking of Modern Warfare or the best time listing of Formula 1 game.</rdfs:comment>
        <rdfs:label xml:lang="en">Leaderboard</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#LoseEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#LoseEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#GameEvent"/>
        <rdfs:comment xml:lang="en">vgo:LoseEvent describes an event that is related to character/player losing something in a game. This is a subclass of GameEvent as gaining something is related to a specific game. For example, a player can lose a character due to trade with another player. Another example would be a character losing item due to consuming it.</rdfs:comment>
        <rdfs:label xml:lang="en">lose event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Loyalty -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Loyalty">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Loyalty achievement is used to give recognition to loyal players. For example, this could be an achievement received after subscribing to the game for a year.</rdfs:comment>
        <rdfs:label xml:lang="en">Loyalty</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Luck -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Luck">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Lucky describes an achievement that is awarded to the player in a lucky situation. An example of vgo:Lucky achievement would be winning in a lottery or throwing “Yahtzee” without re-rolling the dice.</rdfs:comment>
        <rdfs:label xml:lang="en">Luck</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#MenuEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#MenuEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:comment xml:lang="en">The vgo:MenuEvent describes an event that is related to interacting with the ingame menu. An example of menu event is muting sounds, changing graphic settings, changing gameplay difficulty or remapping game controls.</rdfs:comment>
        <rdfs:label xml:lang="en">menu event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Minigame -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Minigame">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Minigame achievement describes success in mini-games that have been included in a certain game but are not vital for completing the game. An example could be to complete all the Pizza deliveries in GTA minigame or gaining over 100 dollars while playing poker in Red Dead Redemption.</rdfs:comment>
        <rdfs:label xml:lang="en">Minigame</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Multiplayer -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Multiplayer">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Multiplayer achievement describes anything that can be awarded to one or multiple players due to their gameplay in multiplayer. For example, this could be winning 10 Team Fortress matches in a row with the same team or getting killed ten times in a row in Counter-Strike.</rdfs:comment>
        <rdfs:label xml:lang="en">Multiplayer</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Paragon -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Paragon">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Paragon is a rare achievement that is given only to limited number of players. An example of vgo:Paragon achievement could be the first player to finish a game under 10 hours or the first ten players to complete the game 100% through.</rdfs:comment>
        <rdfs:label xml:lang="en">Paragon</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Player -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Player">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Human_Immersed_Agent"/>
        <rdfs:subClassOf rdf:resource="http://xmlns.com/foaf/0.1/Agent"/>
        <rdfs:comment xml:lang="en">The vgo:Player describes the entity playing the game. This can be either a human or a computer. vgo:Player class is used to keep a profile of a certain playing entity and to connect all the games, achievements and characters he/she has. The vgo:Player is a subclass of foaf:Person as it contains all relative information of a certain person.</rdfs:comment>
        <rdfs:label xml:lang="en">Player</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#PlayerEvent -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#PlayerEvent">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#InstantaneousEvent"/>
        <rdfs:comment xml:lang="en">The vgo:PlayerEvent describes a vgo:InstantaneousEvent that is caused by the player. For example jumping in the game, throwing an item or pressing a joystick button.</rdfs:comment>
        <rdfs:label xml:lang="en">player event</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#PlayingArea -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#PlayingArea">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Workspace"/>
        <rdfs:comment xml:lang="en">The vgo:PlayingArea is the description of a place where the gameplay takes place. All of the games have some kind of area where they are played in. An example of playing areas could be football field in soccer game, a race track from a racing game or a star system of EVE Online.</rdfs:comment>
        <rdfs:label xml:lang="en">playing area</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Session -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Session">
        <rdfs:subClassOf rdf:resource="http://www.w3.org/2002/07/owl#Thing"/>
        <rdfs:comment xml:lang="en">The vgo:Session class describes a session of gameplay. A session can be a single round of chess, a round of Counter-Strike, one half-time of soccer or one race of Formula 1. vgo:Session class can be used to store gameplay information, especially for analytical reasons.</rdfs:comment>
        <rdfs:label xml:lang="en">Session</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#SpecialPlayStyle -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#SpecialPlayStyle">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:SpecialPlayStyle achievement is awarded to players after playing a game in special fashion. Often this is something harder than the regular play and requires more player experience to excel in it. An example of vgo:SpecialPlayStyle could be to complete a game without any violence or against a timer.</rdfs:comment>
        <rdfs:label xml:lang="en">special play style</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Tutorial -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Tutorial">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Tutorial achievement is  awarded to a player for trying out various features of the game. This is often related to learning how to play the game, how the controls work and how the game logic works. An example of vgo:Tutorial could be testing out newly gained special equipment or just playing through the in-game tutorial in the beginning.</rdfs:comment>
        <rdfs:label xml:lang="en">Tutorial</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Veteran -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Veteran">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Veteran achievement is an award that is given for accumulating a lot of play hours or game actions. For example, vgo:Veteran could be playing thousand hours of World of Tanks or making 100 goals in ice hockey game.</rdfs:comment>
        <rdfs:label xml:lang="en">Veteran</rdfs:label>
    </owl:Class>
    


    <!-- http://purl.org/net/VideoGameOntology#Virtuosity -->

    <owl:Class rdf:about="http://purl.org/net/VideoGameOntology#Virtuosity">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Achievement"/>
        <rdfs:comment xml:lang="en">The vgo:Virtuosity describes an achievement that is awarded for playing masterfully in the game. Examples of virtuous play could be finishing the game without saving at all, dying zero times or preventing an opposing team from scoring any goals in a soccer game.</rdfs:comment>
        <rdfs:label xml:lang="en">Virtuosity</rdfs:label>
    </owl:Class>
    


    <!-- http://schema.org/Product -->

    <owl:Class rdf:about="http://schema.org/Product"/>
    


    <!-- http://www.w3.org/2002/07/owl#Thing -->

    <owl:Class rdf:about="http://www.w3.org/2002/07/owl#Thing"/>
    


    <!-- http://www.w3.org/ns/adms#SemanticAsset -->

    <owl:Class rdf:about="http://www.w3.org/ns/adms#SemanticAsset"/>
    


    <!-- http://xmlns.com/foaf/0.1/Agent -->

    <owl:Class rdf:about="http://xmlns.com/foaf/0.1/Agent"/>
    


    <!-- http://xmlns.com/foaf/0.1/Person -->

    <owl:Class rdf:about="http://xmlns.com/foaf/0.1/Person"/>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#SituatedOrganizationalUnit -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/fd4661d8e8cf52991559fb29ae4c91ec057f99fb/Previous%20Work/MAMbO5.owl#SituatedOrganizationalUnit">
        <owl:equivalentClass>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Inhabitant_Agent"/>
                    <owl:Class>
                        <owl:intersectionOf rdf:parseType="Collection">
                            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law"/>
                                <owl:someValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#has_IVE_Law"/>
                                <owl:allValuesFrom rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
                            </owl:Restriction>
                        </owl:intersectionOf>
                    </owl:Class>
                </owl:unionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalUnit"/>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_0c864038_db01_4f7d_87c0_650ad7f84af1 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_0c864038_db01_4f7d_87c0_650ad7f84af1">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
        <rdfs:label xml:lang="en-gb">Inventory</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_1dcf5fa9_2d15_4876_8912_5a3079ab8ea3 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_1dcf5fa9_2d15_4876_8912_5a3079ab8ea3">
        <rdfs:label xml:lang="en-gb">Quest</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_455b23c2_5f8a_4397_841f_33132d3b92a2 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_455b23c2_5f8a_4397_841f_33132d3b92a2">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#IVE_Law"/>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_b16d01eb_7c6a_47a7_b7a4_32d3f83f30f3"/>
        <rdfs:label xml:lang="en-gb">Local rule</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_b16d01eb_7c6a_47a7_b7a4_32d3f83f30f3 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf#OWLClass_b16d01eb_7c6a_47a7_b7a4_32d3f83f30f3">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Norm"/>
        <rdfs:label xml:lang="en-gb">Rule</rdfs:label>
    </owl:Class>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Individuals
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://autosemanticgame.eu/ontologies/cgo# -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/cgo#">
        <rdf:type rdf:resource="http://www.w3.org/ns/adms#SemanticAsset"/>
    </owl:NamedIndividual>
    


    <!-- http://autosemanticgame.eu/ontologies/cgo#owensacco -->

    <owl:NamedIndividual rdf:about="http://autosemanticgame.eu/ontologies/cgo#owensacco">
        <rdf:type rdf:resource="http://xmlns.com/foaf/0.1/Person"/>
        <owl:sameAs rdf:resource="http://autosemanticgame.eu/ontologies/gco#owensacco"/>
        <rdfs:label>Owen Sacco</rdfs:label>
        <foaf:homepage rdf:resource="http://www.owensacco.com"/>
        <foaf:name>Owen Sacco</foaf:name>
    </owl:NamedIndividual>
    


    <!-- http://autosemanticgame.eu/ontologies/gco#owensacco -->

    <rdf:Description rdf:about="http://autosemanticgame.eu/ontologies/gco#owensacco"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Annotations
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Action">
        <rdfs:comment xml:lang="en">Action as an effect-inducing function of an artefact.</rdfs:comment>
    </rdf:Description>
    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Manual">
        <rdfs:comment xml:lang="en">Used to define Artifacts and describe how to use them.</rdfs:comment>
    </rdf:Description>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // General axioms
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AcademicStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FrontBackStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InvertedStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AcquisitionStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AdhocracyStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FractalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#MergerStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StableSuperStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicAllianceStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#AmoebaStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TeamBasedStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#VirtualStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#BioteamingOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#EmpoweredOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HypertextOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#LearningOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OpenOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#PlatformOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ShamrockOrganization"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategicOrganization"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#BusinessProcessReengineering"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CommunitiesOfPractice"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ComplexAnalyticalMethod"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Kaizen"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#LeanManagement"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#OrganizationalMemory"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#SixSigma"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TaguchiMethod"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TotalQualityManagement"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CultureRelation"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProcessRelation"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StrategyRelation"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StructuralRelation"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#CustomerOrientedStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProductDivisionalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TeritorialStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#DivisionalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FunctionalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#MatrixStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#ProjectOrientedStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#TensorStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#DynamicNetworkStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#FishnetStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InfiniteFlatHierarchyStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#InternalMarketStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#StaticNetworkStructure"/>
        </owl:members>
    </rdf:Description>
    <rdf:Description>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#AllDisjointClasses"/>
        <owl:members rdf:parseType="Collection">
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HeterarchicalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HierarchicalStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#HybridStructure"/>
            <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#SuperStructure"/>
        </owl:members>
    </rdf:Description>
</rdf:RDF>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

