@prefix : <http://www.semanticweb.org/bogdan/ontologies/2025/5/untitled-ontology-91/> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@base <http://www.semanticweb.org/bogdan/ontologies/2025/5/untitled-ontology-91/> .

<https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx> rdf:type owl:Ontology ;
                                                                                                                      owl:imports <https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf> ;
                                                                                                                      rdfs:comment "The MAGO-Ga ontology is designed to model gamified multi-agent systems modelled as intelligent virtual environments."@en ;
                                                                                                                      rdfs:label "The Gamification of an Intelligent Virtual Environment Ontology (MAGO-Ga)"@en .

#################################################################
#    Object Properties
#################################################################

###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_033c65dc_4b0b_49a0_9bde_461467eb76d6
:OWLObjectProperty_033c65dc_4b0b_49a0_9bde_461467eb76d6 rdf:type owl:ObjectProperty ;
                                                        rdfs:domain :OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045 ;
                                                        rdfs:range <http://purl.org/net/VideoGameOntology#GainEvent> ;
                                                        rdfs:label "has gamification outcome"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_268d7ca3_6f86_4e57_9627_e0320f1d33d1
:OWLObjectProperty_268d7ca3_6f86_4e57_9627_e0320f1d33d1 rdf:type owl:ObjectProperty ;
                                                        rdfs:domain :OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045 ;
                                                        rdfs:range :OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9 ;
                                                        rdfs:label "has gamification goal"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_37fa3a36_2699_4e83_b0d7_8b8e6349fd73
:OWLObjectProperty_37fa3a36_2699_4e83_b0d7_8b8e6349fd73 rdf:type owl:ObjectProperty ,
                                                                 owl:FunctionalProperty ;
                                                        rdfs:domain :OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7 ;
                                                        rdfs:range :OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 ;
                                                        rdfs:label "has personality feature"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_67cb4fc7_8aa5_4668_a353_308e7d7b6dd1
:OWLObjectProperty_67cb4fc7_8aa5_4668_a353_308e7d7b6dd1 rdf:type owl:ObjectProperty ;
                                                        owl:inverseOf :OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c ;
                                                        rdfs:label "defines feature"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c
:OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c rdf:type owl:ObjectProperty ;
                                                        rdfs:domain :OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 ;
                                                        rdfs:range :OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88 ;
                                                        rdfs:label "is a part of model"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_8c046b10_bc4c_4cb2_aaf8_98113094df12
:OWLObjectProperty_8c046b10_bc4c_4cb2_aaf8_98113094df12 rdf:type owl:ObjectProperty ;
                                                        rdfs:domain <http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event> ;
                                                        rdfs:range <http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact> ;
                                                        rdfs:label "has event object"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_a5d640f9_c2fc_479c_ab6a_0d354f65e652
:OWLObjectProperty_a5d640f9_c2fc_479c_ab6a_0d354f65e652 rdf:type owl:ObjectProperty ,
                                                                 owl:FunctionalProperty ;
                                                        rdfs:domain [ rdf:type owl:Class ;
                                                                      owl:unionOf ( <http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent>
                                                                                    :OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7
                                                                                    :OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045
                                                                                  )
                                                                    ] ;
                                                        rdfs:range :OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5 ;
                                                        rdfs:label "has personality profile"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_dcd17b13_165e_4453_a7f5_24ded1c0bc7e
:OWLObjectProperty_dcd17b13_165e_4453_a7f5_24ded1c0bc7e rdf:type owl:ObjectProperty ;
                                                        rdfs:domain <http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent> ;
                                                        rdfs:range <http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property> ;
                                                        rdfs:label "has observable property"@en-gb .


#################################################################
#    Data properties
#################################################################

###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_40390f82_26b7_4600_a674_c779002bf747
:OWLDataProperty_40390f82_26b7_4600_a674_c779002bf747 rdf:type owl:DatatypeProperty ,
                                                               owl:FunctionalProperty ;
                                                      rdfs:domain :OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7 ;
                                                      rdfs:range xsd:float ;
                                                      rdfs:label "has weight value"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_aaa77578_43f1_4b25_9e75_c8fdd0126c56
:OWLDataProperty_aaa77578_43f1_4b25_9e75_c8fdd0126c56 rdf:type owl:DatatypeProperty ;
                                                      rdfs:range xsd:string ;
                                                      rdfs:label "has name"@en-gb .


#################################################################
#    Classes
#################################################################

###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7
:OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7 rdf:type owl:Class ;
                                               rdfs:label "Feature weight"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88
:OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88 rdf:type owl:Class ;
                                               rdfs:label "Personality model"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5
:OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5 rdf:type owl:Class ;
                                               rdfs:subClassOf <http://purl.org/net/VideoGameOntology#Feature> ;
                                               rdfs:label "Personality profile"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_420f7e1b_f0c8_4d4c_abfb_365f50700f53
:OWLClass_420f7e1b_f0c8_4d4c_abfb_365f50700f53 rdf:type owl:Class ;
                                               rdfs:subClassOf :OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 ;
                                               rdfs:label "Facet"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_5f4dc944_3afc_4327_873a_483885f4114b
:OWLClass_5f4dc944_3afc_4327_873a_483885f4114b rdf:type owl:Class ;
                                               rdfs:subClassOf <http://purl.org/net/VideoGameOntology#Feature> ;
                                               rdfs:label "Gender"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9
:OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9 rdf:type owl:Class ;
                                               rdfs:subClassOf <http://personales.upv.es/ccarrasc/ooooaflsmas#Objective> ;
                                               rdfs:label "Gamification goal"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_abf22d34_f468_41bb_826c_c555a1846b3f
:OWLClass_abf22d34_f468_41bb_826c_c555a1846b3f rdf:type owl:Class ;
                                               rdfs:subClassOf :OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 ;
                                               rdfs:label "Trait"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_ade1dd36_ed64_46ec_842b_e97727baed7f
:OWLClass_ade1dd36_ed64_46ec_842b_e97727baed7f rdf:type owl:Class ;
                                               rdfs:subClassOf <http://purl.org/net/VideoGameOntology#Feature> ;
                                               rdfs:label "Age"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4
:OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 rdf:type owl:Class ;
                                               rdfs:label "Personality feature"@en-gb .


###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045
:OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045 rdf:type owl:Class ;
                                               rdfs:subClassOf <http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact> ;
                                               rdfs:label "Gamification technique"@en-gb .


#################################################################
#    Individuals
#################################################################

###  https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLNamedIndividual_dc90836e_f55d_4c85_93b4_10b826be7ff7
:OWLNamedIndividual_dc90836e_f55d_4c85_93b4_10b826be7ff7 rdf:type owl:NamedIndividual ,
                                                                  :OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045 ;
                                                         rdfs:label "price optimiser"@en-gb .


###  Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi
