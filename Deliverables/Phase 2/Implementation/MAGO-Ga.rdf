<?xml version="1.0"?>
<rdf:RDF xmlns="http://www.semanticweb.org/bogdan/ontologies/2025/5/untitled-ontology-91/"
     xml:base="http://www.semanticweb.org/bogdan/ontologies/2025/5/untitled-ontology-91/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:foaf="http://xmlns.com/foaf/0.1/"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     xmlns:vann="http://purl.org/vocab/vann/"
     xmlns:terms="http://purl.org/dc/terms/">
    <owl:Ontology rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx">
        <owl:imports rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf"/>
        <rdfs:comment xml:lang="en">The MAGO-Ga ontology is designed to model gamified multi-agent systems modelled as intelligent virtual environments.</rdfs:comment>
        <rdfs:label xml:lang="en">The Gamification of an Intelligent Virtual Environment Ontology (MAGO-Ga)</rdfs:label>
    </owl:Ontology>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Object Properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_033c65dc_4b0b_49a0_9bde_461467eb76d6 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_033c65dc_4b0b_49a0_9bde_461467eb76d6">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045"/>
        <rdfs:range rdf:resource="http://purl.org/net/VideoGameOntology#GainEvent"/>
        <rdfs:label xml:lang="en-gb">has gamification outcome</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_268d7ca3_6f86_4e57_9627_e0320f1d33d1 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_268d7ca3_6f86_4e57_9627_e0320f1d33d1">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9"/>
        <rdfs:label xml:lang="en-gb">has gamification goal</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_37fa3a36_2699_4e83_b0d7_8b8e6349fd73 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_37fa3a36_2699_4e83_b0d7_8b8e6349fd73">
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4"/>
        <rdfs:label xml:lang="en-gb">has personality feature</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_67cb4fc7_8aa5_4668_a353_308e7d7b6dd1 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_67cb4fc7_8aa5_4668_a353_308e7d7b6dd1">
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c"/>
        <rdfs:label xml:lang="en-gb">defines feature</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_68e185c9_f82f_4507_8f25_4d50e8b3cc3c">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88"/>
        <rdfs:label xml:lang="en-gb">is a part of model</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_8c046b10_bc4c_4cb2_aaf8_98113094df12 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_8c046b10_bc4c_4cb2_aaf8_98113094df12">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Event"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Physical_Artifact"/>
        <rdfs:label xml:lang="en-gb">has event object</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_a5d640f9_c2fc_479c_ab6a_0d354f65e652 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_a5d640f9_c2fc_479c_ab6a_0d354f65e652">
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:domain>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5"/>
        <rdfs:label xml:lang="en-gb">has personality profile</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_dcd17b13_165e_4453_a7f5_24ded1c0bc7e -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLObjectProperty_dcd17b13_165e_4453_a7f5_24ded1c0bc7e">
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Agent"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Observable_Property"/>
        <rdfs:label xml:lang="en-gb">has observable property</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Data properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_40390f82_26b7_4600_a674_c779002bf747 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_40390f82_26b7_4600_a674_c779002bf747">
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#float"/>
        <rdfs:label xml:lang="en-gb">has weight value</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_aaa77578_43f1_4b25_9e75_c8fdd0126c56 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLDataProperty_aaa77578_43f1_4b25_9e75_c8fdd0126c56">
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <rdfs:label xml:lang="en-gb">has name</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_0613fb28_22ee_4c72_939f_4344f65624d7">
        <rdfs:label xml:lang="en-gb">Feature weight</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_161fdab5_a39e_42b4_9cee_84a03f52ee88">
        <rdfs:label xml:lang="en-gb">Personality model</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_19a73670_711c_40c7_a371_230c83aaa5b5">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Feature"/>
        <rdfs:label xml:lang="en-gb">Personality profile</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_420f7e1b_f0c8_4d4c_abfb_365f50700f53 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_420f7e1b_f0c8_4d4c_abfb_365f50700f53">
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4"/>
        <rdfs:label xml:lang="en-gb">Facet</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_5f4dc944_3afc_4327_873a_483885f4114b -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_5f4dc944_3afc_4327_873a_483885f4114b">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Feature"/>
        <rdfs:label xml:lang="en-gb">Gender</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_6dae9e4d_d430_445c_b054_d2db632a0da9">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">Gamification goal</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_abf22d34_f468_41bb_826c_c555a1846b3f -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_abf22d34_f468_41bb_826c_c555a1846b3f">
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4"/>
        <rdfs:label xml:lang="en-gb">Trait</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_ade1dd36_ed64_46ec_842b_e97727baed7f -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_ade1dd36_ed64_46ec_842b_e97727baed7f">
        <rdfs:subClassOf rdf:resource="http://purl.org/net/VideoGameOntology#Feature"/>
        <rdfs:label xml:lang="en-gb">Age</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_b3f8233f_1eca_4632_895f_8ec633cf37e4">
        <rdfs:label xml:lang="en-gb">Personality feature</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
        <rdfs:label xml:lang="en-gb">Gamification technique</rdfs:label>
    </owl:Class>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Individuals
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLNamedIndividual_dc90836e_f55d_4c85_93b4_10b826be7ff7 -->

    <owl:NamedIndividual rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLNamedIndividual_dc90836e_f55d_4c85_93b4_10b826be7ff7">
        <rdf:type rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx#OWLClass_d7900a7c_c25b_4ea3_ad4e_8716bbf24045"/>
        <rdfs:label xml:lang="en-gb">price optimiser</rdfs:label>
    </owl:NamedIndividual>
</rdf:RDF>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

