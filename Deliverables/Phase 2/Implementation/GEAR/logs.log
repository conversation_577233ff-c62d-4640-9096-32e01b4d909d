2025-06-14 23:46:44,634:INFO:slixmpp.features.feature_bind.bind: JID set to: provider1@localhost/oKxcSpv5
2025-06-14 23:46:44,635:INFO:spade.Agent: Agent provider1@localhost connected and authenticated.
2025-06-14 23:46:44,645:INFO:utils.logger: [provider1@localhost] Starting receive behaviour with queue_type: deque
2025-06-14 23:46:44,645:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:44,645:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:45,646:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:45,646:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:45,647:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:45,771:INFO:slixmpp.features.feature_bind.bind: JID set to: provider2@localhost/AH7Nr0c1
2025-06-14 23:46:45,772:INFO:spade.Agent: Agent provider2@localhost connected and authenticated.
2025-06-14 23:46:45,773:INFO:utils.logger: [provider2@localhost] Starting receive behaviour with queue_type: deque
2025-06-14 23:46:45,773:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:45,773:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:46,648:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:46,648:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:46,648:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:46,774:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:46,774:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:46,774:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:47,650:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:47,651:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:47,651:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:47,789:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:47,790:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:47,790:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:48,652:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:48,652:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:48,652:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:48,791:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:48,791:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:48,792:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:48,903:INFO:slixmpp.features.feature_bind.bind: JID set to: consumer1@localhost/kjEQ5k_d
2025-06-14 23:46:48,905:INFO:spade.Agent: Agent consumer1@localhost connected and authenticated.
2025-06-14 23:46:48,905:INFO:spade.behaviour: FSM running state CheckOfferedServices
2025-06-14 23:46:48,905:INFO:utils.logger: [Consumer consumer1@localhost] Checking offered services
2025-06-14 23:46:48,906:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:48,907:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:48,907:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:49,654:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:49,654:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:49,654:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7b5be0>])
2025-06-14 23:46:49,654:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:49,655:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:49,655:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:49,655:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:46:49,655:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:46:49,655:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:46:49,655:INFO:utils.logger: [Provider provider1@localhost] Processing request
2025-06-14 23:46:49,655:INFO:utils.logger: [Provider provider1@localhost] Sent services: ['A', 'B']
2025-06-14 23:46:49,655:INFO:spade.behaviour: FSM transiting from ProcessRequest to Idle.
2025-06-14 23:46:49,656:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'inform'}>
{"services": ["A", "B"]}
</message>

2025-06-14 23:46:49,656:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:49,656:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:49,656:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:49,657:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:49,657:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:49,657:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:49,793:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:49,793:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:49,793:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7bb250>])
2025-06-14 23:46:49,793:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:49,793:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:49,793:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"services": []}
</message>

2025-06-14 23:46:49,794:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:49,794:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:46:49,794:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:46:49,794:INFO:utils.logger: [Provider provider2@localhost] Processing request
2025-06-14 23:46:49,794:INFO:utils.logger: [Provider provider2@localhost] Sent services: ['A', 'C']
2025-06-14 23:46:49,794:INFO:spade.behaviour: FSM transiting from ProcessRequest to Idle.
2025-06-14 23:46:49,794:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'inform'}>
{"services": ["A", "C"]}
</message>

2025-06-14 23:46:49,795:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:49,795:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:49,795:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:49,795:INFO:utils.logger: [Consumer consumer1@localhost] Providers: {provider1@localhost: {'services': ['A', 'B']}, provider2@localhost: {'services': ['A', 'C']}}
2025-06-14 23:46:49,795:INFO:spade.behaviour: FSM transiting from CheckOfferedServices to Idle.
2025-06-14 23:46:49,795:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:49,795:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:46:49,796:INFO:spade.behaviour: FSM transiting from Idle to NextElement.
2025-06-14 23:46:49,796:INFO:spade.behaviour: FSM running state NextElement
2025-06-14 23:46:49,796:INFO:utils.logger: [Consumer consumer1@localhost] Starting with the next element in recipe Recipe.1/3: A, A, C
2025-06-14 23:46:49,796:INFO:spade.behaviour: FSM transiting from NextElement to Tender.
2025-06-14 23:46:49,796:INFO:spade.behaviour: FSM running state Tender
2025-06-14 23:46:49,796:INFO:utils.logger: [Consumer consumer1@localhost] Tendering for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:49,796:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider1@localhost for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:49,797:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:49,797:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:49,908:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,658:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:50,658:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:50,658:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7743e0>])
2025-06-14 23:46:50,658:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:50,659:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:50,659:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,659:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:46:50,659:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:46:50,659:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:46:50,659:INFO:utils.logger: [Provider provider1@localhost] Processing proposal
2025-06-14 23:46:50,659:INFO:utils.logger: [competitive pricing] Compatibility: 0.49280181388336963
2025-06-14 23:46:50,660:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.43
2025-06-14 23:46:50,660:INFO:utils.logger: [quality focus] Compatibility: 0.4975062189439554
2025-06-14 23:46:50,660:INFO:utils.logger: [quality focus] Combined compatibility: 0.44
2025-06-14 23:46:50,660:INFO:utils.logger: [risk taking] Compatibility: 0.5111237375367873
2025-06-14 23:46:50,660:INFO:utils.logger: [risk taking] Combined compatibility: 0.45
2025-06-14 23:46:50,660:INFO:utils.logger: [service milestone] Compatibility: 0.45479361705864085
2025-06-14 23:46:50,660:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:50,660:INFO:utils.logger: [service milestone] Combined compatibility: 0.51
2025-06-14 23:46:50,660:INFO:utils.logger: [price optimizer] Compatibility: 0.4991673599561093
2025-06-14 23:46:50,661:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:50,661:INFO:utils.logger: [price optimizer] Combined compatibility: 0.55
2025-06-14 23:46:50,661:INFO:utils.logger: [quality achievement] Compatibility: 0.4429003679771454
2025-06-14 23:46:50,661:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:50,661:INFO:utils.logger: [quality achievement] Combined compatibility: 0.52
2025-06-14 23:46:50,661:INFO:utils.logger: [Provider provider1@localhost] Sent proposal: Service(name='A', price=10, duration=3)
2025-06-14 23:46:50,661:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:46:50,661:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "A", "price": 10, "duration": 3}, "awards": {"badges": [], "trophies": []}}
</message>

2025-06-14 23:46:50,662:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:50,662:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:50,662:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,662:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider1@localhost: {"service": {"name": "A", "price": 10, "duration": 3}, "awards": {"badges": [], "trophies": []}}
2025-06-14 23:46:50,662:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider2@localhost for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:50,662:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,663:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,663:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,796:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:50,796:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:50,797:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7697b0>])
2025-06-14 23:46:50,797:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:50,797:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:50,797:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,797:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:50,797:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:46:50,797:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:46:50,798:INFO:utils.logger: [Provider provider2@localhost] Processing proposal
2025-06-14 23:46:50,798:INFO:utils.logger: [competitive pricing] Compatibility: 0.7528967827000222
2025-06-14 23:46:50,798:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.62
2025-06-14 23:46:50,798:INFO:utils.logger: [competitive pricing] Applied price discount of 1.73 for provider2@localhost
2025-06-14 23:46:50,798:INFO:utils.logger: [quality focus] Compatibility: 0.7565320007338405
2025-06-14 23:46:50,798:INFO:utils.logger: [quality focus] Combined compatibility: 0.62
2025-06-14 23:46:50,798:INFO:utils.logger: [quality focus] Increased duration by 0.81 for provider2@localhost
2025-06-14 23:46:50,799:INFO:utils.logger: [risk taking] Compatibility: 0.7818792383410816
2025-06-14 23:46:50,799:INFO:utils.logger: [risk taking] Combined compatibility: 0.64
2025-06-14 23:46:50,799:INFO:utils.logger: [risk taking] Applied price variation of -0.32 for provider2@localhost
2025-06-14 23:46:50,799:INFO:utils.logger: [service milestone] Compatibility: 0.822818360620144
2025-06-14 23:46:50,799:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:50,799:INFO:utils.logger: [service milestone] Combined compatibility: 0.85
2025-06-14 23:46:50,799:INFO:utils.logger: [price optimizer] Compatibility: 0.7713590879421037
2025-06-14 23:46:50,799:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:50,799:INFO:utils.logger: [price optimizer] Combined compatibility: 0.83
2025-06-14 23:46:50,800:INFO:utils.logger: [quality achievement] Compatibility: 0.7989112965214936
2025-06-14 23:46:50,800:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:50,800:INFO:utils.logger: [quality achievement] Combined compatibility: 0.84
2025-06-14 23:46:50,800:INFO:utils.logger: [Provider provider2@localhost] Sent proposal: Service(name='A', price=np.float64(5.96), duration=np.float64(4.805444120667795))
2025-06-14 23:46:50,800:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:46:50,801:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}, "awards": {"badges": [], "trophies": []}}
</message>

2025-06-14 23:46:50,801:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:50,801:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:50,801:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,801:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider2@localhost: {"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}, "awards": {"badges": [], "trophies": []}}
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Offers: [{'provider': provider1@localhost, 'service': Service(name='A', price=10, duration=3), 'awards': {'badges': [], 'trophies': []}}, {'provider': provider2@localhost, 'service': Service(name='A', price=5.96, duration=4.805444120667795), 'awards': {'badges': [], 'trophies': []}}]
2025-06-14 23:46:50,802:INFO:spade.behaviour: FSM transiting from Tender to SelectBestOffer.
2025-06-14 23:46:50,802:INFO:spade.behaviour: FSM running state SelectBestOffer
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Selecting best offer for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Personality factors: price_sensitivity=0.93, quality_focus=0.15, badge_appreciation=0.48, risk_tolerance=0.16
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider1@localhost: price=10, duration=3, awards=0, value=9.45
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider2@localhost: price=5.96, duration=4.805444120667795, awards=0, value=5.88
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Selected best offer from provider2@localhost with value 5.88
2025-06-14 23:46:50,802:INFO:utils.logger: [Consumer consumer1@localhost] Best offer: Service(name='A', price=5.96, duration=4.805444120667795) from provider2@localhost
2025-06-14 23:46:50,802:INFO:spade.behaviour: FSM transiting from SelectBestOffer to BudgetCheck.
2025-06-14 23:46:50,802:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:50,803:INFO:spade.behaviour: FSM running state BudgetCheck
2025-06-14 23:46:50,803:INFO:utils.logger: [Consumer consumer1@localhost] Checking budget for service Service(name='A', price=5.96, duration=4.805444120667795)
2025-06-14 23:46:50,803:INFO:utils.logger: [Consumer consumer1@localhost] Budget: 50
2025-06-14 23:46:50,803:INFO:utils.logger: [Consumer consumer1@localhost] Budget check complete. Budget OK? True
2025-06-14 23:46:50,803:INFO:spade.behaviour: FSM transiting from BudgetCheck to RequestService.
2025-06-14 23:46:50,803:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,803:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:50,803:INFO:spade.behaviour: FSM running state RequestService
2025-06-14 23:46:50,803:INFO:utils.logger: [Consumer consumer1@localhost] Requesting service Service(name='A', price=5.96, duration=4.805444120667795) from provider2@localhost
2025-06-14 23:46:50,803:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,803:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:50,803:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:50,908:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:51,663:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:51,663:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:51,663:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7d0b50>])
2025-06-14 23:46:51,663:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:51,663:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:51,663:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:51,664:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:46:51,664:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalReject.
2025-06-14 23:46:51,664:INFO:spade.behaviour: FSM running state ProcessProposalReject
2025-06-14 23:46:51,664:INFO:utils.logger: [Provider provider1@localhost] Processing proposal rejection
2025-06-14 23:46:51,664:INFO:spade.behaviour: FSM transiting from ProcessProposalReject to Idle.
2025-06-14 23:46:51,664:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:51,664:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:51,664:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:51,807:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:51,807:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:51,807:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7d0850>])
2025-06-14 23:46:51,807:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:51,807:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:51,807:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:51,807:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:51,807:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalAccept.
2025-06-14 23:46:51,808:INFO:spade.behaviour: FSM running state ProcessProposalAccept
2025-06-14 23:46:51,808:INFO:utils.logger: [Provider provider2@localhost] Processing proposal acceptance
2025-06-14 23:46:51,808:INFO:spade.behaviour: FSM transiting from ProcessProposalAccept to Idle.
2025-06-14 23:46:51,808:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:51,808:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:52,665:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:52,665:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:52,666:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:52,666:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:52,809:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:52,810:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7d4320>])
2025-06-14 23:46:52,810:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:52,810:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:52,810:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:46:52,810:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Processing request
2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Service providing media: [Item(service providing medium, {'available': True})]
2025-06-14 23:46:52,810:INFO:utils.logger: [Provider provider2@localhost] Request approved
2025-06-14 23:46:52,811:INFO:spade.behaviour: FSM transiting from ProcessRequest to PerformServiceState.
2025-06-14 23:46:52,811:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'agree'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:52,811:INFO:spade.behaviour: FSM running state PerformServiceState
2025-06-14 23:46:52,811:INFO:utils.logger: [Provider provider2@localhost] Performing service
2025-06-14 23:46:52,811:INFO:utils.logger: [Provider provider2@localhost] Service complete
2025-06-14 23:46:52,811:INFO:spade.behaviour: FSM transiting from PerformServiceState to Idle.
2025-06-14 23:46:52,811:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:52,811:INFO:utils.logger: [Provider provider2@localhost] Performing service A for consumer1@localhost
2025-06-14 23:46:52,812:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:52,812:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:52,812:INFO:utils.logger: [Consumer consumer1@localhost] Request approved
2025-06-14 23:46:52,812:INFO:spade.behaviour: FSM transiting from RequestService to WaitForService.
2025-06-14 23:46:52,812:INFO:spade.behaviour: FSM running state WaitForService
2025-06-14 23:46:52,812:INFO:utils.logger: [Consumer consumer1@localhost] Waiting for service Service(name='A', price=5.96, duration=4.805444120667795) from provider2@localhost
2025-06-14 23:46:53,667:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:53,667:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:53,667:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:53,813:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:53,813:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:53,813:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:54,669:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:54,669:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:54,669:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:54,815:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:54,815:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:54,815:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:55,670:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:55,670:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:55,670:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:55,816:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:55,816:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:55,816:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:56,671:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:56,671:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:56,671:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:56,817:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:56,817:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:56,817:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:57,619:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'confirm'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}}
</message>

2025-06-14 23:46:57,619:INFO:utils.logger: [Provider provider2@localhost] Service A complete for consumer1@localhost
2025-06-14 23:46:57,619:INFO:utils.logger: [Provider provider2@localhost] Provided services: {'A': 1}
2025-06-14 23:46:57,619:INFO:spade.behaviour: Killing behavior OneShotBehaviour/PerformServiceBehaviour with exit code: None
2025-06-14 23:46:57,619:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:57,620:INFO:utils.logger: [Consumer consumer1@localhost] Service complete
2025-06-14 23:46:57,620:INFO:spade.behaviour: FSM transiting from WaitForService to ServiceComplete.
2025-06-14 23:46:57,620:INFO:spade.behaviour: FSM running state ServiceComplete
2025-06-14 23:46:57,620:INFO:utils.logger: [Consumer consumer1@localhost] Payment sent
2025-06-14 23:46:57,620:INFO:spade.behaviour: FSM transiting from ServiceComplete to Idle.
2025-06-14 23:46:57,620:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}, "cost": 5.96}
</message>

2025-06-14 23:46:57,620:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:57,620:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:46:57,620:INFO:spade.behaviour: FSM transiting from Idle to NextElement.
2025-06-14 23:46:57,621:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:57,621:INFO:spade.behaviour: FSM running state NextElement
2025-06-14 23:46:57,621:INFO:utils.logger: [Consumer consumer1@localhost] Starting with the next element in recipe Recipe.2/3: A, A, C
2025-06-14 23:46:57,621:INFO:spade.behaviour: FSM transiting from NextElement to Tender.
2025-06-14 23:46:57,621:INFO:spade.behaviour: FSM running state Tender
2025-06-14 23:46:57,621:INFO:utils.logger: [Consumer consumer1@localhost] Tendering for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:57,621:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider1@localhost for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:57,621:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}, "cost": 5.96}
</message>

2025-06-14 23:46:57,622:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:57,622:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:57,622:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:57,672:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:57,672:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:57,672:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b813450>])
2025-06-14 23:46:57,672:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:57,672:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:57,672:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:57,672:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:46:57,673:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:46:57,673:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:46:57,673:INFO:utils.logger: [Provider provider1@localhost] Processing proposal
2025-06-14 23:46:57,673:INFO:utils.logger: [competitive pricing] Compatibility: 0.49280181388336963
2025-06-14 23:46:57,673:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.43
2025-06-14 23:46:57,673:INFO:utils.logger: [quality focus] Compatibility: 0.4975062189439554
2025-06-14 23:46:57,673:INFO:utils.logger: [quality focus] Combined compatibility: 0.44
2025-06-14 23:46:57,673:INFO:utils.logger: [risk taking] Compatibility: 0.5111237375367873
2025-06-14 23:46:57,673:INFO:utils.logger: [risk taking] Combined compatibility: 0.45
2025-06-14 23:46:57,673:INFO:utils.logger: [service milestone] Compatibility: 0.45479361705864085
2025-06-14 23:46:57,673:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:57,673:INFO:utils.logger: [service milestone] Combined compatibility: 0.51
2025-06-14 23:46:57,673:INFO:utils.logger: [price optimizer] Compatibility: 0.4991673599561093
2025-06-14 23:46:57,673:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:57,673:INFO:utils.logger: [price optimizer] Combined compatibility: 0.55
2025-06-14 23:46:57,673:INFO:utils.logger: [quality achievement] Compatibility: 0.4429003679771454
2025-06-14 23:46:57,673:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:46:57,673:INFO:utils.logger: [quality achievement] Combined compatibility: 0.52
2025-06-14 23:46:57,674:INFO:utils.logger: [Provider provider1@localhost] Sent proposal: Service(name='A', price=10, duration=3)
2025-06-14 23:46:57,674:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:46:57,674:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "A", "price": 10, "duration": 3}, "awards": {"badges": [], "trophies": []}}
</message>

2025-06-14 23:46:57,674:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:57,674:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:57,674:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:57,674:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider1@localhost: {"service": {"name": "A", "price": 10, "duration": 3}, "awards": {"badges": [], "trophies": []}}
2025-06-14 23:46:57,674:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider2@localhost for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:57,674:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:57,674:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:57,820:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:57,820:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:57,820:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b707310>])
2025-06-14 23:46:57,820:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:57,820:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:57,820:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 5.96, "duration": 4.805444120667795}, "cost": 5.96}
</message>

2025-06-14 23:46:57,821:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:57,821:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessInform.
2025-06-14 23:46:57,821:INFO:spade.behaviour: FSM running state ProcessInform
2025-06-14 23:46:57,821:INFO:utils.logger: [Provider provider2@localhost] Processing inform
2025-06-14 23:46:57,821:INFO:utils.logger: [Provider provider2@localhost] Received payment for service A
2025-06-14 23:46:57,821:INFO:spade.behaviour: FSM transiting from ProcessInform to Idle.
2025-06-14 23:46:57,821:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:57,821:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:58,623:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:58,675:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:58,675:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:58,675:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:58,822:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:58,823:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:58,823:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7be810>])
2025-06-14 23:46:58,823:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:58,823:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:58,823:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:58,823:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:58,824:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:46:58,824:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:46:58,824:INFO:utils.logger: [Provider provider2@localhost] Processing proposal
2025-06-14 23:46:58,824:INFO:utils.logger: [competitive pricing] Compatibility: 0.7528967827000222
2025-06-14 23:46:58,824:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.62
2025-06-14 23:46:58,824:INFO:utils.logger: [competitive pricing] Applied price discount of 1.29 for provider2@localhost
2025-06-14 23:46:58,824:INFO:utils.logger: [quality focus] Compatibility: 0.7565320007338405
2025-06-14 23:46:58,825:INFO:utils.logger: [quality focus] Combined compatibility: 0.62
2025-06-14 23:46:58,825:INFO:utils.logger: [quality focus] Increased duration by 0.97 for provider2@localhost
2025-06-14 23:46:58,825:INFO:utils.logger: [risk taking] Compatibility: 0.7818792383410816
2025-06-14 23:46:58,825:INFO:utils.logger: [risk taking] Combined compatibility: 0.64
2025-06-14 23:46:58,825:INFO:utils.logger: [risk taking] Applied price variation of -0.21 for provider2@localhost
2025-06-14 23:46:58,825:INFO:utils.logger: [service milestone] Compatibility: 0.822818360620144
2025-06-14 23:46:58,825:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:58,825:INFO:utils.logger: [service milestone] Combined compatibility: 0.85
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Compatibility: 0.7713590879421037
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Combined compatibility: 0.83
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Goal achieved by provider2@localhost!
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Awarded Efficiency Badge to provider2@localhost
2025-06-14 23:46:58,826:INFO:utils.logger: [price optimizer] Efficiency bonus applied by provider2@localhost
2025-06-14 23:46:58,826:INFO:utils.logger: [quality achievement] Compatibility: 0.7989112965214936
2025-06-14 23:46:58,827:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:46:58,827:INFO:utils.logger: [quality achievement] Combined compatibility: 0.84
2025-06-14 23:46:58,827:INFO:utils.logger: [Provider provider2@localhost] Sent proposal: Service(name='A', price=np.float64(4.46), duration=np.float64(5.534640911460257))
2025-06-14 23:46:58,827:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:46:58,828:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}, "awards": {"badges": ["Efficiency Badge"], "trophies": []}}
</message>

2025-06-14 23:46:58,828:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:58,828:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:46:58,828:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:58,828:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider2@localhost: {"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}, "awards": {"badges": ["Efficiency Badge"], "trophies": []}}
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Offers: [{'provider': provider1@localhost, 'service': Service(name='A', price=10, duration=3), 'awards': {'badges': [], 'trophies': []}}, {'provider': provider2@localhost, 'service': Service(name='A', price=4.46, duration=5.534640911460257), 'awards': {'badges': ['Efficiency Badge'], 'trophies': []}}]
2025-06-14 23:46:58,829:INFO:spade.behaviour: FSM transiting from Tender to SelectBestOffer.
2025-06-14 23:46:58,829:INFO:spade.behaviour: FSM running state SelectBestOffer
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Selecting best offer for service Service(name='A', price=None, duration=None)
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Personality factors: price_sensitivity=0.93, quality_focus=0.15, badge_appreciation=0.48, risk_tolerance=0.16
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider1@localhost: price=10, duration=3, awards=0, value=9.43
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider2@localhost: price=4.46, duration=5.534640911460257, awards=1, value=-0.27
2025-06-14 23:46:58,829:INFO:utils.logger: [Consumer consumer1@localhost] Selected best offer from provider2@localhost with value -0.27
2025-06-14 23:46:58,830:INFO:utils.logger: [Consumer consumer1@localhost] Best offer: Service(name='A', price=4.46, duration=5.534640911460257) from provider2@localhost
2025-06-14 23:46:58,830:INFO:spade.behaviour: FSM transiting from SelectBestOffer to BudgetCheck.
2025-06-14 23:46:58,830:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:58,830:INFO:spade.behaviour: FSM running state BudgetCheck
2025-06-14 23:46:58,830:INFO:utils.logger: [Consumer consumer1@localhost] Checking budget for service Service(name='A', price=4.46, duration=5.534640911460257)
2025-06-14 23:46:58,830:INFO:utils.logger: [Consumer consumer1@localhost] Budget: 44.04
2025-06-14 23:46:58,830:INFO:utils.logger: [Consumer consumer1@localhost] Budget check complete. Budget OK? True
2025-06-14 23:46:58,830:INFO:spade.behaviour: FSM transiting from BudgetCheck to RequestService.
2025-06-14 23:46:58,830:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:58,830:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:46:58,831:INFO:spade.behaviour: FSM running state RequestService
2025-06-14 23:46:58,831:INFO:utils.logger: [Consumer consumer1@localhost] Requesting service Service(name='A', price=4.46, duration=5.534640911460257) from provider2@localhost
2025-06-14 23:46:58,831:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:58,831:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:46:58,831:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:58,831:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:46:59,625:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:46:59,676:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:59,676:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:59,676:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b745ff0>])
2025-06-14 23:46:59,676:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:59,676:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:59,676:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'reject proposal'}>
{"service": {"name": "A", "price": null, "duration": null}}
</message>

2025-06-14 23:46:59,677:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:46:59,677:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalReject.
2025-06-14 23:46:59,677:INFO:spade.behaviour: FSM running state ProcessProposalReject
2025-06-14 23:46:59,677:INFO:utils.logger: [Provider provider1@localhost] Processing proposal rejection
2025-06-14 23:46:59,677:INFO:spade.behaviour: FSM transiting from ProcessProposalReject to Idle.
2025-06-14 23:46:59,677:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:59,677:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:46:59,829:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:46:59,829:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:59,829:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b889310>])
2025-06-14 23:46:59,829:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:46:59,829:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:46:59,829:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:46:59,830:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:46:59,830:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalAccept.
2025-06-14 23:46:59,830:INFO:spade.behaviour: FSM running state ProcessProposalAccept
2025-06-14 23:46:59,830:INFO:utils.logger: [Provider provider2@localhost] Processing proposal acceptance
2025-06-14 23:46:59,830:INFO:spade.behaviour: FSM transiting from ProcessProposalAccept to Idle.
2025-06-14 23:46:59,830:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:46:59,830:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:00,627:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:47:00,679:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:00,679:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:00,680:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:00,831:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:00,831:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:00,831:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7c8cd0>])
2025-06-14 23:47:00,832:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:00,832:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:00,832:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:47:00,832:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:00,832:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:47:00,832:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:47:00,832:INFO:utils.logger: [Provider provider2@localhost] Processing request
2025-06-14 23:47:00,833:INFO:utils.logger: [Provider provider2@localhost] Service providing media: [Item(service providing medium, {'available': True})]
2025-06-14 23:47:00,833:INFO:utils.logger: [Provider provider2@localhost] Request approved
2025-06-14 23:47:00,833:INFO:spade.behaviour: FSM transiting from ProcessRequest to PerformServiceState.
2025-06-14 23:47:00,833:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'agree'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:47:00,833:INFO:spade.behaviour: FSM running state PerformServiceState
2025-06-14 23:47:00,833:INFO:utils.logger: [Provider provider2@localhost] Performing service
2025-06-14 23:47:00,834:INFO:utils.logger: [Provider provider2@localhost] Service complete
2025-06-14 23:47:00,834:INFO:spade.behaviour: FSM transiting from PerformServiceState to Idle.
2025-06-14 23:47:00,834:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:00,834:INFO:utils.logger: [Provider provider2@localhost] Performing service A for consumer1@localhost
2025-06-14 23:47:00,834:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:00,834:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:00,834:INFO:utils.logger: [Consumer consumer1@localhost] Request approved
2025-06-14 23:47:00,834:INFO:spade.behaviour: FSM transiting from RequestService to WaitForService.
2025-06-14 23:47:00,834:INFO:spade.behaviour: FSM running state WaitForService
2025-06-14 23:47:00,835:INFO:utils.logger: [Consumer consumer1@localhost] Waiting for service Service(name='A', price=4.46, duration=5.534640911460257) from provider2@localhost
2025-06-14 23:47:01,680:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:01,681:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:01,681:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:01,835:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:01,836:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:01,836:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:02,682:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:02,682:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:02,682:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:02,837:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:02,837:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:02,837:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:03,683:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:03,683:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:03,683:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:03,839:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:03,839:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:03,841:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:04,684:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:04,684:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:04,684:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:04,844:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:04,844:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:04,844:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:05,686:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:05,686:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:05,686:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:05,845:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:05,845:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:05,845:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:06,369:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'confirm'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}}
</message>

2025-06-14 23:47:06,369:INFO:utils.logger: [Provider provider2@localhost] Service A complete for consumer1@localhost
2025-06-14 23:47:06,370:INFO:utils.logger: [Provider provider2@localhost] Provided services: {'A': 2}
2025-06-14 23:47:06,370:INFO:spade.behaviour: Killing behavior OneShotBehaviour/PerformServiceBehaviour with exit code: None
2025-06-14 23:47:06,370:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:06,370:INFO:utils.logger: [Consumer consumer1@localhost] Service complete
2025-06-14 23:47:06,370:INFO:spade.behaviour: FSM transiting from WaitForService to ServiceComplete.
2025-06-14 23:47:06,370:INFO:spade.behaviour: FSM running state ServiceComplete
2025-06-14 23:47:06,371:INFO:utils.logger: [Consumer consumer1@localhost] Payment sent
2025-06-14 23:47:06,371:INFO:spade.behaviour: FSM transiting from ServiceComplete to Idle.
2025-06-14 23:47:06,371:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}, "cost": 4.46}
</message>

2025-06-14 23:47:06,371:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:06,371:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:47:06,372:INFO:spade.behaviour: FSM transiting from Idle to NextElement.
2025-06-14 23:47:06,372:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:06,372:INFO:spade.behaviour: FSM running state NextElement
2025-06-14 23:47:06,372:INFO:utils.logger: [Consumer consumer1@localhost] Starting with the next element in recipe Recipe.3/3: A, A, C
2025-06-14 23:47:06,372:INFO:spade.behaviour: FSM transiting from NextElement to Tender.
2025-06-14 23:47:06,372:INFO:spade.behaviour: FSM running state Tender
2025-06-14 23:47:06,373:INFO:utils.logger: [Consumer consumer1@localhost] Tendering for service Service(name='C', price=None, duration=None)
2025-06-14 23:47:06,373:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider2@localhost for service Service(name='C', price=None, duration=None)
2025-06-14 23:47:06,373:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}, "cost": 4.46}
</message>

2025-06-14 23:47:06,373:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "C", "price": null, "duration": null}}
</message>

2025-06-14 23:47:06,374:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:06,687:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:06,688:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:06,688:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:06,846:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:06,847:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:06,847:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7c3ed0>])
2025-06-14 23:47:06,847:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:06,847:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:06,847:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "A", "price": 4.46, "duration": 5.534640911460257}, "cost": 4.46}
</message>

2025-06-14 23:47:06,847:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:06,848:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessInform.
2025-06-14 23:47:06,848:INFO:spade.behaviour: FSM running state ProcessInform
2025-06-14 23:47:06,848:INFO:utils.logger: [Provider provider2@localhost] Processing inform
2025-06-14 23:47:06,848:INFO:utils.logger: [Provider provider2@localhost] Received payment for service A
2025-06-14 23:47:06,848:INFO:spade.behaviour: FSM transiting from ProcessInform to Idle.
2025-06-14 23:47:06,848:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:06,848:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:07,375:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "C", "price": null, "duration": null}}
</message>

2025-06-14 23:47:07,689:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:07,690:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:07,690:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:07,850:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:07,850:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:07,850:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7c30d0>])
2025-06-14 23:47:07,850:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:07,851:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:07,851:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "C", "price": null, "duration": null}}
</message>

2025-06-14 23:47:07,851:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:07,851:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:47:07,851:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:47:07,851:INFO:utils.logger: [Provider provider2@localhost] Processing proposal
2025-06-14 23:47:07,852:INFO:utils.logger: [competitive pricing] Compatibility: 0.7528967827000222
2025-06-14 23:47:07,852:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.62
2025-06-14 23:47:07,852:INFO:utils.logger: [competitive pricing] Applied price discount of 4.32 for provider2@localhost
2025-06-14 23:47:07,852:INFO:utils.logger: [quality focus] Compatibility: 0.7565320007338405
2025-06-14 23:47:07,852:INFO:utils.logger: [quality focus] Combined compatibility: 0.62
2025-06-14 23:47:07,852:INFO:utils.logger: [quality focus] Increased duration by 0.40 for provider2@localhost
2025-06-14 23:47:07,853:INFO:utils.logger: [risk taking] Compatibility: 0.7818792383410816
2025-06-14 23:47:07,853:INFO:utils.logger: [risk taking] Combined compatibility: 0.64
2025-06-14 23:47:07,853:INFO:utils.logger: [risk taking] Applied price variation of -0.69 for provider2@localhost
2025-06-14 23:47:07,853:INFO:utils.logger: [service milestone] Compatibility: 0.822818360620144
2025-06-14 23:47:07,853:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:47:07,853:INFO:utils.logger: [service milestone] Combined compatibility: 0.85
2025-06-14 23:47:07,853:INFO:utils.logger: [service milestone] Goal achieved by provider2@localhost!
2025-06-14 23:47:07,854:INFO:utils.logger: [service milestone] Awarded Service Trophy to provider2@localhost
2025-06-14 23:47:07,854:INFO:utils.logger: [service milestone] Celebration discount applied by provider2@localhost
2025-06-14 23:47:07,854:INFO:utils.logger: [price optimizer] Compatibility: 0.7713590879421037
2025-06-14 23:47:07,854:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:47:07,854:INFO:utils.logger: [price optimizer] Combined compatibility: 0.83
2025-06-14 23:47:07,854:INFO:utils.logger: [quality achievement] Compatibility: 0.7989112965214936
2025-06-14 23:47:07,854:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider2@localhost: 0.91
2025-06-14 23:47:07,855:INFO:utils.logger: [quality achievement] Combined compatibility: 0.84
2025-06-14 23:47:07,855:INFO:utils.logger: [Provider provider2@localhost] Sent proposal: Service(name='C', price=np.float64(13.49), duration=np.float64(2.4027220603338977))
2025-06-14 23:47:07,855:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:47:07,855:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}, "awards": {"badges": ["Efficiency Badge"], "trophies": ["Service Trophy"]}}
</message>

2025-06-14 23:47:07,855:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:07,855:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:07,855:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider2@localhost: {"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}, "awards": {"badges": ["Efficiency Badge"], "trophies": ["Service Trophy"]}}
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Offers: [{'provider': provider2@localhost, 'service': Service(name='C', price=13.49, duration=2.4027220603338977), 'awards': {'badges': ['Efficiency Badge'], 'trophies': ['Service Trophy']}}]
2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM transiting from Tender to SelectBestOffer.
2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM running state SelectBestOffer
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Selecting best offer for service Service(name='C', price=None, duration=None)
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Personality factors: price_sensitivity=0.93, quality_focus=0.15, badge_appreciation=0.48, risk_tolerance=0.16
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider2@localhost: price=13.49, duration=2.4027220603338977, awards=2, value=3.19
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Selected best offer from provider2@localhost with value 3.19
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Best offer: Service(name='C', price=13.49, duration=2.4027220603338977) from provider2@localhost
2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM transiting from SelectBestOffer to BudgetCheck.
2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM running state BudgetCheck
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Checking budget for service Service(name='C', price=13.49, duration=2.4027220603338977)
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Budget: 39.58
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Budget check complete. Budget OK? True
2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM transiting from BudgetCheck to RequestService.
2025-06-14 23:47:07,856:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:07,856:INFO:spade.behaviour: FSM running state RequestService
2025-06-14 23:47:07,856:INFO:utils.logger: [Consumer consumer1@localhost] Requesting service Service(name='C', price=13.49, duration=2.4027220603338977) from provider2@localhost
2025-06-14 23:47:07,857:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:07,857:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:07,857:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:08,376:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:08,692:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:08,692:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:08,692:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:08,856:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:08,856:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:08,856:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7e8350>])
2025-06-14 23:47:08,857:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:08,857:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:08,857:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:08,857:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:08,857:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalAccept.
2025-06-14 23:47:08,857:INFO:spade.behaviour: FSM running state ProcessProposalAccept
2025-06-14 23:47:08,857:INFO:utils.logger: [Provider provider2@localhost] Processing proposal acceptance
2025-06-14 23:47:08,858:INFO:spade.behaviour: FSM transiting from ProcessProposalAccept to Idle.
2025-06-14 23:47:08,858:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:08,858:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:09,377:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:09,693:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:09,694:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:09,694:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:09,860:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:09,860:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:09,860:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7e82d0>])
2025-06-14 23:47:09,860:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:09,860:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:09,861:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:09,861:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:09,861:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:47:09,861:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:47:09,861:INFO:utils.logger: [Provider provider2@localhost] Processing request
2025-06-14 23:47:09,861:INFO:utils.logger: [Provider provider2@localhost] Service providing media: [Item(service providing medium, {'available': True})]
2025-06-14 23:47:09,861:INFO:utils.logger: [Provider provider2@localhost] Request approved
2025-06-14 23:47:09,861:INFO:spade.behaviour: FSM transiting from ProcessRequest to PerformServiceState.
2025-06-14 23:47:09,862:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'agree'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:09,862:INFO:spade.behaviour: FSM running state PerformServiceState
2025-06-14 23:47:09,862:INFO:utils.logger: [Provider provider2@localhost] Performing service
2025-06-14 23:47:09,862:INFO:utils.logger: [Provider provider2@localhost] Service complete
2025-06-14 23:47:09,862:INFO:spade.behaviour: FSM transiting from PerformServiceState to Idle.
2025-06-14 23:47:09,862:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:09,863:INFO:utils.logger: [Provider provider2@localhost] Performing service C for consumer1@localhost
2025-06-14 23:47:09,863:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:09,863:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:09,863:INFO:utils.logger: [Consumer consumer1@localhost] Request approved
2025-06-14 23:47:09,863:INFO:spade.behaviour: FSM transiting from RequestService to WaitForService.
2025-06-14 23:47:09,863:INFO:spade.behaviour: FSM running state WaitForService
2025-06-14 23:47:09,863:INFO:utils.logger: [Consumer consumer1@localhost] Waiting for service Service(name='C', price=13.49, duration=2.4027220603338977) from provider2@localhost
2025-06-14 23:47:10,694:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:10,695:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:10,695:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:10,864:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:10,864:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:10,864:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:11,697:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:11,697:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:11,697:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:11,866:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:11,866:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:11,866:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:12,267:INFO:utils.logger: [provider2@localhost] Sent message:
<message to="consumer1@localhost" from="provider2@localhost" thread="None" metadata={'performative': 'confirm'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}}
</message>

2025-06-14 23:47:12,267:INFO:utils.logger: [Provider provider2@localhost] Service C complete for consumer1@localhost
2025-06-14 23:47:12,267:INFO:utils.logger: [Provider provider2@localhost] Provided services: {'A': 2, 'C': 1}
2025-06-14 23:47:12,267:INFO:spade.behaviour: Killing behavior OneShotBehaviour/PerformServiceBehaviour with exit code: None
2025-06-14 23:47:12,268:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,268:INFO:utils.logger: [Consumer consumer1@localhost] Service complete
2025-06-14 23:47:12,268:INFO:spade.behaviour: FSM transiting from WaitForService to ServiceComplete.
2025-06-14 23:47:12,268:INFO:spade.behaviour: FSM running state ServiceComplete
2025-06-14 23:47:12,268:INFO:utils.logger: [Consumer consumer1@localhost] Payment sent
2025-06-14 23:47:12,268:INFO:spade.behaviour: FSM transiting from ServiceComplete to Idle.
2025-06-14 23:47:12,268:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}, "cost": 13.49}
</message>

2025-06-14 23:47:12,269:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,269:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:47:12,269:INFO:spade.behaviour: FSM transiting from Idle to CompleteRecipe.
2025-06-14 23:47:12,269:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,269:INFO:spade.behaviour: FSM running state CompleteRecipe
2025-06-14 23:47:12,269:INFO:utils.logger: [Consumer consumer1@localhost] Recipe completed. New recipe: Recipe.1/5: B, D, A, D, B
2025-06-14 23:47:12,270:INFO:spade.behaviour: FSM transiting from CompleteRecipe to Idle.
2025-06-14 23:47:12,270:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,270:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:47:12,270:INFO:spade.behaviour: FSM transiting from Idle to NextElement.
2025-06-14 23:47:12,270:INFO:utils.logger: [provider2@localhost] Received message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}, "cost": 13.49}
</message>

2025-06-14 23:47:12,270:INFO:spade.behaviour: FSM running state NextElement
2025-06-14 23:47:12,270:INFO:utils.logger: [Consumer consumer1@localhost] Starting with the next element in recipe Recipe.1/5: B, D, A, D, B
2025-06-14 23:47:12,270:INFO:spade.behaviour: FSM transiting from NextElement to Tender.
2025-06-14 23:47:12,271:INFO:spade.behaviour: FSM running state Tender
2025-06-14 23:47:12,271:INFO:utils.logger: [Consumer consumer1@localhost] Tendering for service Service(name='B', price=None, duration=None)
2025-06-14 23:47:12,271:INFO:utils.logger: [Consumer consumer1@localhost] Sent query to provider1@localhost for service Service(name='B', price=None, duration=None)
2025-06-14 23:47:12,271:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "B", "price": null, "duration": null}}
</message>

2025-06-14 23:47:12,271:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,271:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "B", "price": null, "duration": null}}
</message>

2025-06-14 23:47:12,698:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:12,698:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,698:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7e8550>])
2025-06-14 23:47:12,699:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:12,699:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:12,699:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'call for proposal'}>
{"service": {"name": "B", "price": null, "duration": null}}
</message>

2025-06-14 23:47:12,699:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:47:12,699:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposal.
2025-06-14 23:47:12,699:INFO:spade.behaviour: FSM running state ProcessProposal
2025-06-14 23:47:12,699:INFO:utils.logger: [Provider provider1@localhost] Processing proposal
2025-06-14 23:47:12,700:INFO:utils.logger: [competitive pricing] Compatibility: 0.49280181388336963
2025-06-14 23:47:12,700:INFO:utils.logger: [competitive pricing] Combined compatibility: 0.43
2025-06-14 23:47:12,700:INFO:utils.logger: [quality focus] Compatibility: 0.4975062189439554
2025-06-14 23:47:12,700:INFO:utils.logger: [quality focus] Combined compatibility: 0.44
2025-06-14 23:47:12,700:INFO:utils.logger: [risk taking] Compatibility: 0.5111237375367873
2025-06-14 23:47:12,700:INFO:utils.logger: [risk taking] Combined compatibility: 0.45
2025-06-14 23:47:12,700:INFO:utils.logger: [service milestone] Compatibility: 0.45479361705864085
2025-06-14 23:47:12,700:INFO:utils.logger: [service milestone] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:47:12,701:INFO:utils.logger: [service milestone] Combined compatibility: 0.51
2025-06-14 23:47:12,701:INFO:utils.logger: [price optimizer] Compatibility: 0.4991673599561093
2025-06-14 23:47:12,701:INFO:utils.logger: [price optimizer] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:47:12,701:INFO:utils.logger: [price optimizer] Combined compatibility: 0.55
2025-06-14 23:47:12,701:INFO:utils.logger: [quality achievement] Compatibility: 0.4429003679771454
2025-06-14 23:47:12,701:INFO:utils.logger: [quality achievement] Trophy/badge compatibility for provider1@localhost: 0.63
2025-06-14 23:47:12,701:INFO:utils.logger: [quality achievement] Combined compatibility: 0.52
2025-06-14 23:47:12,701:INFO:utils.logger: [Provider provider1@localhost] Sent proposal: Service(name='B', price=15, duration=5)
2025-06-14 23:47:12,702:INFO:spade.behaviour: FSM transiting from ProcessProposal to Idle.
2025-06-14 23:47:12,702:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'propose'}>
{"service": {"name": "B", "price": 15, "duration": 5}, "awards": {"badges": [], "trophies": []}}
</message>

2025-06-14 23:47:12,702:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,702:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:12,702:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,702:INFO:utils.logger: [Consumer consumer1@localhost] Received reply from provider1@localhost: {"service": {"name": "B", "price": 15, "duration": 5}, "awards": {"badges": [], "trophies": []}}
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Offers: [{'provider': provider1@localhost, 'service': Service(name='B', price=15, duration=5), 'awards': {'badges': [], 'trophies': []}}]
2025-06-14 23:47:12,703:INFO:spade.behaviour: FSM transiting from Tender to SelectBestOffer.
2025-06-14 23:47:12,703:INFO:spade.behaviour: FSM running state SelectBestOffer
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Selecting best offer for service Service(name='B', price=None, duration=None)
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Personality factors: price_sensitivity=0.93, quality_focus=0.15, badge_appreciation=0.48, risk_tolerance=0.16
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Offer from provider1@localhost: price=15, duration=5, awards=0, value=13.83
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Selected best offer from provider1@localhost with value 13.83
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Best offer: Service(name='B', price=15, duration=5) from provider1@localhost
2025-06-14 23:47:12,703:INFO:spade.behaviour: FSM transiting from SelectBestOffer to BudgetCheck.
2025-06-14 23:47:12,703:INFO:spade.behaviour: FSM running state BudgetCheck
2025-06-14 23:47:12,703:INFO:utils.logger: [Consumer consumer1@localhost] Checking budget for service Service(name='B', price=15, duration=5)
2025-06-14 23:47:12,704:INFO:utils.logger: [Consumer consumer1@localhost] Budget: 26.089999999999996
2025-06-14 23:47:12,704:INFO:utils.logger: [Consumer consumer1@localhost] Budget check complete. Budget OK? True
2025-06-14 23:47:12,704:INFO:spade.behaviour: FSM transiting from BudgetCheck to RequestService.
2025-06-14 23:47:12,704:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:12,704:INFO:spade.behaviour: FSM running state RequestService
2025-06-14 23:47:12,704:INFO:utils.logger: [Consumer consumer1@localhost] Requesting service Service(name='B', price=15, duration=5) from provider1@localhost
2025-06-14 23:47:12,705:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,705:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:12,705:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:12,867:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:12,868:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,868:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7c2550>])
2025-06-14 23:47:12,868:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:12,868:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:12,868:INFO:utils.logger: [Provider provider2@localhost] Analysing message:
<message to="provider2@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "C", "price": 13.49, "duration": 2.4027220603338977}, "cost": 13.49}
</message>

2025-06-14 23:47:12,868:INFO:utils.logger: [Provider provider2@localhost] Message analysis complete
2025-06-14 23:47:12,869:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessInform.
2025-06-14 23:47:12,869:INFO:spade.behaviour: FSM running state ProcessInform
2025-06-14 23:47:12,869:INFO:utils.logger: [Provider provider2@localhost] Processing inform
2025-06-14 23:47:12,869:INFO:utils.logger: [Provider provider2@localhost] Received payment for service C
2025-06-14 23:47:12,869:INFO:spade.behaviour: FSM transiting from ProcessInform to Idle.
2025-06-14 23:47:12,869:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:12,869:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:13,272:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:13,703:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:13,704:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:13,704:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7e89d0>])
2025-06-14 23:47:13,704:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:13,704:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:13,704:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'accept proposal'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:13,704:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:47:13,704:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessProposalAccept.
2025-06-14 23:47:13,705:INFO:spade.behaviour: FSM running state ProcessProposalAccept
2025-06-14 23:47:13,705:INFO:utils.logger: [Provider provider1@localhost] Processing proposal acceptance
2025-06-14 23:47:13,705:INFO:spade.behaviour: FSM transiting from ProcessProposalAccept to Idle.
2025-06-14 23:47:13,705:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:13,705:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:13,870:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:13,871:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:13,871:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:14,274:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:14,706:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:14,706:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:14,706:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7e8950>])
2025-06-14 23:47:14,706:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:14,706:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:14,707:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'request'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:14,707:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:47:14,707:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessRequest.
2025-06-14 23:47:14,707:INFO:spade.behaviour: FSM running state ProcessRequest
2025-06-14 23:47:14,707:INFO:utils.logger: [Provider provider1@localhost] Processing request
2025-06-14 23:47:14,707:INFO:utils.logger: [Provider provider1@localhost] Service providing media: [Item(service providing medium, {'available': True})]
2025-06-14 23:47:14,707:INFO:utils.logger: [Provider provider1@localhost] Request approved
2025-06-14 23:47:14,707:INFO:spade.behaviour: FSM transiting from ProcessRequest to PerformServiceState.
2025-06-14 23:47:14,707:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'agree'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:14,707:INFO:spade.behaviour: FSM running state PerformServiceState
2025-06-14 23:47:14,708:INFO:utils.logger: [Provider provider1@localhost] Performing service
2025-06-14 23:47:14,708:INFO:utils.logger: [Provider provider1@localhost] Service complete
2025-06-14 23:47:14,708:INFO:spade.behaviour: FSM transiting from PerformServiceState to Idle.
2025-06-14 23:47:14,708:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:14,708:INFO:utils.logger: [Provider provider1@localhost] Performing service B for consumer1@localhost
2025-06-14 23:47:14,708:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:14,708:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:14,708:INFO:utils.logger: [Consumer consumer1@localhost] Request approved
2025-06-14 23:47:14,708:INFO:spade.behaviour: FSM transiting from RequestService to WaitForService.
2025-06-14 23:47:14,708:INFO:spade.behaviour: FSM running state WaitForService
2025-06-14 23:47:14,709:INFO:utils.logger: [Consumer consumer1@localhost] Waiting for service Service(name='B', price=15, duration=5) from provider1@localhost
2025-06-14 23:47:14,872:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:14,872:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:14,872:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:15,709:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:15,709:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:15,710:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:15,873:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:15,873:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:15,873:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:16,711:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:16,711:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:16,711:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:16,874:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:16,874:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:16,874:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:17,713:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:17,713:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:17,713:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:17,876:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:17,876:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:17,876:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:18,715:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:18,715:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:18,715:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:18,878:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:18,878:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:18,878:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:19,710:INFO:utils.logger: [provider1@localhost] Sent message:
<message to="consumer1@localhost" from="provider1@localhost" thread="None" metadata={'performative': 'confirm'}>
{"service": {"name": "B", "price": 15, "duration": 5}}
</message>

2025-06-14 23:47:19,710:INFO:utils.logger: [Provider provider1@localhost] Service B complete for consumer1@localhost
2025-06-14 23:47:19,710:INFO:utils.logger: [Provider provider1@localhost] Provided services: {'B': 1}
2025-06-14 23:47:19,711:INFO:spade.behaviour: Killing behavior OneShotBehaviour/PerformServiceBehaviour with exit code: None
2025-06-14 23:47:19,711:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:19,711:INFO:utils.logger: [Consumer consumer1@localhost] Service complete
2025-06-14 23:47:19,711:INFO:spade.behaviour: FSM transiting from WaitForService to ServiceComplete.
2025-06-14 23:47:19,711:INFO:spade.behaviour: FSM running state ServiceComplete
2025-06-14 23:47:19,711:INFO:utils.logger: [Consumer consumer1@localhost] Payment sent
2025-06-14 23:47:19,711:INFO:spade.behaviour: FSM transiting from ServiceComplete to Idle.
2025-06-14 23:47:19,712:INFO:utils.logger: [consumer1@localhost] Sent message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "B", "price": 15, "duration": 5}, "cost": 15}
</message>

2025-06-14 23:47:19,712:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:19,712:INFO:utils.logger: [Consumer consumer1@localhost] Idle.
2025-06-14 23:47:19,712:INFO:spade.behaviour: FSM transiting from Idle to NextElement.
2025-06-14 23:47:19,712:INFO:spade.behaviour: Killing behavior OneShotBehaviour/SendMessageBehaviour with exit code: None
2025-06-14 23:47:19,712:INFO:spade.behaviour: FSM running state NextElement
2025-06-14 23:47:19,712:INFO:utils.logger: [Consumer consumer1@localhost] Starting with the next element in recipe Recipe.2/5: B, D, A, D, B
2025-06-14 23:47:19,712:INFO:spade.behaviour: FSM transiting from NextElement to Tender.
2025-06-14 23:47:19,712:INFO:spade.behaviour: FSM running state Tender
2025-06-14 23:47:19,713:INFO:utils.logger: [Consumer consumer1@localhost] Tendering for service Service(name='D', price=None, duration=None)
2025-06-14 23:47:19,713:INFO:utils.logger: [Consumer consumer1@localhost] Offers: []
2025-06-14 23:47:19,713:INFO:spade.behaviour: FSM transiting from Tender to LookForNewProvider.
2025-06-14 23:47:19,713:INFO:utils.logger: [provider1@localhost] Received message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "B", "price": 15, "duration": 5}, "cost": 15}
</message>

2025-06-14 23:47:19,713:INFO:spade.behaviour: FSM running state LookForNewProvider
2025-06-14 23:47:19,713:INFO:utils.logger: [Consumer consumer1@localhost] Looking for new provider for service Service(name='D', price=None, duration=None)
2025-06-14 23:47:19,713:INFO:spade.behaviour: Killing behavior State/LookForNewProvider with exit code: None
2025-06-14 23:47:19,713:INFO:spade.behaviour: FSM arrived to a final state (no transitions found). Killing FSM.
2025-06-14 23:47:19,713:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,714:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,716:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:19,716:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:19,716:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([<spade.message.Message object at 0x74c61b7c3750>])
2025-06-14 23:47:19,716:INFO:spade.behaviour: FSM transiting from Idle to AnalyseMessage.
2025-06-14 23:47:19,716:INFO:spade.behaviour: FSM running state AnalyseMessage
2025-06-14 23:47:19,716:INFO:utils.logger: [Provider provider1@localhost] Analysing message:
<message to="provider1@localhost" from="consumer1@localhost" thread="None" metadata={'performative': 'inform'}>
{"service": {"name": "B", "price": 15, "duration": 5}, "cost": 15}
</message>

2025-06-14 23:47:19,717:INFO:utils.logger: [Provider provider1@localhost] Message analysis complete
2025-06-14 23:47:19,717:INFO:spade.behaviour: FSM transiting from AnalyseMessage to ProcessInform.
2025-06-14 23:47:19,717:INFO:spade.behaviour: FSM running state ProcessInform
2025-06-14 23:47:19,717:INFO:utils.logger: [Provider provider1@localhost] Processing inform
2025-06-14 23:47:19,717:INFO:utils.logger: [Provider provider1@localhost] Received payment for service B
2025-06-14 23:47:19,717:INFO:spade.behaviour: FSM transiting from ProcessInform to Idle.
2025-06-14 23:47:19,717:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:19,717:INFO:utils.logger: [Provider provider1@localhost] Idle, inbox: deque([])
2025-06-14 23:47:19,879:INFO:spade.behaviour: FSM transiting from Idle to Idle.
2025-06-14 23:47:19,880:INFO:spade.behaviour: FSM running state Idle
2025-06-14 23:47:19,880:INFO:utils.logger: [Provider provider2@localhost] Idle, inbox: deque([])
2025-06-14 23:47:19,944:INFO:slixmpp.xmlstream.xmlstream: connection_lost: (None,)
2025-06-14 23:47:19,945:INFO:spade.Agent: Client disconnected.
2025-06-14 23:47:19,945:INFO:spade.behaviour: Killing behavior CyclicBehaviour/ReceiveMessagesBehaviour with exit code: None
2025-06-14 23:47:19,946:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,952:INFO:slixmpp.xmlstream.xmlstream: connection_lost: (None,)
2025-06-14 23:47:19,952:INFO:spade.Agent: Client disconnected.
2025-06-14 23:47:19,953:INFO:spade.behaviour: Killing behavior CyclicBehaviour/ReceiveMessagesBehaviour with exit code: None
2025-06-14 23:47:19,953:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,956:INFO:slixmpp.xmlstream.xmlstream: connection_lost: (None,)
2025-06-14 23:47:19,957:INFO:spade.Agent: Client disconnected.
2025-06-14 23:47:19,957:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,958:INFO:spade.behaviour: Killing behavior CyclicBehaviour/ReceiveMessagesBehaviour with exit code: None
2025-06-14 23:47:19,958:INFO:spade.behaviour: Killing behavior CyclicBehaviour/FSMBehaviour with exit code: None
2025-06-14 23:47:19,958:INFO:spade.behaviour: Killing behavior CyclicBehaviour/ReceiveMessagesBehaviour with exit code: None
