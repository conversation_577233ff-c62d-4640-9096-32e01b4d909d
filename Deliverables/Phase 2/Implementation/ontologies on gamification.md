# Ontologies of Gamification: A Literature Review

## Introduction

Gamification – often defined as “the use of game design elements in non-game contexts” – has matured over the past decade as a strategy to boost engagement and motivation in various domains (education, business, health, etc.). As gamification research and practice expanded, scholars recognized the need for formal semantic models (ontologies) to standardize gamification concepts and enable intelligent systems to reason about and personalize gameful experiences. An ontology provides a shared vocabulary and structured representation of knowledge – in this case, knowledge about game elements, user attributes, motivational strategies, and their relationships – which can support interoperability, reuse of design knowledge, and data integration across gamified systems. In recent years (especially 2020–2025), there has been a surge of peer-reviewed research proposing ontologies of gamification. These ontologies span general-purpose models of the gamification domain as well as domain-specific ontologies tailored to education and other fields.

This review surveys the development of ontologies of gamification concepts, emphasizing literature from the last five years while including foundational works that underpin the field. For each identified ontology, we summarize its name and structure, domain of application, key concepts modeled, the publication in which it appeared (with citation), author information (including affiliations when available), its contribution to semantic modeling of gamification, and notes on public availability (e.g. published OWL files or repositories). A comparative summary table is provided at the end to highlight key characteristics of these ontologies.

## Foundational Ontologies for Gamification

**OntoGamif (2019)** – One of the earliest comprehensive ontologies for gamification is *OntoGamif*, introduced by Bouzidi *et al.* (2019). This work was conducted by researchers from Ecole Nationale Supérieure d’Informatique (Algeria) and ENEA (Italy), and published in *Applied Ontology*. OntoGamif (short for “Ontology of Gamification”) is a **modular ontology** covering seven sub-domains of the gamification domain. These include: (1) **Core Gamification Concepts** (generic game design elements and principles), (2) **Organizational Concepts** (context of the implementing organization), (3) **Evaluation Concepts** (metrics and assessment of gamification outcomes), (4) **Psychological Concepts** (theories of motivation, player types, user behavior), (5) **Ethical Concepts** (ethical considerations of gamification, e.g. manipulation, privacy), (6) **Risk Concepts** (potential negative side-effects or risks of gamification), and (7) **User-Related Concepts** (profiles of target users/players). OntoGamif was built by extracting definitions of gamification elements from a broad literature review, and each ontology class is annotated with its source reference. The ontology is grounded in the SUMO upper ontology to ensure alignment with higher-level concepts. A key motivation for OntoGamif was to establish an agreed-upon common representation of gamification knowledge, which can facilitate communication among stakeholders (researchers, designers, managers) and enable semantic reasoning about gamified systems. In terms of contribution, OntoGamif provides a holistic semantic model of gamification, unifying concepts from game design, psychology, and implementation practice. Notably, the authors suggest it can help managers understand employee behavior and needs through gamification’s theoretical lens, indicating its relevance to enterprise contexts (e.g. workplace gamification). OntoGamif is **publicly available**: the authors released the ontology’s OWL file and documentation via the Mendeley Data repository. This allows other researchers to reuse or extend OntoGamif in their own semantic applications. OntoGamif has since become a foundational reference, being cited in later works as a general gamification ontology (e.g. Palomino *et al.*, 2023 refer to “OntoGamif ontology: A modular ontology for the gamification domain” as prior art).

**GaTO (2019)** – Another influential early ontology is *GaTO*, proposed by Dermeval *et al.* (2019). GaTO stands for “Gamification Tutoring Ontology” and was developed by a team at Federal University of Alagoas and University of São Paulo (Brazil) in collaboration with University of Saskatchewan (Canada). Published in *Frontiers in Artificial Intelligence*, GaTO is an ontological model connecting gamification concepts with Intelligent Tutoring Systems (ITS). The motivation here was to formally integrate gamification into adaptive learning systems. GaTO encodes knowledge from both game design and educational theory, allowing an ITS to reason about which game elements to apply in which pedagogical scenarios. The ontology reuses and links multiple models: it extends an existing ITS ontology (modeling student, domain, and pedagogical components) and incorporates a gamification design taxonomy. In particular, GaTO builds on the framework by Werbach & Hunter (which categorizes game *Dynamics*, *Mechanics*, and *Components*) – for example, in GaTO’s structure, *Game Design Element* is a class with sub-types: **Dynamics** (abstract patterns like “Narrative”, “Progression”, “Relationships”, etc.), **Mechanics** (mid-level game rules or interactions like “Challenges”, “Competition”, “Feedback”, “Rewards”, etc.), and **Components** (concrete elements like “Badges”, “Points”, “Leaderboards”, etc.). GaTO also introduces the notion of **Target Behaviors** – desired learner behaviors or outcomes (e.g. participation, performance, collaboration) that gamification seeks to encourage. The ontology maps specific game design elements to these target behavior categories, grounding gamification design choices in pedagogical objectives. By formalizing such relationships, GaTO supports *automated reasoning* (e.g., an intelligent tutor can infer which game element to deploy to foster a certain behavior) and *interoperability* of gamified tutoring systems. The authors evaluated GaTO using ontology quality metrics and demonstrated a use-case of an ontology-driven authoring tool for teachers. GaTO is also **available publicly** (the authors provide an OWL file in the publication’s supplementary material) and has been a cornerstone for ontology-based gamification in education. It illustrates how linking gamification with domain-specific ontologies (like an education model) can create *theory-aware systems* that go beyond one-size-fits-all gamification.

**Note on Terminology:** It’s worth noting that early works sometimes used “ontology” in a loose sense (referring to conceptual frameworks). However, the ontologies discussed here are formal, machine-interpretable models (often implemented in OWL/RDF). The period around 2018–2019 marks the transition where gamification research started embracing Semantic Web technologies, moving from purely conceptual taxonomies to formal ontologies that could be evaluated and used by software agents.

## Ontologies for Gamification in Education

Education has been a particularly active domain for gamification, and consequently many recent ontologies focus on modeling gamification in educational contexts. These ontologies aim to support **personalized gamification** – adapting game elements to different learners – and to guide the **design of gamified learning activities** in a systematic way. Below we review major ontologies in this area.

**OntoGaCLeS (2020)** – Challco, Bittencourt, and Isotani (2020) introduced *OntoGaCLeS*, an ontology for **gamified collaborative learning sessions**. (The name “OntoGaCLeS” stands for “Ontology for Gamified Collaborative Learning Scenarios/Sessions.”) This work was part of a project at University of São Paulo and Federal University of Alagoas in Brazil, with results published in the *Artificial Intelligence in Education* conference (AIED 2020). OntoGaCLeS is designed to address motivational problems in Computer-Supported Collaborative Learning (CSCL) scripts by formally encoding relevant knowledge from game design and psychology. In a collaborative learning script (e.g. students working in groups through structured phases), lack of motivation can arise if the script is too rigid or not engaging. OntoGaCLeS tackles this by integrating **persuasive game design strategies** and **motivation theories** into the representation of the learning scenario. The ontology defines structures to align *pedagogical objectives and collaboration patterns* with *gameful motivational strategies, player roles, and game elements*. For example, a given collaborative task can be linked to a motivational affordance (like a challenge or competition) that is appropriate for the learners’ profile, and to a game element (like a badge or leaderboard) that implements that affordance. This alignment is explicitly represented in OntoGaCLeS, ensuring that every game element added to a learning activity serves a well-defined motivational purpose. The authors emphasize avoiding one-size-fits-all gamification: OntoGaCLeS enables **personalized gamification** in collaborative learning by taking into account different player (learner) types and their motivational needs. Technically, the ontology was engineered using a top-down approach and builds upon Challco’s earlier PhD work (2018) on gamification for CL. It interoperates with an existing CSCL script ontology and reuses concepts from a general gamification ontology (the authors reference using an upper model similar to GaTO’s *Gamification Design Ontology core/full*). OntoGaCLeS was validated through an empirical study: a controlled experiment showed that students in collaborative sessions gamified via the ontology had significantly higher intrinsic motivation and better learning outcomes than those in non-gamified or naively gamified sessions. This provided evidence that an ontology-driven, theory-grounded gamification approach can indeed improve engagement and achievement. OntoGaCLeS is **available online** – the authors have released it on a public repository (GitHub) – making it accessible for other researchers or developers to adopt in collaborative learning systems. Overall, OntoGaCLeS contributes a semantic framework to *design and personalize gamified collaborative activities*, bridging the gap between gamification research and collaborative learning design.

**AGE-Learn (2020)** – In the context of e-learning, Souha Bennani *et al.* (2020) proposed the *AGE-Learn* ontology, which stands for “Adaptive Gamification in E-learning”. This work (from University of Manouba and University of Sousse, Tunisia) was presented at the KES 2020 conference (*Procedia Computer Science*). AGE-Learn specifically addresses **personalized gamification in online learning platforms**. The authors note that simply adding game elements to e-learning (“one size fits all”) often fails because different learners respond to gamification differently. Thus, the goal was to model the domain knowledge needed for *adaptive gamification* – gamification that dynamically fits the learner’s profile. The AGE-Learn ontology combines concepts of the learner’s **personal traits** (e.g. personality, values, motivations), the learner’s **current state and experience** in the e-learning course, and gamification mechanics/dynamics. By incorporating learner models (such as learning styles or personality types) alongside gamification elements, the ontology allows an intelligent e-learning system to tailor the gamification strategy to each student. For example, a learner who values social interaction might be targeted with collaborative game elements, whereas a learner who prefers mastery might get level-up feedback and challenges. Bennani *et al.* explicitly state *“the objective of this paper is to propose a representation of adaptive gamification domain knowledge into an ontology”*. They named this ontology AGE-Learn and evaluated it using a criteria-based approach to ensure it met quality measures for scope, consistency, etc.. Key concepts in AGE-Learn include: **Gamification Mechanisms** (like points, badges, leaderboards, narrative, etc.), **Learner Profile** (encompassing demographics, personality – possibly using standard models like Big Five or Bartle types, prior knowledge level, etc.), **Learning Context** (course topics, difficulty, etc.), and **Adaptation Rules** that map profile features to appropriate gamification techniques. By formalizing these, AGE-Learn contributes to the semantic modeling of *why and how* certain game elements should be used for certain learners – effectively a personalization rule-base encoded in ontology form. This ontology is focused on the **education domain (e-learning)**, but its approach to personalization is broadly relevant. The authors did not explicitly mention a public repository for AGE-Learn’s OWL file in the paper; however, since it was published open-access, the ontology model is described in the text and could be reconstructed. AGE-Learn reflects the broader trend (also seen in OntoGaCLeS) of avoiding generic gamification in favor of *adaptivity*, using ontologies to encode the multiple dimensions that personalization must consider (learner needs, motivations, etc.).

**SPOnto (2020)** – Another ontology centered on learner modeling is *SPOnto*, developed by Missaoui, Bennani, and Maalel (2020). The acronym stands for “Student Profile Ontology.” This work (from University of Sousse and Manouba, Tunisia – note the overlap in team with AGE-Learn) was published at the KEOD 2020 conference (Knowledge Engineering and Ontology Development, part of IC3K 2020). SPOnto’s aim is to provide a **global semantic model of a student’s profile** that can be used to personalize gamified learning systems. The authors point out that many adaptive learning systems fail to account for motivation and engagement, and that integrating gamification with adaptive learning could be a solution. SPOnto therefore combines the two concepts: it models all the important **learner characteristics** for adaptation (learning styles, preferences, competences, cognitive abilities, etc.) *and* links these to **gamification aspects** that can improve motivation. The ontology likely includes classes such as *Student* (with properties like hasLearningStyle, hasSkillLevel, hasPersonalityType), and classes for *Game Element* or *Gamification Technique*, as well as *Learning Activity*. The idea is that using SPOnto, a system can reason about which game element would best motivate a particular student given their profile. In their paper, Missaoui *et al.* describe SPOnto as *“an ontology for representation of student profile, by combining the two concepts ‘adaptive learning’ and ‘gamification’ to provide a personalized gamified experience”*. This ontology contributes to semantic modeling by focusing on the **user profile** side of gamification – complementing ontologies like OntoGamif or GaTO which focus more on game elements and design. By formally representing learner attributes and linking them to gamification decisions, SPOnto helps bridge the gap between user modeling and gamification frameworks. The authors intended for SPOnto to support decision-making in academic contexts (e.g., an educator or an adaptive system can query the ontology to decide how to gamify a course for a certain student). While primarily an educational ontology, SPOnto’s approach to modeling user traits could inform gamification in other domains as well (since understanding the “player” is crucial everywhere). The publication does not explicitly mention if SPOnto is available on a repository; it may be accessible by contacting the authors or from the conference proceedings. Nonetheless, SPOnto is an important piece in the puzzle of gamification ontologies, emphasizing *semantic user modeling for personalized gamification*.

**Ontology for Gamified Education (Palomino et al. 2023)** – A very recent contribution to this field is the ontology presented by Palomino *et al.* (2023), published in *Research and Practice in Technology Enhanced Learning*. This work is a collaboration between researchers from Federal University of Alagoas (Brazil), University of São Paulo (Brazil), and University of Waterloo (Canada). While the authors did not give a specific name to the ontology, it is essentially a **lightweight application ontology for personalized gamification in education**. Palomino *et al.* seek to connect three components in one model: (1) a **classification of user types** based on psychology, (2) a **taxonomy of game elements** suitable for education, and (3) **educational learning activity types** (drawn from an instructional design framework). Specifically, they adopt **Jung’s 12 archetypes** (a set of broad personality archetypes like *Hero*, *Caregiver*, *Rebel*, etc.) as a basis for defining gamification user types, an approach earlier proposed by the same research group to address limitations in gamer type models. These archetypes are linked in the ontology to the **Hexad user types** or other gamification-relevant motivational traits, and also correlated with the Big Five personality traits. Next, the ontology includes a list of **game elements and mechanics** (21 game elements are mentioned as included, likely things such as points, badges, leaderboards, narrative, challenges, etc., organized into higher-level categories or “dimensions”). Finally, it incorporates **Bloom’s revised taxonomy of educational objectives** (which classifies learning activities by cognitive level: Remember, Understand, Apply, Analyze, Evaluate, Create). By relating these three facets – student archetype, game element, and learning objective – the ontology enables a structured way to design gamified learning experiences. As the authors state, their ontology *“seeks to represent relationships between Jung’s archetypes (e.g., the Hero, the Outlaw, etc.) adapted for educational purposes, a taxonomy of game elements specific for use in educational contexts, and Bloom’s revised taxonomy to classify learning activity types”*. The contribution is that **personalized gamification designs** can be derived by tracing these relationships. For example, if a student is identified as the “Explorer” archetype (to use a generic term), and the learning task is at the “Analyze” level of Bloom’s taxonomy, the ontology might suggest certain game elements that are effective for that archetype in analytical tasks (perhaps exploratory challenges or puzzle-solving mechanics). The authors demonstrate that using this ontology leads to better gamification designs that *directly tie the type of learning activity to suitable gameful strategies for that learner type*, thereby improving both engagement and instructional alignment. They envision the ontology being usable by teachers or learning designers (even in “unplugged” non-software contexts) to plan class activities, as well as by software systems to adapt gamified e-learning in real-time. The ontology was evaluated through expert review and case studies included in the paper. Palomino *et al.* have made a **conceptual model diagram** of the ontology available as supplemental material (via OSF), and an OWL version was developed for use in tools (though it’s not yet linked to higher-level ontologies, as they note in limitations). This 2023 ontology is notable for integrating **user modeling, pedagogy, and gamification elements in one semantic structure**, reflecting a state-of-the-art approach to personalized gamification. It builds on prior ontologies like OntoGamif (which it cites), but focuses on the educational domain and the specific challenge of mapping game design to instructional design.

## Ontologies in Other Domains and Emerging Work

While education has led the way in ontology-based gamification research, other domains are beginning to see similar efforts. One example is in the area of **assessment and serious games**:

**Game-Based Assessment Ontology (2024)** – Gómez *et al.* (2024) present an ontology-driven framework for **game-based assessments (GBAs)**, i.e. using serious games to assess skills and knowledge. The team (University of Murcia, Spain) published their work in *Expert Systems with Applications*. In their framework, a “common GBA ontology” is a central component that allows different educational games and assessment analytics to interoperate. The ontology standardizes concepts like **game events**, **player actions**, **assessment metrics**, and **competencies** being measured across different serious games. By having a shared ontology, data from various game-based assessments can be aggregated and interpreted in a unified way. Gómez *et al.* describe their contribution thus: *“we introduce a novel approach to develop interoperable GBAs by: (1) designing and creating an ontology that can standardize the GBA area; (2) ... conducting validation studies; (3) ... case study in a real scenario. Our results confirm that the designed ontology can be used to effectively perform GBAs... solving current limitations regarding GBA interoperability”*. This highlights the ontology’s role in enabling **interoperability** – a key benefit of semantic modeling – in this case allowing a “Game-Based Assessment as a Service (GBAaaS)” architecture. While not purely a “gamification ontology” (it is more about serious games used for assessment), this work overlaps with gamification since it deals with applying game elements for a non-entertainment purpose (assessment). It shows the expanding use of ontologies to formalize game-based **competence models** and **analytics**. The ontology is likely available through the authors’ project page or supplemental files, given their emphasis on standardization (and the authors have a history of sharing tools on GitHub). This example underscores that beyond education, *other fields like training and evaluation are adopting ontology-based gamification approaches* to ensure that data and interventions are comparable across systems.

**Other Domains:** In business, marketing, and health care, gamification is popular, but we found fewer published ontologies specific to those domains. It appears that the concept of a general gamification ontology (like OntoGamif) can be applied to any domain, and domain-specific details (like “patient” vs “student” as user, or specific business objectives) could extend from that. For instance, OntoGamif’s inclusion of “organizational” and “user” subdomains suggests it can be adapted to workplace or consumer gamification scenarios. Some recent health gamification studies mention using ontologies or knowledge-based approaches (e.g., to integrate expert knowledge in personalized health games), but concrete ontologies analogous to the above were not prominent in the literature. It is an open opportunity for future research to create, for example, a **Gamification Ontology for Health/Wellness** or for **Marketing Engagement**, building on the foundations laid in education.

Finally, methodology-focused work should be noted: Researchers like Bandeira *et al.* (2016) developed **FOCA**, a methodology for evaluating ontologies in the context of gamification. Such methods have been employed in the above works to ensure the ontologies meet quality criteria (coverage, consistency, clarity, etc.). The interplay of ontology engineering and gamification design is a developing area, and we see cross-pollination with the Semantic Web community in terms of best practices for ontology development.

## Comparison of Gamification Ontologies

The table below summarizes key characteristics of the ontologies discussed in this review. It highlights the year and source of publication, the domain or application focus, the main concepts modeled, and availability. (All are peer-reviewed works; citations refer to the publications where the ontologies were introduced.)

| **Ontology (Year)**                   | **Domain/Application**                   | **Key Concepts & Structure**                                                                                                                                                                                                                                                                                                                                                                      | **Publication (Authors)**                                                                                                    | **Availability**                                                                                             |
| ------------------------------------- | ---------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| **OntoGamif (2019)**                  | General Gamification (cross-domain)      | Modular ontology with 7 sub-domains: core game concepts, user types, organizational context, evaluation metrics, psychological theories, ethical issues, risks. Classes annotated with definitions from literature; aligned with SUMO upper ontology for integration.                                                                                                                             | *Applied Ontology* (Bouzidi *et al.*) – Team from ENSI Algiers & ENEA Italy.                                                 | **Yes:** OWL file and documentation on Mendeley Data (CC BY 4.0).                                            |
| **GaTO (2019)**                       | Intelligent Tutoring Systems (Education) | Ontology connecting gamification to tutoring system models. Defines game design element taxonomy (Dynamics, Mechanics, Components) and links to educational constructs (target behaviors, student model, domain model). Enables reasoning about which game elements to use to elicit desired learning behaviors.                                                                                  | *Frontiers in AI* (Dermeval *et al.*) – Federal Univ. of Alagoas & Univ. São Paulo (Brazil), Univ. of Saskatchewan (Canada). | **Yes:** Included in article supplement (OWL available).                                                     |
| **OntoGaCLeS (2020)**                 | Collaborative Learning (Education)       | Ontology for gamified CSCL scripts. Encodes motivational strategies (persuasive game design, player roles, etc.) and maps them to collaborative learning activities and goals. Structures align game elements with pedagogical objectives and collaboration patterns for *well-thought-out*, personalized gamification.                                                                           | *AIED 2020 (LNCS)* (Challco *et al.*) – Univ. São Paulo & Fed. Univ. Alagoas (Brazil).                                       | **Yes:** Ontology file released (GitHub pages).                                                              |
| **AGE-Learn (2020)**                  | E-learning Personalization (Education)   | Adaptive gamification ontology. Models learner profile (traits: personality, needs, motivation), learning context, and gamification mechanics. Supports tailoring of gamification to individual learners in online courses. Essentially a semantic framework combining adaptive learning and gamification for personalized e-learning.                                                            | *KES 2020 / Procedia CS* (Bennani *et al.*) – Univ. of Manouba & Sousse (Tunisia).                                           | *Not explicitly stated* (described in paper; likely obtainable from authors).                                |
| **SPOnto (2020)**                     | Student Profile Modeling (Education)     | Student Profile Ontology for gamified learning. Represents learner characteristics (preferences, abilities, behaviors, etc.) and links them with gamification elements for motivation. Integrates adaptive learning concepts with gamification to enable personalized gamified experiences. Focus on user modeling: a comprehensive view of the learner to inform gamification decisions.         | *KEOD 2020 (IC3K)* (Missaoui *et al.*) – Univ. of Sousse & Manouba (Tunisia).                                                | *Not stated* (ontology described conceptually in publication).                                               |
| **Palomino et al.’s Ontology (2023)** | Gamification in Educational Design       | Lightweight application ontology connecting **Jung’s 12 archetype user types**, a taxonomy of game elements (21 elements categorized by their gameful dimensions), and Bloom’s taxonomy of learning objectives. Allows mapping of learner archetype → suitable game element → type of learning activity. Aids in designing personalized gamification strategies aligned with instructional goals. | *RPTEL Journal 2023* (Palomino *et al.*) – Fed. Univ. of Alagoas (Brazil) & Univ. of Waterloo (Canada).                      | **Partial:** Conceptual model diagram and OWL file shared as supplementary (via OSF).                        |
| **GBA Ontology (2024)**               | Game-Based Assessments (Serious Games)   | Ontology to standardize **serious game-based assessment** concepts. Models common structures for game telemetry, player actions, competencies, and metrics, enabling interoperability across different game assessments. Used to implement “GBA as a Service” by providing a unified data/knowledge model for assessment games.                                                                   | *Expert Syst. Appl.* (Gómez *et al.* 2024) – Univ. of Murcia (Spain).                                                        | **Yes:** (Expected) – Framework is open-source; ontology likely on authors’ repository (mentioned in paper). |

**Table:** Summary of key ontologies of gamification concepts (2020–2025, with foundational 2019 works). Each ontology’s focus, scope of concepts, and availability are noted.

## Conclusion and Future Directions

Ontologies of gamification have rapidly developed in the last few years, reflecting a growing scholarly effort to bring semantic rigor to a field that was once driven largely by informal frameworks and ad-hoc designs. The reviewed ontologies show a clear trend toward **integrating interdisciplinary knowledge**: psychological theories of motivation, educational taxonomies, user modeling, and game design principles are all being woven into formal ontological structures. This integration is enabling more personalized and effective gamification. For example, by using ontologies like OntoGamif or Palomino et al.’s model, designers and systems can ensure that a “badge” or “quest” introduced into a system is not just a gimmick, but is semantically tied to a user’s goals and the system’s context – thereby likely increasing its effectiveness and avoiding misapplication.

Several contributions stand out: OntoGamif gave the field a broad ontology that can be a foundation for any domain’s gamification; GaTO, OntoGaCLeS, and others demonstrated the power of domain-specific ontologies in education to actually improve learning outcomes when evaluated in user studies. The education focus makes sense – learning environments provide rich, data-driven testbeds for gamification – but we expect to see similar ontology-driven approaches expand into business (customer engagement, employee training), health (patient adherence games), and beyond. Indeed, the Game-Based Assessment ontology by Gómez *et al.* is an example in the training/assessment realm, pointing to the applicability in any scenario where games intersect with serious purposes.

One challenge noted by researchers is **ontology integration**. For instance, Palomino *et al.* (2023) describe their ontology as not yet linked to higher-level ontologies. Future work could connect these gamification ontologies with well-established upper ontologies or domain ontologies (e.g., link a gamification ontology with a healthcare ontology for a health gamification application, or with business process ontologies for enterprise gamification). This would further increase interoperability and enable cross-domain reasoning (imagine a system that can leverage a common gamification vocabulary across education, health, and marketing applications).

Another consideration is tooling and adoption: the existence of an ontology alone does not guarantee it will be used. Efforts like FOCA help ensure quality, but broader adoption may require building **knowledge graphs and APIs** on top of these ontologies to make them accessible to non-expert developers and practitioners. Some projects, like the GBAaaS framework, are already moving in this direction by offering services powered by ontologies.

In summary, the development of gamification ontologies has progressed from foundational modeling of game elements and principles, to sophisticated domain-specific frameworks that demonstrably enhance personalized user experiences. These ontologies contribute to the **semantic modeling of gamification** by providing formalized representations of *who* the user is, *what* game mechanisms are available, *why* they work (theoretical rationale), and *how* they relate to the user’s context and objectives. The literature from 2020–2025 shows an increasingly comprehensive handling of these questions. With many ontologies being openly available, the stage is set for the community to refine and perhaps converge these models (for example, aligning the taxonomies of game elements used by different ontologies, or standardizing on certain user type models).

The use of ontologies also opens up exciting possibilities in **research**: scholars can more easily compare gamification techniques across studies if they annotate their experiments using a shared ontology (as suggested by OntoGamif’s literature-derived classes). It also aids **AI-driven personalization**: an intelligent tutoring system or a gamified app can query an ontology to decide the best gamification strategy for a new user, rather than relying purely on trial-and-error.

In conclusion, ontologies of gamification concepts represent an important evolution of the field – moving it toward a more theory-grounded, systematic discipline. The peer-reviewed works discussed here lay a strong foundation. Going forward, we anticipate more cross-domain ontologies, community-driven ontology repositories (perhaps inclusion of gamification ontologies in libraries like the Linked Open Vocabularies (LOV) or BioPortal for broader reuse), and continued validation of these models in real-world deployments. The convergence of gamification and semantic technologies is still in its early stages, but the progress in the last five years suggests a rich avenue for both researchers and practitioners to explore.

**Sources:** The information in this review was synthesized from multiple peer-reviewed publications that introduced or utilized gamification ontologies, including Bouzidi *et al.* (2019), Dermeval *et al.* (2019), Challco *et al.* (2020), Bennani *et al.* (2020), Missaoui *et al.* (2020), Palomino *et al.* (2023), and Gómez *et al.* (2024), among others as cited throughout. Each citation corresponds to a specific excerpt from the original source, providing evidence and details for the points discussed.
