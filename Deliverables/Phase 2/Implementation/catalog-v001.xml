<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<catalog prefer="public" xmlns="urn:oasis:names:tc:entity:xmlns:xml:catalog">
    <uri id="User Entered Import Resolution" name="http://autosemanticgame.eu/ontologies/cgo#" uri="file:/home/<USER>/Documents/Projekti%20FOI/MAGO/Deliverables/Phase%202/Related%20ontologies/Core%20Game%20Ontology.rdf"/>
    <group id="Folder Repository, directory=, recursive=true, Auto-Update=true, version=2" prefer="public" xml:base="">
        <uri id="Automatically generated entry, Timestamp=1750804000862" name="http://www.semanticweb.org/bogdan/ontologies/2025/5/untitled-ontology-91/" uri="MAGO-Ga.rdf"/>
        <uri id="Automatically generated entry, Timestamp=1750804000862" name="duplicate:https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf" uri="GIVEn.rdf"/>
        <uri id="Automatically generated entry, Timestamp=1750804000862" name="duplicate:https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/GIVEn.rdf" uri="GIVEn%20copy%202.rdf"/>
        <uri id="Automatically generated entry, Timestamp=1750804000862" name="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%202/Implementation/MAGO-Ga.owx" uri="MAGO-Ga.owx"/>
    </group>
</catalog>
