# Extensions to the GIVEn Ontology for Gamification, Personality, and Agents

To enrich the **Gamified Intelligent Virtual Environment Ontology (GIVEn)**, we propose three extension modules addressing: **(1)** gamification technique concepts (mechanics, dynamics, components), **(2)** user personality modeling via the Big Five traits, and **(3)** artificial agent modeling (roles, goals, autonomy, decision-making, and interactions with gamification elements). Each extension is grounded in concepts from OntoGamif and GaTO, ensuring coherence with GIVEn’s existing classes and alignment with standard ontologies (FOAF, DOLCE, PROV-O, etc.) where appropriate. Below, we describe each extension’s rationale and provide illustrative OWL/RDF snippets using GIVEn’s namespace (`cgo`) and relevant prefixes.

## Gamification Technique Concepts: Mechanics, Dynamics, and Components

**Narrative:** Gamification design uses *game elements* (borrowed from game design) to motivate and engage users. OntoGamif’s *core gamification* ontology identifies **gamification design elements** that specialize into **mechanics**, **dynamics**, and **aesthetics**. Here we focus on mechanics, dynamics, and the concrete components, following frameworks like <PERSON><PERSON><PERSON> & Hunter (2012) which categorize gamification elements into these three levels. Modeling these explicitly allows GIVEn to represent *what* game-inspired techniques are used, *how* they work, and *the effects on user behavior*.

* **Gamification Mechanics:** These are the *functional rules or gameplay constructs* that drive user actions. Examples include point systems, leaderboards, levels, challenges – all of which provide control mechanisms and reward structures to influence behavior. In an ontology, a `GamificationMechanic` class represents such rule-based elements. We align this concept with the idea of a “rule” or norm in a multi-agent environment, since mechanics define constraints and incentives (in GIVEn, the existing class `GameDesign` is similarly a set of game rules subclassing a Norm). Thus, `cgo:GamificationMechanic` can be introduced as a subclass of the MAS ontology’s Norm concept to ensure semantic consistency.

* **Gamification Dynamics:** Dynamics are the *emergent reactions or behavioral outcomes* that result from applying mechanics. For instance, a mechanic like a leaderboard may create **competition** among players – competition is a dynamic, i.e. a user reaction or social interaction pattern. We model `cgo:GamificationDynamic` as a class capturing these response-pattern concepts (e.g. competition, cooperation, collaboration, altruism). Dynamics often correspond to psychological or social phenomena, so they can be linked to user motivation models (see the personality extension) or to DOLCE upper categories (e.g. consider dynamics as processes or states resulting from mechanics).

* **Gamification Components:** Components are the *concrete instantiations* of mechanics – the specific interface elements or rewards used in a system. Examples: badges, points, leaderboards, levels, avatars, trophies, etc. While OntoGamif groups many of these under “mechanics”, we include a distinct `GamificationComponent` class to represent tangible game elements. Each component implements or realizes one or more mechanics (e.g. a **Badge** component implements a reward mechanic; a **Leaderboard** implements a ranking mechanic). Defining components allows granular description of gamification systems. These components can often be aligned with existing game concepts in GIVEn’s imported Video Game Ontology (for example, `vgo:Achievement` in GIVEn represents in-game rewards and could be mapped to certain gamification reward components like badges or trophies).

**Relevance & Justification:** Incorporating mechanics, dynamics, and components gives GIVEn the vocabulary to describe *gamification techniques* in any context (education, business, etc.), not just video games. This extension builds on OntoGamif’s foundational concepts, enabling GIVEn to answer **“Which gamification elements are used, and why?”** A well-defined hierarchy (design element → mechanic/dynamic/component) supports reasoning; e.g., one could query for all mechanics applied in a system or infer dynamics from given mechanics. By aligning with standard ontologies, we ensure interoperability: for instance, treating a gamification mechanic as a kind of rule aligns with upper ontology notions of *norms* or *plans*, and concrete components (badges, points) can be linked to FOAF/Dublin Core if needed (e.g. a badge might be modeled as a foaf\:Document image awarded to a user). Below is an OWL snippet illustrating these classes and relationships:

```turtle
@prefix cgo: <http://autosemanticgame.eu/ontologies/cgo#> .
@prefix mas: <http://personales.upv.es/ccarrasc/ooooaflsmas#> .  # MAS ontology (for Norm, Role, etc.)

cgo:GamificationDesignElement a owl:Class ;
    rdfs:label "Gamification Design Element"@en ;
    rdfs:comment "Any game-inspired element (pattern, object, principle, model, or method) used in a gamification design:contentReference[oaicite:18]{index=18}."@en .

cgo:GamificationMechanic a owl:Class ;
    rdfs:subClassOf cgo:GamificationDesignElement , mas:Norm ;
    rdfs:label "Gamification Mechanic"@en ;
    rdfs:comment "A rule or functional component that provides actions and control mechanisms in a gamified system (e.g. point system, leaderboard, levels, challenge):contentReference[oaicite:19]{index=19}."@en .

cgo:GamificationDynamic a owl:Class ;
    rdfs:subClassOf cgo:GamificationDesignElement ;
    rdfs:label "Gamification Dynamic"@en ;
    rdfs:comment "An emergent reaction or behavior of users in response to implemented mechanics (e.g. competition among players is a dynamic outcome):contentReference[oaicite:20]{index=20}."@en .

cgo:GamificationComponent a owl:Class ;
    rdfs:subClassOf cgo:GamificationDesignElement ;
    rdfs:label "Gamification Component"@en ;
    rdfs:comment "A concrete gamification element or resource that implements a mechanic (e.g. a Badge, Point, Leaderboard as specific game elements):contentReference[oaicite:21]{index=21}."@en .

# Relationship: a component realizes a mechanic
cgo:realizesMechanic a owl:ObjectProperty ;
    rdfs:domain cgo:GamificationComponent ;
    rdfs:range cgo:GamificationMechanic ;
    rdfs:label "realizes mechanic"@en ;
    rdfs:comment "Links a concrete gamification component to the abstract mechanic(s) it implements or instantiates."@en .
```

*(The prefix `mas:` here denotes the imported multi-agent system ontology used in GIVEn, which provides the class `mas:Norm` that we reuse for mechanics. In practice, specific components like `Badge` or `Point` could be added as subclasses of `GamificationComponent`, potentially reusing classes like `vgo:Achievement` for alignment where appropriate.)*

## Personality Modeling Extension: Big Five Traits Integration

**Narrative:** Personalizing gamified experiences requires modeling user personality. A widely adopted model is the **Big Five** (OCEAN) traits: **Openness**, **Conscientiousness**, **Extraversion**, **Agreeableness**, and **Neuroticism**. These five dimensions capture relatively stable personality differences and have been used in gamification research to tailor design elements to user preferences. For example, an extraverted user might prefer competitive or social mechanics, whereas a neurotic (high-anxiety) user might respond better to cooperative, low-pressure dynamics. By representing the Big Five in the ontology, GIVEn can link gamification elements to user profiles for adaptive or personalized gamified systems.

We introduce a class `cgo:PersonalityTrait` with five specific trait subclasses. Each trait represents one Big Five factor; a user (modeled as a FOAF person/agent) can be associated with these traits, ideally with a quantitative value or level for each. We align this model with upper ontologies by treating traits as *qualities* of a person (in DOLCE/DUL, a **Quality** is an inherent property of an entity). This means each Big Five trait can be seen as a quality dimension along which a person has a value. In implementation, one might represent a person’s trait levels via data properties (e.g., `hasOpennessScore`) or as individuals of a `PersonalityTrait` value class. For simplicity, we show the class structure and a generic linking property:

* **Openness to Experience:** Trait reflecting imagination, curiosity, and openness to new ideas.
* **Conscientiousness:** Trait reflecting organization, dependability, and goal-directed behavior.
* **Extraversion:** Trait reflecting sociability, assertiveness, and energy level.
* **Agreeableness:** Trait reflecting compassion, cooperativeness, and trust in others.
* **Neuroticism:** Trait reflecting tendency to experience negative emotions (emotional instability).

Each of these five is a subclass of `PersonalityTrait`. We reuse **FOAF** to integrate with existing classes: GIVEn already uses `foaf:Person`/`foaf:Agent` for people and agents (e.g., game players). We can say a `foaf:Person` (or `vgo:Player` in game context) **has** certain personality traits. In OWL, this could be via an object property linking a person to a trait instance or via data properties storing trait scores. We will use a simple object property here (`hasPersonalityTrait`) to illustrate the connection, understanding that in a full implementation this may be refined to include trait magnitude.

**Justification:** Integrating the Big Five provides a *standard vocabulary for user modeling*. It complements gamification elements by enabling **adaptive gamification** – systems can select or adjust mechanics based on a user’s trait profile (as suggested in recent gamification literature). This extension aligns with established vocabularies: FOAF (for person), and upper ontologies like DOLCE (treating traits as qualities of agents). No widely accepted ontology for Big Five existed in GIVEn, so we extend it, ensuring to reference known definitions (e.g., trait names per psychology standards) and keeping the schema flexible for different personality assessment data. Below is an OWL/RDF snippet for the Big Five trait classes and their usage:

```turtle
@prefix cgo: <http://autosemanticgame.eu/ontologies/cgo#> .
@prefix foaf: <http://xmlns.com/foaf/0.1/> .
@prefix dul: <http://www.ontologydesignpatterns.org/ont/dul/DUL.owl#> .  # DOLCE Ultra Light

cgo:PersonalityTrait a owl:Class ;
    rdfs:subClassOf dul:Quality ;
    rdfs:label "Personality Trait"@en ;
    rdfs:comment "A personality-related quality or dimension of an agent (modeled here using the Big Five traits)."@en .

cgo:Openness a owl:Class ; rdfs:subClassOf cgo:PersonalityTrait ;
    rdfs:label "Openness (Big Five Trait)"@en ;
    rdfs:comment "Openness to experience – creativity and openness to new ideas:contentReference[oaicite:29]{index=29}."@en .

cgo:Conscientiousness a owl:Class ; rdfs:subClassOf cgo:PersonalityTrait ;
    rdfs:label "Conscientiousness (Big Five Trait)"@en ;
    rdfs:comment "Conscientiousness – tendency to be organized, responsible, and goal-oriented:contentReference[oaicite:30]{index=30}."@en .

cgo:Extraversion a owl:Class ; rdfs:subClassOf cgo:PersonalityTrait ;
    rdfs:label "Extraversion (Big Five Trait)"@en ;
    rdfs:comment "Extraversion – sociability and assertiveness in interaction:contentReference[oaicite:31]{index=31}."@en .

cgo:Agreeableness a owl:Class ; rdfs:subClassOf cgo:PersonalityTrait ;
    rdfs:label "Agreeableness (Big Five Trait)"@en ;
    rdfs:comment "Agreeableness – compassion, cooperativeness, and trustworthiness:contentReference[oaicite:32]{index=32}."@en .

cgo:Neuroticism a owl:Class ; rdfs:subClassOf cgo:PersonalityTrait ;
    rdfs:label "Neuroticism (Big Five Trait)"@en ;
    rdfs:comment "Neuroticism – tendency toward anxiety, emotional instability:contentReference[oaicite:33]{index=33}."@en .

# Link a person/agent to a trait (this can be refined to include trait value)
cgo:hasPersonalityTrait a owl:ObjectProperty ;
    rdfs:domain foaf:Person ;  # (or foaf:Agent / vgo:Player as applicable)
    rdfs:range cgo:PersonalityTrait ;
    rdfs:label "has personality trait"@en ;
    rdfs:comment "Indicates an association between a person and a personality trait (Big Five). Actual trait level or score can be further specified via data properties."@en .
```

*In practice, each person could have multiple `hasPersonalityTrait` links (one for each trait) along with a numeric score. The Big Five trait classes here can be aligned with psychological trait vocabularies or mapped to standard upper ontology classes (we treat them as qualities per DOLCE). This extension connects to gamification by allowing, for example, rules like “if a user is high in Openness, prefer exploratory game mechanics.”*

## Artificial Agent Modeling Extension: Roles, Goals, Autonomy, and Interactions

**Narrative:** GIVEn already encompasses multi-agent system concepts (via JaCalIVE and an organizational ontology), but we extend it to richly model **artificial agents** in gamified environments. This includes defining agent **roles**, representing agent **goals** and **autonomy**, describing decision-making processes, and linking agents to gamification elements they interact with. These additions enable describing intelligent non-player characters, autonomous tutors, or other AI-driven agents that co-exist with human players.

Key concepts introduced or highlighted:

* **Agent Roles:** In a gamified environment, agents (human or artificial) can play specific roles. OntoGamif’s user ontology, for example, identifies roles like *Spectator* (a user with a passive role in the system). We generalize this with an `AgentRole` concept (building on the imported `mas:Role` in GIVEn’s MAS ontology) to categorize an agent’s function. Roles might include *Player*, *Moderator*, *Tutor*, *NPC* (non-player character), etc. For instance, a *GamificationDesigner* role could denote an agent (perhaps an AI or a user) responsible for adjusting game mechanics, whereas a *Player* role denotes those interacting with the game for rewards. Modeling roles allows constraint definitions (e.g., only agents in the “Tutor” role can administer educational content). We reuse the existing `hasRole` property from the MAS ontology (already present in GIVEn) to assign roles to agents, rather than duplicating it.

* **Agent Goals:** Artificial agents are typically goal-driven. We add `cgo:AgentGoal` to represent an objective or task an agent pursues (e.g., an NPC’s goal to guide the player, or an agent’s goal to maximize points). Goals can be linked to gamification elements – for example, an agent’s goal might be to trigger a certain dynamic (like increase player collaboration), or simply a traditional AI goal (reach a location, achieve a score). In ontological terms, goals are often modeled as intangible entities or descriptions (akin to **plans or intentions** in BDI agent models). We ensure `AgentGoal` is coherent with upper ontologies by treating it as a non-physical concept (it could be aligned to **SUMO’s Goal** concept or a planning ontology). Each agent can have one or more goals via a property (e.g., `hasGoal`). This ties into gamification: a *Gamified* agent might have goals like “keep player engagement high,” which relate to gamification objectives.

* **Autonomy & Decision-Making:** Autonomy refers to an agent’s ability to act without direct human input. We introduce `cgo:ArtificialAgent` (a subclass of `foaf:Agent`) to specifically denote *non-human autonomous agents* within the environment (distinct from `foaf:Person`). This class covers AI-driven characters or bots. We can further characterize autonomy via attributes (e.g., a data property `hasAutonomyLevel` with values like full/semi/manual). Decision-making can be modeled by linking an agent to a strategy or algorithm class. For example, one could create classes for decision models (BDI reasoning, reactive policy, etc.) and a property `usesDecisionMechanism`. Due to scope, we assume a generic approach: an autonomous agent is capable of making decisions toward its goals. If needed, this can align with PROV-O or a process ontology: an agent’s decision could be seen as a *prov\:Activity* it initiates. (While we won’t create a detailed decision-making ontology here, we note that **PROV-O** could be used to document agent actions and their outcomes as events for traceability).

* **Interaction with Gamification Elements:** We create an explicit link between agents and gamification design elements. An agent (whether a human player or an AI agent) can **engage with** or be **affected by** gamification components and mechanics. For instance, an agent might *use* a component (e.g., an AI agent awards a badge to a user), or an agent might be *targeted by* a mechanic (e.g., a leveling-up mechanic applies to a player agent). To support this, we add a property `cgo:interactsWithElement` (domain `foaf:Agent`, range `cgo:GamificationDesignElement`). This generic relation can encompass various interactions: consuming, triggering, or participating in gamified elements. We can refine it into sub-properties if needed (like `earnsBadge`, `triggersEvent`, etc.), but at the core, it connects the **Agent** and **Gamification** extensions. For example, if a player (foaf\:Person) unlocks a badge, we could represent that as the player `interactsWithElement` some `Badge` individual, possibly alongside a PROV-O statement that an *AchievementUnlockEvent* (prov\:Activity) was involved.

**Justification:** These agent-oriented extensions ensure GIVEn can represent intelligent virtual agents and their participation in a gamified system. By reusing FOAF (`foaf:Agent`/`foaf:Person`) and the existing multi-agent framework (JaCalIVE and organizational ontology), we stay semantically coherent with GIVEn’s foundation. Aligning `ArtificialAgent` with FOAF means any agent (human or AI) is consistently an Agent in the RDF sense, and a `foaf:Person` remains the class for human players. Roles are tied to the MAS ontology’s definitions of role within an organizational unit. Goals can be aligned to upper ontology notions of objectives (SUMO) or plans (PROV-O’s Plan, if using extensions of PROV for planning). Interactions might leverage PROV-O to record provenance of gamification actions (the *prov\:Agent* corresponds to our agent, the *prov\:Entity* could be a gamification component, and the *prov\:Activity* a gameplay event linking them).

Below is a snippet illustrating the agent-related extensions in OWL:

```turtle
@prefix cgo: <http://autosemanticgame.eu/ontologies/cgo#> .
@prefix foaf: <http://xmlns.com/foaf/0.1/> .
# (Assume mas: prefix as above for Role, and that GamificationDesignElement was defined earlier)

cgo:ArtificialAgent a owl:Class ;
    rdfs:subClassOf foaf:Agent ;
    rdfs:label "Artificial Agent"@en ;
    rdfs:comment "An autonomous non-human agent (e.g., AI bot or virtual character) in the gamified environment, capable of making decisions and acting towards goals."@en .

# Example specialized role (could also use mas:Role directly)
cgo:GamificationRole a owl:Class ; 
    rdfs:subClassOf mas:Role ;
    rdfs:label "Gamification Role"@en ;
    rdfs:comment "A role that an agent can play in the gamified system (e.g., Player, Spectator, Tutor, Designer). Uses existing MAS Role concept for integration."@en .

cgo:AgentGoal a owl:Class ;
    rdfs:label "Agent Goal"@en ;
    rdfs:comment "A goal or objective pursued by an agent in the gamified environment (e.g., achieving a score, assisting a player, increasing engagement)."@en .

cgo:hasGoal a owl:ObjectProperty ;
    rdfs:domain foaf:Agent ;
    rdfs:range cgo:AgentGoal ;
    rdfs:label "has goal"@en ;
    rdfs:comment "Associates an agent with a goal it is trying to achieve or fulfill."@en .

cgo:interactsWithElement a owl:ObjectProperty ;
    rdfs:domain foaf:Agent ;
    rdfs:range cgo:GamificationDesignElement ;
    rdfs:label "interacts with gamification element"@en ;
    rdfs:comment "Indicates that an agent interacts with or is affected by a gamification design element (mechanic, dynamic, or component) in the system."@en .
```

*In this snippet, `ArtificialAgent` is a subclass of foaf\:Agent (to distinguish AI agents from human foaf\:Person). We introduce `GamificationRole` as a subclass of the imported `Role` for semantic clarity, though specific roles (Player, Spectator, etc.) could be added similarly. The `hasGoal` property links agents to their objectives. We reuse FOAF for agents and the previously defined `GamificationDesignElement` for interactions. These additions allow us to represent statements like: “Agent123 (`rdf:type cgo:ArtificialAgent`) **hasGoal** GoalXYZ (maximize player score) and **interactsWithElement** some `cgo:Leaderboard` (thus Agent123 uses a leaderboard to achieve its goal).” This forms a basis for reasoning about agent behaviors and their interplay with gamified features.*

## Conclusion

Through these extensions, the GIVEn ontology is enhanced with: **(1)** a taxonomy of gamification techniques (mechanics, dynamics, components) grounded in OntoGamif’s core concepts and gamification literature; **(2)** a personality modeling layer incorporating the Big Five traits for user/adaptive personalization; and **(3)** a richer model of agents, including their roles within the gamified system, their goals and autonomous behaviors, and their interactions with game elements. We ensured semantic coherence by reusing GIVEn’s existing ontologies (FOAF for people/agents, the MAS/JaCalIVE ontology for norms and roles) and by aligning with upper ontologies (treating traits as qualities, mechanics as norms/rules, etc.). Standard ontologies like **FOAF** and **PROV-O** can be leveraged for interoperability – e.g., a gamification event can be described as a prov\:Activity involving a prov\:Agent (player) and prov\:Entity (gamification component). Overall, these extensions position GIVEn to describe not just the static elements of games, but also the *dynamic* gamification techniques, *human factors* (personality), and *intelligent agents* that together drive engagement in interactive virtual environments.

**Sources:**

* Bouzidi et al. (2019). *OntoGamif: A Modular Ontology for Integrated Gamification* – definitions of gamification mechanics, dynamics, etc.
* Werbach & Hunter (2012). *Gamification Framework* – taxonomy of dynamics, mechanics, components.
* Dermeval et al. (2019). *GaTO: Gamified Tutoring Ontology* – integration of gamification in ITS, motivating use of Big Five for personalization.
* **GIVEn Ontology (2016)** – existing classes (`GameDesign`, `Role`, `Achievement`, etc.) and imports (FOAF, JaCalIVE MAS ontology) that we built upon.
* Zsigmond et al. (2020). *Ontology-based UX Personalization for Gamified Education* – use of Big Five traits in ontology.
* OntoGamif user sub-ontology – example of user roles like *Spectator* (passive role in system), illustrating the need for role modeling in gamified environments.
