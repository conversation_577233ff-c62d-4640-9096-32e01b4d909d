Concept,Definition,Description,Domain,Range,Example
Feature weight,A numerical value that represents the importance of a personality feature in a model or system.,Feature weights are used to quantify the relative importance or influence of specific personality features when calculating personality profiles or making decisions based on personality traits.,,,"A feature weight of 0.75 for the 'Openness' trait indicates it has strong influence in the personality model."
Personality model,A structured framework used to categorize and understand personality traits and characteristics.,Personality models provide organized systems for conceptualizing and measuring different aspects of human personality, often including multiple traits and facets that describe behavioural tendencies and preferences.,,,The Big Five personality model which includes Openness, Conscientiousness, Extraversion, Agreeableness, and Neuroticism.
Personality profile,A collection of personality traits and their values that characterize an individual.,A personality profile represents the unique combination of personality features that describe a person's typical patterns of thinking, feeling, and behaving across different situations.,,,A personality profile showing high scores in Extraversion and Openness but lower scores in Neuroticism.
Facet,A specific aspect or component of a broader personality trait.,Facets are more narrowly defined characteristics that together make up a broader personality trait, allowing for more nuanced assessment of personality.,,,Assertiveness is a facet of the broader Extraversion trait.
Gender,A social or biological classification of an individual.,Gender represents a categorization that may influence personality development, social interactions, and behavioural tendencies in various contexts.,,,Male, Female, Non-binary
Gamification goal,An objective that gamification techniques aim to achieve.,Gamification goals represent the desired outcomes or purposes that guide the implementation of game elements in non-game contexts to enhance user engagement or behaviour change.,,,Increasing user retention in an educational application.
Trait,A relatively stable characteristic that influences behaviour across situations.,Traits are enduring personal qualities or attributes that shape how individuals typically respond to their environment and interact with others.,,,Extraversion as a trait characterized by sociability and assertiveness.
Age,A demographic characteristic representing the length of time a person has lived.,Age can influence personality development, preferences, and behaviors, making it an important factor to consider in personalized systems.,,,25 years old
Personality feature,A distinguishable characteristic or attribute of personality.,Personality features encompass the various elements that together form an individual's personality, including traits, facets, and other measurable aspects of psychological functioning.,,,Creativity as a personality feature related to the Openness trait.
Gamification technique,A method that applies game design elements in non-game contexts.,Gamification techniques involve incorporating elements typically found in games (such as points, badges, or leaderboards) into non-game environments to increase engagement and motivation.,,,Using achievement badges to reward user progress in a fitness application.
has weight value,A data property that assigns a numerical weight to a feature.,This property quantifies the importance or influence of a personality feature within a model or system using a floating-point value.,Feature weight,float,0.85
has name,A data property that assigns a text label to an entity.,This property provides a human-readable identifier for various entities in the ontology, facilitating reference and organization.,,,Price Optimizer
has gamification outcome,A relationship between a technique and its result.,This property connects gamification techniques to their observed or intended effects, allowing for evaluation of effectiveness.,Gamification technique,,,
has gamification goal,A relationship between an entity and its gamification objective.,This property links entities to the specific goals they aim to achieve through gamification, establishing purpose and direction.,,,
has personality feature,A relationship between a profile and its constituent features.,This property connects personality profiles to the specific features that characterize them, establishing the composition of the profile.,Personality profile,Personality feature,
has event object,A relationship between an event and its associated object.,This property links events to the objects they affect or involve, providing context for the event's occurrence and impact.,,,
has personality profile,A relationship between an entity and its personality characteristics.,This property connects entities (typically agents or users) to their personality profiles, enabling personalization based on individual differences.,Agent,Personality profile,