\chapter{Specification}\label{ch: MAGO-Ga Specification}

This chapter presents the ontology engineering process for the \magoga ontology. The process is based on the METHONTOLOGY methodology \cite{fernandez-lopez1997METHONTOLOGYOntologicalArt}. The METHONTOLOGY methodology is a structured approach to ontology development that consists of six main steps: specification, knowledge acquisition, conceptualisation, formalisation, integration, and implementation.

The specification phase is the first step in the ontology engineering process. The goal of this phase is to define the purpose, scope, and intended use of the ontology. The specification phase also involves identifying the key concepts and relationships that will be included in the ontology.

The \magoga ontology is designed to model gamified multi-agent systems modelled as \acfp{IVE}. The main objective of the ontology is to provide a formal representation of the gamification elements and their interactions with the agents in the system. In addition to the gamification elements, the ontology is also aimed at representing personality traits and facets based on the Five Factor personality model \cite{mccrae2006PersonalityAdulthoodFiveFactor}. Finally, this ontology is aimed to serve as a knowledge base for reasoning about gamified multi-agent systems.

The intended use
\marginnote{intended use}%
of the ontology is to represent the gamification elements and their interactions with the agents in the system. The ontology will also be used to represent the agents' personalities and their interactions with the gamification elements. In order to make it possible to model how the gamification elements affect the agents' personalities, the ontology will include concepts related to the Five Factor personality model. It should be noted here that this ontology does not aim to comprise all the concepts related to gamification, but rather a subset of concepts that are applicable to gamified multi-agent systems. Therefore, only a specialised selection of concepts of the related ontologies will be considered for inclusion in the \magoga ontology.

The modelled ontology can be used for modelling a specific gamified multi-agent system, but it is not intended to model individual agents or their specific interactions. The ontology is rather intended to model the system's design, i.e. the gamification elements and their interactions with the agents in the system, and the agents' personalities and the personalities specific gamification techniques influence the most.

A proposed scenario of use
\marginnote{scenario of use}%
of the \magoga ontology is to model a gamified multi-agent system for social simulations. Agents are expected to have personalities that are represented using the Five Factor personality model. The gamification elements are expected to influence the agents' behaviour or decisions, and the agents' personalities are expected to influence their interactions with the gamification elements.

The \magoga ontology will be expressed using a high degree of formality,
\marginnote{degree of formality}%
namely classified as rigorously formal by \cite{uschold1996OntologiesPrinciplesMethods}. Such a formal structure and its many constraints ensure that the ontology is clear, unambiguous, and easy to use. Furthermore, it is easier to use it to extend other ontologies or be extended by other ontologies. Lastly, a highly formalized expression of an ontology makes it easier to use it in conjunction with other digital systems.

The intended level of granularity 
\marginnote{level of granularity}%
is expected to make it possible to model a specific gamified multi-agent system, but not to model individual agents or their specific interactions. Furthermore, the ontology is not intended to model the implementation of the system, but rather the system's design.

Based on the above description of the \magoga ontology, the ontology is planned to comprise the concepts
\marginnote{scope}%
related to gamification, personality traits and facets, and the interactions between them. The ontology will be based on the existing ontologies, but only a specialised selection of concepts will be considered for inclusion in the \magoga ontology.
