\chapter{Knowledge Acquisition}\label{ch: mago-ga knowledge acquisition}

The main focus of the knowledge acquisition process involved in the making of this ontology is the acquisition of knowledge related to gamification. The knowledge acquisition process is based on the METHONTOLOGY methodology \cite{fernandez-lopez1997METHONTOLOGYOntologicalArt}. The METHONTOLOGY methodology is a structured approach to ontology development that consists of six main steps: specification, knowledge acquisition, conceptualisation, formalisation, integration, and implementation.

The knowledge acquisition process is the second step in the ontology engineering process, although it is an ongoing process. The goal of this phase is to identify the key concepts and relationships that will be included in the ontology. The knowledge acquisition process involves researching the existing ontologies applicable to describing gamification, and identifying the most relevant concepts and relationships. The knowledge acquisition process also involves identifying the most relevant sources of knowledge, such as expert interviews, text analysis, and brainstorming techniques.

Gamification is often defined as the use of game design elements in non-game contexts \cite{deterding2011GamificationDefinition}, usually with the main goal of engaging and motivating users. Gamification is a complex phenomenon that involves various elements, such as game mechanics, dynamics, and components, as well as psychological theories and motivational frameworks. Therefore, the knowledge acquisition process for the \magoga ontology involves researching the existing ontologies applicable to describing gamification, and identifying the most relevant concepts and relationships.

The main identified and selected sources of knowledge on gamification are the following ontologies:

\begin{itemize}
    \item OntoGamif \cite{bouzidi2019OntoGamifModularOntology},
    \item Gamification Domain Ontology (GaDO) \cite{dermeval2019GaTOOntologicalModel}.
\end{itemize}

\newthought{OntoGamif} is one of the earliest ontologies for gamification \cite{bouzidi2019OntoGamifModularOntology}. This ontology is a modular ontology covering several sub-domains of the domain of gamification:
\begin{enumerate}[label=\arabic*)]
    \item Core Gamification Concepts (generic game design elements and principles),
    \item Organizational Concepts (context of the implementing organization),
    \item Evaluation Concepts (metrics and assessment of gamification outcomes),
    \item Psychological Concepts (theories of motivation, player types, user behaviour),
    \item Ethical Concepts (ethical considerations of gamification, e.g. manipulation, privacy),
    \item Risk Concepts (potential negative side-effects or risks of gamification), and
    \item User-Related Concepts (profiles of target users/players).
\end{enumerate}

OntoGamif was built by extracting definitions of gamification elements from a broad literature review. Each class of this ontology is annotated with its source reference. The ontology is grounded in the SUMO upper ontology to ensure alignment with higher-level concepts. A key motivation for OntoGamif was to establish an agreed-upon common representation of gamification knowledge, which can facilitate communication among stakeholders (researchers, designers, managers) and enable semantic reasoning about gamified systems.

In terms of contribution, OntoGamif provides a holistic semantic model of gamification, unifying concepts from game design, psychology, and implementation practice. Notably, the authors suggest it can help managers understand employee behaviour and needs through gamification's theoretical lens, indicating its relevance to enterprise contexts (e.g. workplace gamification). OntoGamif is publicly available: the authors released the ontology's OWL file and documentation via the Mendeley Data repository. 



\newthought{GaDO} is an ontology for gamification in the domain of intelligent tutoring systems (ITS) \cite{dermeval2019GaTOOntologicalModel}. The main purpose of this ontology was \enquote{to support its application in intelligent tutoring systems.} This ontology is divided into two sub-domain ontologies:
\begin{enumerate*}[label=\arabic*)]
    \item GaDO Core, and
    \item GaDO Full.
\end{enumerate*}

GaDO Core covers the gamification domain, including game design elements, motivational theories, and user profiles. GaDO Full extends GaDO Core by adding ITS-specific concepts, such as student models, domain models, and pedagogical components. The main contribution of GaDO is the integration of gamification with ITS, enabling semantic modelling of the use of gamification in educational contexts. GaDO is publicly available. The GaDO ontology covers the gamification domain, including game design elements, motivational theories, and user profiles.

GaDO ontologies are used in the process of engineering the GaTO ontology for gamification in the domain of intelligent tutoring systems (ITS) \cite{dermeval2019GaTOOntologicalModel}. The main purpose of this ontology was \enquote{connecting gamification
and intelligent tutoring systems concepts.}

The ontology maps specific game design elements to these target behaviour categories, grounding gamification design choices in pedagogical objectives. By formalizing such relationships, GaTO supports automated reasoning (e.g., an intelligent tutor can infer which game element to deploy to foster a certain behaviour) and interoperability of gamified tutoring systems. The authors evaluated GaTO using ontology quality metrics and demonstrated a use-case of an ontology-driven authoring tool for teachers. GaTO is also available publicly.

These ontologies aim to support personalized gamification -- adapting game elements to different learners -- and to guide the design of gamified learning activities in a systematic way.



\newthought{Other} ontologies have been found in the domain of gamification, but most are focused on specific aspects or applications of gamification, rather than providing a comprehensive model of the domain. Education has been a particularly active domain for gamification, and consequently many recent ontologies focus on modelling gamification in educational contexts. These include ontologies for integrating gamification with intelligent tutoring systems \cite{dermeval2019GaTOOntologicalModel}, gamified collaborative learning sessions \cite{c.challco2015PersonalizationGamificationCollaborative}, personalised or adaptive gamification in online learning platforms \cite{bennani2020AGELearnOntologyBasedRepresentation}, profiling students in the context of gamified learning systems \cite{missaoui2020OntologyRepresentingStudents}, modelling user profiles and activities in gamified systems \cite{palomino2023OntologyModellingUser}, and game-based assessment \cite{gomez2024DevelopingValidatingInteroperable}. However, these ontologies are not directly applicable to the domain of multi-agent systems and are therefore not considered in the knowledge acquisition process for the \magoga ontology. Moreover, these ontologies are focused on specific aspects of gamification, rather than providing a comprehensive model of the domain. Therefore, they are considered only as inspiration in the knowledge acquisition process for the \magoga ontology.

\newthought{The concepts} of the above mentioned two ontologies revolve, naturally, around the central concept of gamification and gamified systems. Since gamification by definition heavily depends on game design elements, the two ontologies also include a wide range of concepts related to game design. Finally, both ontologies include concepts related to the users of gamified systems, which is also relevant to the \magoga ontology since the agents in the system are the users of the gamified system and the players that can be found within it.

Some basic game design elements are mentioned in these ontologies, such as game mechanics, game dynamics, and game components.
In OntoGamif, gamification mechanics
\marginnote{gamification mechanics}%
are defined as the functional components of a gameful system that provide actions and control mechanisms. Examples given include point systems, leaderboards, levels, and challenges. GaDO similarly defines Mechanic as one of the three types of game design element. In GaDO, though, game mechanics are the rules or rewards that govern the gamified learning process. For modelling a gamified multi-agent system, mechanics may be vital in the context of describing what gamified incentives or constraints are present.
Gamification dynamics
\marginnote{gamification dynamics}%
are modelled as the individual's reactions or behaviours that arise in response to the gamified system's mechanics. In OntoGamif, dynamics are defined as the emergent reactions or behavioural outcomes that result from applying mechanics. For example, a mechanic like a leaderboard may create competition among players, wherefore competition is a dynamic, i.e. a user reaction or social interaction pattern. In GaDO, dynamics are not explicitly modelled, but the idea is similar: the behavioural outcomes of gamified interactions are of central importance. For a multiagent system, dynamics could describe how the agents behave in response to the gamified system's mechanics.
Ultimately, gamification elements
\marginnote{gamification elements}%
are modelled as components of a game, i.e. specific elements or tokens used in gamification. In OntoGamif, these are called gamification components and include elements like badges, points, leaderboards, and quests. In GaDO, the idea is similar: the concrete elements used in gamification are of great importance. For a multiagent system, components might describe the specific elements used in the gamified system. Usually there are a limited number of game components that are used most often, although the comprehensive set is much larger. The most well-known components include:

\begin{itemize}
    \item Badge\marginnote{badge} -- a symbolic reward (usually an icon or title) given for accomplishing something, e.g. a \enquote{Problem Solver} Badge after solving 100 puzzles.
    \item Point\marginnote{point} -- a numeric score that accumulates, often for accomplishing tasks or reaching milestones, e.g. earning points for each completed task. Points can be used in different rules, such as to unlock new levels or to compete on leaderboards.
    \item Leaderboard\marginnote{leaderboard} -- a ranked list of players by some score, often used to encourage competition and social comparison. For example, one player might try to outscore another player to reach the top of the leaderboard.
    \item Quest\marginnote{quest} -- a series of tasks or challenges that must be completed to earn a reward. For example, a player might be given a quest to complete 10 tasks within a certain time limit to earn a reward.
    \item Level\marginnote{level} -- a progressive stage that agents can achieve, e.g. an agent might reach a new level after completing a certain number of tasks or achieving a certain score.
\end{itemize}

\newthought{Personality} modelling is one of the many interesting aspects of gamification and modelling a gamified system, since personality traits and facets are known to influence the effectiveness of gamification techniques \cite{ertansevk2024ReviewPersonalizationGamified, kirchner-krath2024UncoveringTheoreticalBasis, phosanarack2025UserCenteredPersonalizedGamification, ray2024ImpactPersonalityTraits}. For example, extraverts tend to enjoy competitive and social elements like leaderboards, whereas introverts might prefer unlockable content or single-player challenges. Conscientious individuals might respond well to achievement badges and clear goals, while those high in Openness might appreciate narrative and exploration mechanics.

The Big Five personality model, also known as the Five-Factor Model \cite{mccrae2006PersonalityAdulthoodFiveFactor}, also known as OCEAN for the five personality traits it includes (Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism), is a widely accepted framework for describing human personality facets. While neither OntoGamif nor GaTO explicitly provide a full Big Five trait ontology out of the box, OntoGamif's user model is designed to accommodate personality aspects, and it can be extended to incorporate the Big Five traits and facets. However, OntoGamif does not list each trait as a separate class. GaTO itself doesn't incorporate the Big Five, but it does incorporate the BrainHex player types as a form of gamer psychology modelling. BrainHex types (e.g. Seeker, Survivor, Daredevil) correlate with certain play styles, which are arguably related to personality traits (for example, BrainHex Socializer overlaps with being high in extraversion/agreeableness).

In the context of multi-agent systems, if our agents are simulations of humans or user personas, giving them personality profiles (even randomly assigned or scenario-specific) can lead to more diverse and realistic behaviour patterns. For example, in a multi-agent training simulation, we might want some agents to be competitive (high extraversion/low agreeableness) and others to be cooperative (high agreeableness) to see how they interact under a gamified incentive scheme.

Selecting the most relevant concepts from the ontologies, the following list of concepts is identified as relevant to the context of modelling gamification in the domain of multi-agent systems.

\csvreader[
    separator=semicolon,
    head to column names,
    ]{DDGa.csv}{}% use head of csv as column names
{
\begin{table}[h]
    \centering
    \caption{\emph{\ConceptName} glossary entry}
    \label{gt: mago-ga \ConceptName}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{0.25\linewidth}|p{0.71\linewidth}}
        \toprule
        \textbf{Concept name} & \ConceptName\\
        \midrule 
        \textbf{Description} & \Description\\
        \bottomrule

    \end{tabular*}
\end{table}
}