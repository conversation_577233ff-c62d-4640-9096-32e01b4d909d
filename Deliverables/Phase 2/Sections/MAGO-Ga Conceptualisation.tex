\chapter{Conceptualisation}\label{ch: mago-ga conceptualisation}

Based on the knowledge acquired in the previous chapter, the next step is to transform the collected knowledge into a structured and meaningful model. During this phase, the key concepts, relationships, attributes, and rules of the domain are identified and organized to reflect the domain's structure. The goal is to create a clear and coherent conceptual model that captures the domain's essential elements without formalizing them into specific ontology languages. The conceptual model produced in this phase is not yet formalized but provides a foundational blueprint for the subsequent formalization and implementation phases of the ontology development process.

Several concepts have been identified as relevant to the context of modelling gamification in the domain of multi-agent systems. The visual representation
\lookAt{\cref{fig: mago-ga concepts}}%
of the conceptual model is based on the concepts of three ontologies. The visual representation of the conceptual model is an extension of the \given ontology. The concepts that are not going to be included in the final ontology are absent from the visual conceptual model.

The selection of the concepts for the \magoga ontology is based on the intended use of the ontology. The ontology is intended to model gamified multi-agent systems, wherefore the concepts related to gamification are carefully chosen. The concepts related to the agents and their personalities are also included, as they are essential for modelling the interactions between the agents and the gamification elements.



\section{Data Dictionary}

The following list of tables
\lookAt{\crefrange{dd: mago-ga Gamification technique}{dd: mago-ga has name}}%
provides a detailed description of the selected concepts, their definitions, and their intended use in the ontology.

\begin{figure*}
    \centering
    \includegraphics[width=1\linewidth]{Deliverables/Phase 2/Figures/mago-ga concept}
    \caption{Concepts and their relationships in the \magoga ontology}
    \label{fig: mago-ga concepts}
\end{figure*}

\csvreader[
    separator=semicolon,
    head to column names,
    ]{ConceptsGa.csv}{}% use head of csv as column names
{
\begin{table}[h]
    \centering
    \caption{\emph{\ConceptName} concept details}
    \label{dd: mago-ga \ConceptName}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{0.25\linewidth}|p{0.71\linewidth}}
        \toprule
        \textbf{Concept name} & \ConceptName \\
        \midrule
        \textbf{Definition} & \Definition \\
        \noalign{\vskip 2mm}
        \textbf{Description} & \Description\\
        \if \Domain
            \else 
                \noalign{\vskip 2mm}
                \textbf{Domain} & \Domain\\
        \fi
        \if \Range
            \else 
                \textbf{Range} & \Range\\
        \fi
        \bottomrule
    \end{tabular*}
\end{table}
}