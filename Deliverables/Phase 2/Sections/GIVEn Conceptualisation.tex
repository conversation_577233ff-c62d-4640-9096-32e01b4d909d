\chapter{Conceptualisation}\label{ch: given conceptualisation}

The acquired knowledge laid out in the previous section is transformed into a structured and meaningful model in this phase of ontology development. During this phase, therefore, the key concepts, relationships, attributes, and rules of the domain are identified and organized to reflect the domain's structure. The goal is to create a clear and coherent conceptual model that captures the domain's essential elements without formalizing them into specific ontology languages. The conceptual model produced in this phase is not yet formalized and can be rather unstructured, but provides a foundational blueprint for the subsequent formalization and implementation phases of the chosen ontology development process.

First, a graphical representation
\lookAt{\cref{fig: GIVEn conceptualisation concepts}}%
of the concepts and their relationships is created. The concepts are represented as nodes, and the relationships between them as edges. Not all of the relationships are to be modelled as properties in the ontology afterwards. Before proceeding to the formalisation phase, the graphical representation is reviewed and refined to ensure that it accurately captures the domain's structure and that all relevant concepts and relationships are included. Furthermore, it is possible that some of the concepts are not necessary after all, and can be discarded when moving on to the formalisation phase. On the other hand, additional concepts may be identified, that have not been a part of the acquired knowledge presented in the previous section, nor in the list of concept provided in it, but should be added to the graphical representation.

The prepared visual representation of the conceptual model is based on the concepts of three ontologies. The concepts from the MAMbO5 ontology are represented in green, those from the CGO in blue, and those from the VGO in orange. The concepts that are additions to the ones found in the ontologies are represented in dashed boxes. The concepts that are not going to be included in the final ontology are absent from the visual conceptual model.

\begin{figure*}
    \begin{sideways}
        \centering
        \resizebox{!}{0.9\linewidth}{
            \includegraphics{Deliverables/Phase 2/Figures/conceptualisation model}
        }
        \caption{Concepts and their relationships in the \given conceptual model}
        \label{fig: GIVEn conceptualisation concepts}
    \end{sideways}
\end{figure*}

Conceptual modelling was started using the fundamental concepts of MAMbO5 ontology, such as Agent, Role, Behaviour, Action, Strategy, Artefact, Knowledge Artefact, Norm, and Workspace. The concepts from the CGO and VGO were added later, as they were deemed relevant to the domain of video games and their representation as an \ac{IVE}. Those concepts from CGO and VGO that were left in the model are represented in blue and orange, respectively, in dashed boxes, indicating that they are additions to the ones found in the MAMbO5 ontology. The additional concepts, not previously found in any of the three observed ontologies, were added as the need arose, and are represented in clear dashed boxes in the visual representation of the conceptual model.

The \mintedInline{Agent} concept is one of the central concepts in the model, as it represents the entities that are bound to a specific video game and can interact with the in-game world, i.e. some \mintedInline{Workspace}, and with each other. The \mintedInline{Agent} can be either a \mintedInline{Player} or a \mintedInline{Character}. The \mintedInline{Player} represents the human user in a video game, while the \mintedInline{Character} represents the \acp{NPC} in the game. The \mintedInline{Character} concept can be further divided into subconcepts representing specific types of \acp{NPC}, yet that division is not to be present in the current version of the ontology, but is expected to be expanded with in the future, i.e. should be added when using the subject on ontology for modelling a specific video game genre.

Each \mintedInline{Agent} can access a set of \mintedInline{Role} individuals that are relevant within the context of the video game they are related to, and wherein they can be found. The \mintedInline{Role} concept represents the different roles that the agents can play in the game, and is a key element in the game's narrative and gameplay. Roles are especially popular in \acp{RPG}, where the player can choose from a set of predefined roles, each with its own set of abilities and limitations. The \mintedInline{Role} is associated with a set of \mintedInline{Behaviour} individuals that define the actions and activities that an agent can perform when playing that role. In other words, behaviours are the means by which the agents can achieve the objectives set by the roles they play, i.e. behaviours are a way that agents can affect the environment they are in. The \mintedInline{Behaviour} are associated with \mintedInline{Action} that are the actual actions that the agent can perform. Several actions may be grouped together to form a \mintedInline{Behaviour}, and a single action may be a part of multiple behaviours. For example, a behaviour of shooting a bow may be composed of the actions of drawing the bow, aiming, and releasing the arrow. On the other hand, the action of drawing the bow may be a part of the behaviour of shooting a bow, but also of the behaviour of reloading the bow, or the behaviour of aiming the bow.

Since \mintedInline{Action} class is defined as atomic by MAMbO5, it is not further divided into subconcepts in the current version of the ontology. However, it is expected that the ontology will be expanded with in the future, i.e. more specific concepts may be added when using the ontology for modelling a specific video game genre. Activities can be used to achieve certain \mintedInline{Objective} class individuals. These objectives can be observed as specific subclasses of the \mintedInline{Quest} class. An objective should be described here as an individual goal to be accomplished, while a quest is a collection of objectives that are to be accomplished in a specific order. A quest may be specific to some particular game genres, but it may be considered in a more general and abstract manner as a list of objectives with a specific starting situation and a specific ending situation. The quest is a way to structure the gameplay and provide the player with a sense of progression and accomplishment. The quest is also a way to structure the narrative of the game, as the player's progress through the quest can be used to drive the story forward. The quest is, therefore, a way to connect the gameplay and the narrative of the game.

The concept of \mintedInline{Gameplay} represents the way a player interacts with a given game. Therefore, this concept is a part of the \mintedInline{Strategy} concept, as the player's strategies are the means by which they interact with the game and achieve their objectives. The \mintedInline{Strategy} concept is a way to represent the player's approach to the game, and can be used to model different play styles and strategies that players can use to achieve their objectives.

The concepts of a \mintedInline{Rule} and \mintedInline{LocalRule} are added to the model to represent the game's mechanics. The \mintedInline{Rule} concept represents the general rules of the game, while the \mintedInline{LocalRule} concept represents the rules that are specific to a particular quest or a particular part of the game. The \mintedInline{Rule} and \mintedInline{LocalRule} concepts are both subclasses of the \mintedInline{Norm} concept, since they are both normative rules that define the behaviour of the agents in the game. The \mintedInline{Rule} is a more general concept, as it represents the rules that are applicable in the entire game, while the \mintedInline{LocalRule} is a more specific concept that represents the rules that are applicable only in a specific context, i.e. a specific location in the \ac{IVE}. The \mintedInline{Rule} concept is a subclass of \mintedInline{Norm}, just like the concept of \mintedInline{Role}, but while the \mintedInline{Role} concept represents the roles that the agents can play in the game, the \mintedInline{Rule} concept represents the rules that govern more than just the behaviour of the agents in the game. While roles empower agents, rules constrain the world they are in.

A collection of norms (including roles and rules) is a specific type of \mintedInline{Knowledge Artefact} concept, since they are a piece of knowledge that is shared among agents within an organisation. Furthermore, such a collection may be queried by the agents to determine the applicable norms in a specific context. This concept is a subclass of the \mintedInline{Artefact} concept, and represents a piece of knowledge or information that is specific to an individual agent or shared among agents within an organisation. More specifically, a knowledge artefact may contain knowledge that can be retrieved by individual agents. A specific new concept was added to the conceptual model to represent an artefact containing a collection of data about a single agent, i.e. the \mintedInline{Inventory} concept. The \mintedInline{Inventory} concept represents the data that is specific to a single agent, and is therefore a specific type of a \mintedInline{Knowledge Artefact}. Although the concept of inventory is used by video games to store specific in-game items that a player or a character can carry with them, the \mintedInline{Inventory} concept is a more general concept that can be used to store any data that is specific to a single agent, such as their health, level, score, or any other relevant information. There is no restriction on how the data is stored in the \mintedInline{Inventory} concept, and it is up to the specific genre implementation of the ontology to decide how the data is structured and represented.

One specific form of an artefact defined in this conceptual model is the VGO concept \mintedInline{Achievement}. The \mintedInline{Achievement} represents a reward that is given to a player for accomplishing a certain task in the game. Finishing a task should be modelled as an \mintedInline{Observable Event}, as it is an event that can be observed by the agents in the game. An achievement may affect several different elements of a game, yet the conceptual model shows its most common effect, i.e. that it affects an agent's \mintedInline{Inventory}. For example, an achievement might be a badge the player will receive, a specific number of experience points, a power-up that can be used in the game, or an \mintedInline{Item} awarded to the player participating in an \mintedInline{Observable Event}.

Concepts \mintedInline{Visuals}, \mintedInline{Audio}, and \mintedInline{LevelDesign} are added to the model to represent the different aspects of the game's aesthetics, i.e. various assets related to a game. The \mintedInline{Visuals} concept represents the visual elements of the game, such as the graphics and the user interface. The \mintedInline{Audio} concept represents the audio elements of the game, such as the music and the sound effects. The \mintedInline{LevelDesign} concept represents the design of the levels in the game, i.e. the layout of the game world and the placement of the in-game elements. Most of the objects individuals of these concepts are located in the \mintedInline{Workspace} concept, as they are the elements that make up the game world and are related to specific areas or maps or other instances of the \mintedInline{PlayingArea} concept.

One of the concepts that should not be excluded is the concept of an \mintedInline{Item}. Since items are usually rendered as physical, relative to the in-game world, they are modelled as a subclass of the \mintedInline{Physical Artefact} concept. It should be noted here that artefacts have related \mintedInline{Action} individuals, as they are the means by which the agents can interact with the artefacts. For example, an \mintedInline{Item} can be picked up by an agent, or used in some way. The \mintedInline{Action} individuals related to an artefact represent the ways in which the agents can interact with the artefact.

As opposed to an \mintedInline{Inventory} individual describing and defining some aspects of an agent, \mintedInline{Observable Property} individuals are used to define the properties of the artefacts that may be observed by agents. For example, an \mintedInline{Item} may have a property that defines its weight, or its value, or its rarity. These properties can be observed by agents, and can be used to determine the behaviour of the agents towards the artefact. Specific properties that define what an artefact looks like or where it is located are modelled as \mintedInline{Physical Property} individuals, as they are properties that are specific to the physical representation of the artefact in the game world.

\vspace{2em}

According to some of the most popular literature on video games, there are four basic elements that form a game \cite{schell2019ArtGameDesign}:
\begin{enumerate}
    \item \textbf{Mechanics} -- create the structure of the game and define how players interact with it;
    \item \textbf{Story} -- provides context and meaning to the gameplay experience;
    \item \textbf{Aesthetics} -- contributes to the game's atmosphere and style;
    \item \textbf{Technology} -- used to create and run the game.
\end{enumerate}

These elements are present in the proposed conceptual model as well, even though several concepts of the model may be mapped to a single element.
\lookAt{\cref{fig: GIVEn conceptualisation concepts game elements}}%
For example, the \mintedInline{Rule} and \mintedInline{LocalRule} concepts, as well as the \mintedInline{Role} concept, can be considered as part of the mechanics of the game. The \mintedInline{Visuals}, \mintedInline{Audio}, and \mintedInline{LevelDesign} concepts can be considered as part of the aesthetics of the game. The \mintedInline{Narrative} concept can be considered as part of the story of the game. The technology element is not explicitly represented in the conceptual model, as it is not relevant to the domain of video games and their representation as an \ac{IVE}, i.e. the technology element includes the game engine, the programming language, the graphics engine, the audio engine, etc., which are not deemed relevant to the domain of the ontology.

\begin{figure*}
    \begin{sideways}
        \centering
        \resizebox{!}{0.9\linewidth}{
            \includegraphics{Deliverables/Phase 2/Figures/conceptualisation model game elements}
        }
        \caption{Marked areas of the \given conceptual model that represent three basic elements of a game}
        \label{fig: GIVEn conceptualisation concepts game elements}
    \end{sideways}
\end{figure*}