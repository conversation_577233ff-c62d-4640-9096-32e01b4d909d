\chapter{Formalisation}\label{ch: mago-ga formalisation}

The formalisation phase in ontology engineering focuses on applying formal semantics to the conceptual model of a domain. This phase aims to create an ontology that is precise, unambiguous, and capable of supporting complex reasoning. By using a formal language, the ontology's structure, relationships, and constraints become systematically interpretable by both humans and machines.

The formalisation of the \magoga ontology is based on the \ac{OWL}. \ac{OWL} is a family of knowledge representation languages for authoring ontologies. OWL ontologies are based on the \ac{RDF} and are designed to be machine-readable and machine-interpretable. Even so, \magoga ontology is given a human-readable documentation in this deliverable, using Turtle
\lookAt{\cref{ch: mago-ga serialization turtle}}
syntax serialization, as well as a machine-readable formalisation in the OWL format.

\ac{FOI}