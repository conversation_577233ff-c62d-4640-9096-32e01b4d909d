\part{The Games as Intelligent Virtual Environments Ontology \given}

% \input{Deliverables/Phase 2/Sections/Introduction}
% \input{Deliverables/Phase 2/Sections/GIVEn Specification}
% \input{Deliverables/Phase 2/Sections/GIVEn Knowledge Acquisition}
% \input{Deliverables/Phase 2/Sections/GIVEn Conceptualisation}



\part{The Gamification of an Intelligent Virtual Environment Ontology \magoga}

\input{Deliverables/Phase 2/Sections/Introduction}

\input{Deliverables/Phase 2/Sections/MAGO-Ga Specification}
\input{Deliverables/Phase 2/Sections/MAGO-Ga Kn Acquisition.tex}
\input{Deliverables/Phase 2/Sections/MAGO-Ga Conceptualisation.tex}
\input{Deliverables/Phase 2/Sections/MAGO-Ga Formalisation.tex}
% \input{Deliverables/Phase 2/Sections/MAGO-Ga Integration.tex}
% \input{Deliverables/Phase 2/Sections/MAGO-Ga Implementation.tex}


\part{The Agent Gamification Framework \magoga}

\input{Deliverables/Phase 2/Sections/framework introduction}
% \input{Deliverables/Phase 2/Sections/framework design and description}
% \input{Deliverables/Phase 2/Sections/case study the recipe world}

\part{Appendices}

\input{Deliverables/Phase 2/Sections/Appendix - MAGO-Ga Ontology Serialization Turtle}
% \input{Deliverables/Phase 2/Sections/Appendix - Ontology Serialization OWL}