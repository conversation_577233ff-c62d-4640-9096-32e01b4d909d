\chapter*{\magoontologyname Framework Implementation Files}\label{ch: framework implementation files}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/mago_thing.py}
    \caption{The \mintedInline{mago_thing.py} file}
    \label{lst: appendix mago-ag thing}
\end{longlisting}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/mago_agent.py}
    \caption{The \mintedInline{mago_agent.py} file}
    \label{lst: appendix mago-ag agent}
\end{longlisting}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/mago_behaviour.py}
    \caption{The \mintedInline{mago_behaviour.py} file}
    \label{lst: appendix mago-ag behaviour}
\end{longlisting}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/mago_plan.py}
    \caption{The \mintedInline{mago_plan.py} file}
    \label{lst: appendix mago-ag plan}
\end{longlisting}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/mago_workspace.py}
    \caption{The \mintedInline{mago_workspace.py} file}
    \label{lst: appendix mago-ag workspace}
\end{longlisting}

\begin{longlisting}
    \mintedFilePython[]{Deliverables/Phase 1/Implementation/translate.py}
    \caption{The main script of the framework}
    \label{lst: appendix mago-ag translate}
\end{longlisting}
