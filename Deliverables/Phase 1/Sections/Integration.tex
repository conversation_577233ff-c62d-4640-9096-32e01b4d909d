\chapter{Integration}\label{ch: Integration}

The integration phase of this ontology engineering methodology focuses on harmonizing the developed ontology with other relevant ontologies or knowledge sources to create a unified knowledge ecosystem. This phase enriches the ontology's content, enhancing its semantic reach and improving its reusability across various applications.

Connecting with external ontologies supports data consistency, reduces redundancy, and ensures that the developed ontology can communicate effectively within broader systems, ranging from semantic web applications to intelligent multi-agent frameworks.

Integration is commonly performed using a combination of the following actions. Related equivalent concepts are identified between the current ontology and other ontologies to ensure consistency and avoid redundancy.
Concepts and relationships from multiple ontologies are combined, often by importing parts of external ontologies or adding new entities that enhance the current model.
Links are established between concepts in different ontologies, defining rules or correspondences to enable data interchange and semantic interpretation across systems.
Adapting the ontology structure, used and existing naming conventions, and logical definitions ensures compatibility with the integrated ontologies or systems.

The \magoontologyname ontology can be considered a filtered out and domain-specific subset of concepts from the \ac{MAMbO5} ontology. This is a natural continuation of the already established research cooperation between this research's host and sending institutions. The concepts of \magoontologyname ontology bearing the same names as those of the \ac{MAMbO5} ontology may be considered to be the same, even though taking them for subconcepts should be the preferred approach since \magoontologyname concepts
\lookAt{\cref{fig: mapping of the mago and the mambo5 concepts}}%
are made to be adapted to implementation and are thus more domain-specific. Furthermore, newly-defined properties of the \magoontologyname ontology, related to the concepts that can be found in \ac{MAMbO5} as well, present a valid argument in favour of regarding \magoontologyname concepts as \ac{MAMbO5} subconcepts.

For the sake of simplicity in the context of using the \magoontologyname ontology with the \magoontologyname framework, the ontology is not formally related to \ac{MAMbO5} ontology in its current form of implementation.

\begin{figure}
    \centering
    \input{Deliverables/Phase 1/Figures/mapping concepts to mambo5}
    \caption{Mapping of the \magoontologyname and the MAMbO5 concepts}
    \label{fig: mapping of the mago and the mambo5 concepts}
\end{figure}
