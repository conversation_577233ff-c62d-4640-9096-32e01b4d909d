\chapter{Implementation}\label{ch: Implementation}

The implementation phase is a step in ontology engineering where the abstract model of the domain is transformed into a tangible, functional system component. This phase involves converting the formalized ontology into a format that is compatible with the chosen technological infrastructure, enabling it to interact with other systems, perform reasoning tasks, and respond to user queries. Successful implementation ensures the ontology accurately represents knowledge and operates smoothly within its intended environment. This process forms the foundation for intelligent systems, knowledge management tools, and data-driven applications.

The \magoontologyname ontology was implemented using the Prot\'{e}g\'{e} tool for engineering ontologies. 
The implemented ontology is available on the GitHub repository of this research.
\link{https://github.com/AILab-FOI/MAGO}%
The ontology is serialized using the OWL/XML method. Such a file, bearing the extension \mintedInline{.owx}, can be used by the programming language Python via the available libraries, such as \mintedInline{owlready2}.

The implemented ontology is consistent and provides additional knowledge when reasoned upon using one of the reasoners available in Prot\'{e}g\'{e} or \mintedInline{owlready2}.

The complete OWL/XML serialization of the implemented \magoontologyname ontology is in appendix \nameref{ch: serialization owl}.
\lookAt{\nameref{ch: serialization owl}}
