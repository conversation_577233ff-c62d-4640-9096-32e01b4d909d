\chapter{Knowledge Acquisition}\label{ch: Knowledge Acquisition}

% Finished analysis of the applicable concepts and a collection of the concepts to be used in the ontology describing multiagent systems, i.e. the refined first glossary with potentially relevant terms and their meaning.

\blockquote[{{\cite[p. 37]{fernandez-lopez1997METHONTOLOGYOntologicalArt}}}]{It is important to bear in mind that knowledge acquisition is an independent activity in the ontology development process. However, it coincides with other activities. \textelp{} Most of the acquisition is done simultaneously with the requirements specification phase and decreases as the ontology development process progresses.}

The authors of METHONTOLOGY describe knowledge acquisition as a process that lasts throughout the ontology engineering process, yet it is not always of the same intensity. Early engineering process steps are richer in knowledge acquisition, classification and modelling. The main goal of this step is to identify sources of knowledge used as input for the remaining steps and to extract and acquire the knowledge necessary for successfully engineering the planned ontology.

This ontology's primary source of concepts, information, and knowledge is the MAMbO5 ontology presented in more detail in \cite{okresaduric2019MAMbO5NewOntology}. MAMbO5 results from an earlier collaboration instance of the sending and host institution, particularly this mobility's young researcher and the hosting research institute. The main goal of that ontology is to provide concepts related to modelling a multiagent system as an \ac{IVE}, boosted with concepts used in describing the organisational features of a system of agents. An \ac{IVE} in this context is a virtual system that can be seen as a model of a real system comprising agents, artefacts, and many other concepts related to the two. The agent and the artefact concepts are expected to be specialised for specific application areas when modelling a domain-specific scenario.

The purpose of \magoontologyname ontology is to enable modelling a \ac{MAS} in a way that is translatable into implementation, specifically in the SPADE-based implementation foundation of the modelled system. To do so, some concepts of the MAMbO5 ontology have to be modified, and some added, while the rest of the concepts can be left in the ontology for expressiveness and comprehensiveness. For knowledge acquisition in this part of the planned research and enhancing collaboration with the host institute, guided meetings have been performed, followed by structured brainstorming sessions and research plans, with the research team of Dr Carrascosa. Since the goal of this part of the planned research is aligned with a part of the research performed by Dr Carrascosa and his team, the rest of the planned research of this phase is conducted in cooperation with them.

Building on the MAMbO5 ontology, the \magoontologyname ontology is planned to comply with the digital twin concept and the idea of containing concepts applicable to instantiating a \ac{MAS} based on the model expressed using the developed ontology. Therefore, it must include some concepts related to the implementation domain, e.g., describing agents' internal variables or the knowledge models used. The following is an overview of the selection of the more interesting concepts, followed by a selection of the concepts that have to be added. Both the described tables include the concepts identified as such and are not necessarily exhaustive.


\section{Glossary of Terms}\label{sec: Glossary of Terms}

A \ac{GT} is a critical resource that defines and organizes key concepts, entities, and relationships within the modelled domain. This glossary forms the foundation for the ontology by ensuring that all participants in the development process share a common understanding of the domain's terminology. 

The glossary is created to maintain conceptual clarity across the ontology. It avoids duplicate or overlapping concepts, ensuring every term is clearly defined. Additionally, \ac{GT} serves as a stepping stone toward formalization, wherein these terms and relationships will later be translated into more structured, formal representations, such as in description logics or other ontology languages (e.g., OWL, RDF).

The \ac{GT} typically includes:

\begin{itemize}
    \item Concepts and definitions: A list of core terms representing the domain's important entities or phenomena. Each term is clearly defined to avoid ambiguity and to ensure consistency throughout the ontology.

    \item Relationships between concepts: The glossary may also describe how different concepts are interrelated. For example, it can specify hierarchies (e.g., subclasses), associations, or dependencies among the terms.

    \item Attributes and characteristics: Each concept might include specific attributes that describe it in greater detail. These attributes help formalize the ontology during later phases.
\end{itemize}

By ensuring that all relevant concepts are properly identified and explained, the \ac{GT} helps to streamline the ontology's development process and ensures that the final product accurately reflects the domain's knowledge structure.

The following list of 
\crefrange{gt: Acquisition}{gt: Workspace}
encompasses definitions and descriptions of the most relevant terms that can be found in the MAMBO5 ontology and the ontologies it was built upon. Other features, such as attributes and explicitly stated relationships between concepts, are not stated here since the concepts of the \ac{GT} must be filtered before being added to the developed ontology. The content of the tables is directly derived from the author's doctoral thesis \cite{okresaduric2019OrganizationalModelingLargeScale}.

\csvreader[
    separator=semicolon,
    head to column names,
    filter equal={\Include}{1},
    ]{DD.csv}{}% use head of csv as column names
{
\begin{table}[h]
    \centering
    \caption{\emph{\ConceptName} glossary entry}
    \label{gt: \ConceptName}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{0.25\linewidth}|p{0.71\linewidth}}
        \toprule
        \textbf{Concept name} & \ConceptName \if \Symbol \else \space(\Symbol) \fi \\
        \if \Synonyms
            \else 
                \textbf{Synonyms} & \Synonyms\\
            \fi
        \midrule \textbf{Definition} & \Definition \\\noalign{\vskip 2mm}
        \textbf{Description} & \Description\\
        \bottomrule
            
    \end{tabular*}
\end{table}
}