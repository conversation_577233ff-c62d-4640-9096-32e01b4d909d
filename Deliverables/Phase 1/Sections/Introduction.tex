\chapter*{Introduction}

The development and engineering processes of the ontology described here follow the steps defined by the METHONTOLOGY ontology engineering methodology \cite{fernandez-lopez1997METHONTOLOGYOntologicalArt, okresaduric2019OrganizationalModelingLargeScale}. This is the methodology of choice because it is very well defined, using clearly described steps to engineer the desired ontology. This and a dozen other ontology engineering methodologies were analysed more thoroughly in preparation for the author's doctoral thesis \cite{okresaduric2019OrganizationalModelingLargeScale}.

This chosen ontology engineering methodology is described as having a set number of steps,%
\lookAt{\cref{fig:methontology steps}}
each described in detail by the authors of the methodology. In addition, steps related to the entire life cycle of an ontology are identified. An ontology life cycle is described by \citeauthor{iqbal2013AnalysisOntologyEngineering} as \blockquote[{\cite[p. 2997]{iqbal2013AnalysisOntologyEngineering}}]{\textelp{a} set of stages through which the ontology moves during its life.} 

\begin{figure}
    \resizebox{\linewidth}{!}{\input{Deliverables/Phase 1/Figures/methontology steps}}
    \caption{Basic steps of METHONTOLOGY ontology engineering methodology, reproduced from \cite{okresaduric2019OrganizationalModelingLargeScale}, adapted from \cite{fernandez-lopez1997METHONTOLOGYOntologicalArt}}
    \label{fig:methontology steps}
\end{figure}

The rest of this document is structured according to the defined METHONTOLOGY steps, representing the documentation process. First, the ontology specification%
\lookAt{\cref{ch: Specification}}
document is described and given, followed by the description of knowledge acquisition%
\lookAt{\cref{ch: Knowledge Acquisition}}
as an ongoing process.
Conceptualisation%
\lookAt{\cref{ch: Conceptualisation}}
originates from the defined specification and is the input to formalisation.%
\lookAt{\cref{ch: Formalisation}}
Once the ontology is formalised, it can be integrated%
\lookAt{\cref{ch: Integration}}
with other ontologies of similar domains. Finally, the ontology is implemented and maintained.%
\lookAt{\cref{ch: Implementation}}
The ontology is evaluated in phase P1.2 of this part of this research.%
\lookAt{\cref{ch: Evaluation}}
Each methodology step is briefly described at the beginning of the related chapter.