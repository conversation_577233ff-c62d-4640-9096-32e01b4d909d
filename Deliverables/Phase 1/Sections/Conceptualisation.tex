\chapter{Conceptualisation}\label{ch: Conceptualisation}

% Defined Glossary of Terms, Data Dictionary visualised using a concepts classification tree, and Verbs Dictionary visually represented using a verbs diagram.

The Conceptualisation phase in ontology development transforms the collected knowledge into a structured and meaningful model. During this phase, the key concepts, relationships, attributes, and rules of the domain are identified and organized to reflect the domain's structure. The goal is to create a clear and coherent conceptual model that captures the domain's essential elements without formalizing them into specific ontology languages. The conceptual model produced in this phase is not yet formalized but provides a foundational blueprint for the subsequent formalization and implementation phases of the ontology development process



\section{Applicable Concepts}

\newthought{Agent}
    \marginnote{Overview of the select concepts contained in MAMbO5 ontology}%
\blockquote[{{\cite[p. 54]{russell2022ArtificialIntelligenceModern}}}]{An agent is anything that can be viewed as perceiving its environment through sensors and acting upon that environment through actuators.} 

\newthought{Organisational unit} An organisational unit is an \mintedInline{Agent} subclass that is a part of an organisation. Usually, a \ac{MAS} can be considered an organisation comprising multiple agents. Strictly speaking, it would have to feature and use some other concepts related to defining, for example, an organisational structure. An organisational unit can be a part of another organisational unit and consist of organisational units. This feature, similar to the concept of holons and holarchy, allows for modelling organisations on different levels of abstraction or hierarchy. Each \mintedInline{OrganisationalUnit} instance is expected, at any given point in time, to be able to enact one of a set of \mintedInline{Role} individuals at its disposal. 

\newthought{Behaviour} The concept of agent behaviour is described in the \mintedInline{ooooaflsmas} ontology as \blockquote{\textelp{} some kind of activity performed by some agent. It has to be acceptable by a normative system the agent belongs to.} In terms of SPADE implementation, behaviour is the most basic way of implementing the operations of an agent. Each agent can have multiple behaviours. Types of behaviours offered by default by SPADE can already be found in the \mintedInline{ooooaflsmas} ontology,
\lookAt{\cref{fig: behaviour subclasses in ooooaflsmas}}%
i.e., one-shot, periodic, and finite state behaviour that acts according to the principles of finite automata. Each \mintedInline{Behaviour} individual is expected to provide its \mintedInline{Agent} individual with the ability to achieve a certain objective. Some innate \mintedInline{Behaviour} individuals will be available to specific \mintedInline{Agent} subclasses by default.

\begin{figure}
    \centering
    \includegraphics[width=0.84\linewidth]{Deliverables/Phase 1/Figures/Behaviours in ooooaflsmas.png}
    \caption{Subclasses of the \mintedInline{Behavior} concept in \mintedInline{ooooaflsmas}}
    \label{fig: behaviour subclasses in ooooaflsmas}
\end{figure}

The \magoontologyname ontology is specialised for the context of developing \ac{SPADE} agents, wherefore it comprises only the behaviour concepts relevant to the behaviours explicitly implementable using \ac{SPADE}.

\begin{figure}
    \centering
    \includegraphics[width=1\linewidth]{Deliverables/Phase 1/Figures/behaviour subclasses.png}
    \caption{Subclasses of the class \mintedInline{Behaviour} in \magoontologyname ontology}
    \label{fig: behaviour subclasses in MAGO}
\end{figure}

\newthought{Role} The concept of a role is described within the \mintedInline{ooooaflsmas} ontology as a \blockquote{prescribed or expected behavior associated with a particular position or status in a group or organization}. This concept is in the mentioned ontology designated as a direct subclass of the concept \mintedInline{Norm}, derived from the domain of organisational modelling and describing organisation systems. The \mintedInline{Norm} concept is defined therein as \blockquote{(socially) accepted behavior in a defined group and \textins{they} represent a blueprint for behaving in said group.} Based on the stated, the concept of \mintedInline{Role} is interesting because it enables defining a set of features that will be put at the agent's disposal playing the chosen role. In other words, roles can be used as a way of combining different features that can be enacted by an agent. 

The \mintedInline{Role} concept was modelled in \cite{okresaduric2019OrganizationalModelingLargeScale} as a concept which was related to (possibly) several instances of concepts describing behaviours and objectives, meaning that specific roles allow agents who enact them to attain a specific set of behaviours
that enables and empowers them to achieve specific objectives, thus solving specific tasks. Different objectives demand the enactment of various roles from the pool of roles available (defined) in the system. 

Within the \magoontologyname ontology,
\lookAt{\cref{fig: roles achieve objectives and enable knowledge}}%
the concept of a role is used to group features and individuals available to various agents as a \enquote{package}, i.e. a set of features that is available to various individual agents. 

\begin{figure}
    \centering
    \input{Deliverables/Phase 1/Figures/roles achieve objectives and enable knowledge}
    \caption{A \mintedInline{Role} individual can provide some \mintedInline{Behaviour} individuals and can enable access to some \mintedInline{Knowledge} individuals}
    \label{fig: roles achieve objectives and enable knowledge}
\end{figure}

\newthought{Artefact} An artefact is used in the \mintedInline{JaCalIVE} ontology as a comprehensive concept encompassing all interactive and non-interactive objects that are not suitable to be implemented as agents but should be present in the modelled system nonetheless. \mintedInline{MAMbO5} ontology recognises specific versions of artefacts as an \mintedInline{IVE_Artifact}, which is located within the \ac{IVE}, and its even more specific version \mintedInline{Physical_Artifact}, which can be physically represented or is expected to be physically represented, and a complementary specification given as a \mintedInline{KnowledgeArtifact} that is an artefact that is abstract and describes various rules that can be found within the modelled system. Its initial authors describe the latter concept as encompassing \enquote{a wide range of explicit knowledge,} including, but not limited to, knowledge models, such as machine learning models or neural networks. The \mintedInline{Norm} concept and its subclass \mintedInline{Role} are subclasses of the \mintedInline{KnowledgeArtifact} concept,
\lookAt{\cref{fig: Hierarchy artefact KNartefact norm role}}%
as defined in \mintedInline{ooooaflsmas}.

The \magoontologyname ontology introduces the concept of a \mintedInline{Software Artefact} as well, as a specific type of an \mintedInline{Artefact}. This concept describes artefacts that can be used by the system, usually by means of API endpoints, and can encompass different web services, including the XMPP server that is necessary for instantiating SPADE agents.

\begin{figure}
    \centering
    \includegraphics[width=1\linewidth]{Deliverables/Phase 1/Figures/software artefact.png}
    \caption{Class hierarchy on subclasses of the \mintedInline{Artifact} concept in \magoontologyname ontology}
    \label{fig: Hierarchy artefact KNartefact norm role}
\end{figure}

\newthought{Strategy} is modelled within the \magoontologyname ontology using several related concepts. In short, specific objectives can be achieved by conducting a set of actions, i.e. a plan, whereby individual actions are implemented using agent behaviours, while specific behaviours are provided by \mintedInline{Role} individuals. Modelling these concepts and the accompanying relationships within the ontology and translating those relationships to agent implementations eases the process of implementing agents that can play specific roles based on the goals they are faced with. Therefore, agents may be implemented as intelligent in terms of choosing the roles that can enable them to perform specific behaviours that will, in turn, help them achieve specific objectives they are faced with.

\newthought{Workspace} is a concept that represents a group of various elements, i.e. a union of all the elements of a system. In the context of \magoontologyname, a workspace represents the immediate neighbourhood of a number of agents. Multiple \mintedInline{Workspace} individuals can, therefore, exist within a single system. The ultimate goal of the \magoontologyname is to enable \mintedInline{Agent} instances to move between workspaces based on their neighbours, i.e. based on the intensity and frequency of their communication with other agents. For example, should agent Alice communicate more often with agent Charles, who is not in Alice's workspace, than with agent Bob, who is in Alice's workspace, then Alice might want to move to Charles' workspace, which might be more efficient in the long run.

% \newthought{Stage} By definition, an \mintedInline{Agent} is located in an environment. Such an environment can comprise multiple agents and other entities, such as instances of the \mintedInline{Artefact} concept. During one of the brainstorming sessions on the topic of this research with the colleagues of the host institution (\acf{VRAIN} at the \ac{UPV}), a more specific environmental concept was discussed. Since the system described by this ontology is planned to be able to implement several more specific environments of the same domain, it is deemed useful to define a concept describing a smaller-scale environment. This is where the \mintedInline{Stage} concept comes in. In the context of this research, a stage should represent a smaller-scale environment, a specialised version of the general environment, that is related to the general domain of the modelled system.
% Furthermore, a \mintedInline{Stage} individual groups related agents and artefacts together. It is possible to think of an individual of the \mintedInline{Stage} concept as a room or stage where a specific set of activities or processes is conducted. A single stage is envisioned as comprising of some \mintedInline{Agent} individuals.



\section{Data Dictionary}

The following are the descriptions and definitions of the 
\lookAt{\crefrange{dd: Action}{dd: Workspace}}%
key concepts and a selection of relationships
\lookAt{\crefrange{dd: can play role}{dd: has behaviour}}%
that can be found in the \magoontologyname ontology.

\begin{figure*}
    \centering
    \includegraphics[width=1\linewidth]{Deliverables/Phase 1/Figures/MAGO Ag classes.png}
    \caption{Concepts and their subconcepts in the \magoontologyname ontology}
    \label{fig: MAGO ag concepts}
\end{figure*}

\csvreader[
    separator=semicolon,
    head to column names,
    filter equal={\Include}{1},
    ]{DDConcepts.csv}{}% use head of csv as column names
{
\begin{table}[h]
    \centering
    \caption{\emph{\ConceptName} concept description}
    \label{dd: \ConceptName}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{0.25\linewidth}|p{0.71\linewidth}}
        \toprule
        \textbf{Concept name} & \ConceptName \\
        \midrule \textbf{Definition} & \Definition \\\noalign{\vskip 2mm}
        \textbf{Description} & \Description\\
        \bottomrule    
    \end{tabular*}
\end{table}
}

\csvreader[
    separator=semicolon,
    head to column names,
    filter equal={\Include}{1},
    ]{DDRelationships.csv}{}% use head of csv as column names
{
\begin{table}[h]
    \centering
    \caption{\emph{\ConceptName} relationship description}
    \label{dd: \ConceptName}
    \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}}p{0.25\linewidth}|p{0.71\linewidth}}
        \toprule
        \textbf{Concept name} & \ConceptName \\
        % \midrule \textbf{Definition} & \Definition \\\noalign{\vskip 2mm}
        \textbf{Description} & \Description\\
        \bottomrule    
    \end{tabular*}
\end{table}
}