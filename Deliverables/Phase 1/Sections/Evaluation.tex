\chapter{Evaluation}\label{ch: Evaluation}

The evaluation phase in ontology engineering focuses on verifying and validating the ontology's design, structure, and functionality. This phase ensures that the ontology accurately represents the intended domain and operates effectively within its application context. The evaluation phase helps identify potential improvements and confirms that the ontology is ready for deployment by systematically assessing factors such as accuracy, completeness, and operational efficiency. This quality control process establishes the ontology as a reliable and robust knowledge resource aligned with user requirements and technical expectations. Evaluation often involves both automated testing tools and manual reviews by domain experts.

Several activities are a part of the evaluation phase of the methodology used in this ontology engineering process. 
Quality assessment evaluates the ontology’s internal consistency, logical coherence, and adherence to domain knowledge.
It is tested against the intended scope regarding concepts and relationships to ensure the ontology is complete and accurate. 
The ontology's efficiency, scalability, and responsiveness are tested when integrated into the target environment.
Validation against the specification is performed in order to verify that the ontology can address the features and requests defined during the specification phase, confirming its suitability for its intended applications.

The \magoontologyname was evaluated by the domain experts from the host institution using the applicable interview and review approaches. Furthermore, the ontology was tested against the specification document.
\lookAt{\cref{ch: Specification}}%
In addition to the stated, the ontology was, in part, presented in its finalised state at a professional conference \cite{okresaduric2024OntologyActionStreamlining}.