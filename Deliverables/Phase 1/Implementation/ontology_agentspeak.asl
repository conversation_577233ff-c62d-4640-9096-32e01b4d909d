+concept(behaviour).
+concept(individual knowledge artefact).
+concept(role).
+concept(finite state machine behaviour).
+concept(system).
+concept(artefact resource).
+concept(action).
+concept(software artefact).
+concept(one shot behaviour).
+concept(cyclic behaviour).
+concept(periodic behaviour).
+concept(artefact).
+concept(time-out behaviour).
+concept(restapi endpoint).
+concept(plan).
+concept(organisational knowledge artefact).
+concept(strategy).
+concept(state behaviour).
+concept(agent).
+concept(recipe agent).
+concept(agent host server).
+concept(factory agent).
+concept(knowledge artefact).
+concept(objective).
+relation(is achieved by action, objective, action).
+relation(has objective, action, objective).
+relation(is provided by, artefact resource, artefact).
+relation(provides resource, artefact, artefact resource).
+relation(is required in plan, action, plan).
+relation(requires action, plan, action).
+relation(hosts, agent host server, agent).
+relation(lives on host, agent, agent host server).
+relation(implements action, behaviour, action).
+relation(is implemented using behaviour, action, behaviour).
+relation(has final objective, objective, objective).
+relation(is before objective, objective, objective).
+relation(is after objective, objective, objective).
+relation(is after state, state behaviour, state behaviour).
+relation(is before state, state behaviour, state behaviour).
+relation(features role, role, role).
+relation(is after action, action, action).
+relation(is before action, action, action).
+relation(is part of process, plan, plan).
+relation(is part of role, role, role).
+relation(plays role, agent, role).
+relation(can play role, agent, role).
+relation(has initial state, finite state machine behaviour, state behaviour).
+relation(is provided by role, behaviour, role).
+relation(provides behaviour, role, behaviour).
+relation(has final state, finite state machine behaviour, state behaviour).
+relation(is part of objective, objective, objective).
+relation(has initial objective, objective, objective).
+relation(features objective, objective, objective).
+relation(features process, plan, plan).
+individual(register).
+individual(recipe 003).
+individual(personal ontology).
+individual(service consumer).
+individual(interlocutor).
+individual(localhost).
+individual(receive message objective).
+individual(secondary ontology).
+individual(request service action).
+individual(request service objective).
+individual(request service state).
+individual(service provider).
+individual(communicate action).
+individual(send message objective).
+individual(receive messages).
+individual(send message).
+individual(consume service state).
+individual(produce part).
+individual(find service action).
+individual(consume service action).
+individual(find service objective).
+individual(find service state).
+individual(produce part behaviour).
+individual(main ontology).
+individual(recipe 001).
+individual(consume service objective).
+individual(factory b).
+individual(communicate).
+individual(factory c).
+individual(recipe 002).
+individual(rec.foi.hr).
+individual(factory a).
+individual_relation(lives on host, recipe 003, localhost).
+individual_relation(can access artefact, recipe 003, personal ontology).
+individual_relation(can play role, recipe 003, service consumer).
+individual_relation(can play role, recipe 003, interlocutor).
+individual_relation(can access artefact, service consumer, secondary ontology).
+individual_relation(can access artefact, service consumer, main ontology).
+individual_relation(provides behaviour, service consumer, produce part behaviour).
+individual_relation(can access artefact, interlocutor, secondary ontology).
+individual_relation(provides behaviour, interlocutor, receive messages).
+individual_relation(provides behaviour, interlocutor, send message).
+individual_relation(hosts, localhost, recipe 003).
+individual_relation(hosts, localhost, recipe 001).
+individual_relation(hosts, localhost, factory b).
+individual_relation(hosts, localhost, factory c).
+individual_relation(hosts, localhost, recipe 002).
+individual_relation(hosts, localhost, factory a).
+individual_relation(is achieved by action, receive message objective, communicate action).
+individual_relation(has objective, request service action, request service objective).
+individual_relation(is required in plan, request service action, produce part).
+individual_relation(is implemented using behaviour, request service action, request service state).
+individual_relation(is achieved by action, request service objective, request service action).
+individual_relation(implements action, request service state, request service action).
+individual_relation(is after state, request service state, find service state).
+individual_relation(is before state, request service state, consume service state).
+individual_relation(has objective, communicate action, receive message objective).
+individual_relation(has objective, communicate action, send message objective).
+individual_relation(is required in plan, communicate action, communicate).
+individual_relation(is implemented using behaviour, communicate action, receive messages).
+individual_relation(is implemented using behaviour, communicate action, send message).
+individual_relation(is achieved by action, send message objective, communicate action).
+individual_relation(implements action, receive messages, communicate action).
+individual_relation(is provided by role, receive messages, interlocutor).
+individual_relation(implements action, send message, communicate action).
+individual_relation(is provided by role, send message, interlocutor).
+individual_relation(implements action, consume service state, consume service action).
+individual_relation(is after state, consume service state, request service state).
+individual_relation(is before state, consume service state, find service state).
+individual_relation(requires action, produce part, request service action).
+individual_relation(requires action, produce part, find service action).
+individual_relation(requires action, produce part, consume service action).
+individual_relation(has objective, find service action, find service objective).
+individual_relation(is required in plan, find service action, produce part).
+individual_relation(is implemented using behaviour, find service action, find service state).
+individual_relation(has objective, consume service action, consume service objective).
+individual_relation(is required in plan, consume service action, produce part).
+individual_relation(is implemented using behaviour, consume service action, consume service state).
+individual_relation(is achieved by action, find service objective, find service action).
+individual_relation(implements action, find service state, find service action).
+individual_relation(is after state, find service state, consume service state).
+individual_relation(is before state, find service state, request service state).
+individual_relation(has initial state, produce part behaviour, find service state).
+individual_relation(is provided by role, produce part behaviour, service consumer).
+individual_relation(has final state, produce part behaviour, consume service state).
+individual_relation(lives on host, recipe 001, localhost).
+individual_relation(can play role, recipe 001, interlocutor).
+individual_relation(is achieved by action, consume service objective, consume service action).
+individual_relation(lives on host, factory b, localhost).
+individual_relation(can access artefact, factory b, personal ontology).
+individual_relation(can play role, factory b, service provider).
+individual_relation(can play role, factory b, interlocutor).
+individual_relation(requires action, communicate, communicate action).
+individual_relation(lives on host, factory c, localhost).
+individual_relation(can play role, factory c, service provider).
+individual_relation(can play role, factory c, interlocutor).
+individual_relation(lives on host, recipe 002, localhost).
+individual_relation(can play role, recipe 002, service consumer).
+individual_relation(can play role, recipe 002, interlocutor).
+individual_relation(lives on host, factory a, localhost).
+individual_relation(can access artefact, factory a, main ontology).
+individual_relation(can access artefact, factory a, personal ontology).
+individual_relation(can play role, factory a, service provider).
+individual_relation(can play role, factory a, interlocutor).