<?xml version="1.0"?>
<Ontology xmlns="http://www.w3.org/2002/07/owl#"
     xml:base="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     ontologyIRI="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45">
    <Prefix name="" IRI="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45/"/>
    <Prefix name="owl" IRI="http://www.w3.org/2002/07/owl#"/>
    <Prefix name="rdf" IRI="http://www.w3.org/1999/02/22-rdf-syntax-ns#"/>
    <Prefix name="xml" IRI="http://www.w3.org/XML/1998/namespace"/>
    <Prefix name="xsd" IRI="http://www.w3.org/2001/XMLSchema#"/>
    <Prefix name="rdfs" IRI="http://www.w3.org/2000/01/rdf-schema#"/>
    <Declaration>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
    </Declaration>
    <Declaration>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
    </Declaration>
    <Declaration>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7"/>
    </Declaration>
    <Declaration>
        <NamedIndividual IRI="#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57"/>
    </Declaration>
    <ClassAssertion>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52"/>
        <NamedIndividual IRI="#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7"/>
    </ClassAssertion>
    <ClassAssertion>
        <Class IRI="#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b"/>
        <NamedIndividual IRI="#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57"/>
    </ClassAssertion>
    <ObjectPropertyAssertion>
        <Annotation>
            <AnnotationProperty abbreviatedIRI="rdfs:comment"/>
            <Literal>self</Literal>
        </Annotation>
        <ObjectProperty IRI="#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6"/>
        <NamedIndividual IRI="#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947"/>
        <NamedIndividual IRI="#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3"/>
    </ObjectPropertyAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_e0610148_4079_42f9_8a5f_9c746d95b04b</IRI>
        <Literal xml:lang="en">Person</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLClass_e709be43_faa4_4caa_b2a3_df3be3499b52</IRI>
        <Literal xml:lang="en">Dog</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_3127dd68_4db5_49c6_beaa_74781f2f19e3</IRI>
        <Literal xml:lang="en">Dusty</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_37a548ba_0ef4_4550_af8e_ab30e0b2c947</IRI>
        <Literal xml:lang="en">Alice</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_80ccbf39_9913_4c40_a6e6_a27cf3270a83</IRI>
        <Literal xml:lang="en">Charles</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_bb92cf38_4ec1_4407_a3d7_fd691eeeaaa7</IRI>
        <Literal xml:lang="en">Rex</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLNamedIndividual_ecf1b7ec_7c2e_4894_807f_861fdae1ca57</IRI>
        <Literal xml:lang="en">Bob</Literal>
    </AnnotationAssertion>
    <AnnotationAssertion>
        <AnnotationProperty abbreviatedIRI="rdfs:label"/>
        <IRI>#OWLObjectProperty_69420103_2d27_4584_92d2_bea6fcf446d6</IRI>
        <Literal xml:lang="en">is owner of</Literal>
    </AnnotationAssertion>
</Ontology>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

