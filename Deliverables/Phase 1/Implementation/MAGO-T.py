import asyncio
import pickle
import hashlib
import owlready2
from owlready2 import get_ontology
from spade.agent import Agent
from spade.behaviour import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FSMBehaviour, State, OneShotBehaviour
from spade.message import Message
from spade.template import Template

# States for the FSM
READ_MESSAGE_STATE = "READ_MESSAGE"
ANALYSE_MESSAGE_STATE = "ANALYSE_MESSAGE"


class ReceiveMessagesBehaviour(CyclicBehaviour):
    """
    Cyclic behaviour that receives messages with a timeout and stores them in agent.inbox.
    """

    async def run(self):
        # Wait for a message with a timeout of 10 seconds
        msg = await self.receive(timeout=10)
        if msg:
            # Store message in the agent's inbox
            self.agent.inbox.append(msg)
            # Also trigger the send one-shot behaviour if agent.outbox is updated externally
        # No explicit else needed; if no message, loop again


class ReadMessageState(State):
    """
    FSM state that reads the latest message from the inbox.
    """

    async def run(self):
        if self.agent.inbox:
            # Get the latest message
            self.current_message = self.agent.inbox.pop(0)
            print(f"{self.agent.name}: I'm looking at message {self.current_message}")
            # Store it temporarily in the agent for analysis
            self.agent.current_fsm_message = self.current_message
            self.set_next_state(ANALYSE_MESSAGE_STATE)
        else:
            # No message, just stay in the same state or maybe wait a bit
            await asyncio.sleep(1)
            self.set_next_state(READ_MESSAGE_STATE)


class AnalyseMessageState(State):
    """
    FSM state that analyses the message and then transitions back to reading.
    """

    async def run(self):
        msg = self.agent.current_fsm_message
        if msg:
            # Do some analysis of the message body
            print(f"[{self.agent.jid}] Analysing message: {msg.body}")
            if hasattr(self.agent, "check_message_if_agent"):
                self.agent.check_message_if_agent(msg)
            # After analysis go back to read another message
        self.set_next_state(READ_MESSAGE_STATE)


class SendMessagesBehaviour(OneShotBehaviour):
    """
    One-shot behaviour that sends all messages from the outbox.
    Triggered whenever the outbox is updated (user-defined logic to trigger this).
    """

    async def run(self):
        while self.agent.outbox:
            msg = self.agent.outbox.pop(0)
            await self.send(msg)
            print(f"[{self.agent.jid}] Sent a message to {msg.to}")

def load_ontology_world(ontology_path: str):
    world = owlready2.World()
    ontology = world.get_ontology(ontology_path).load()
    return ontology, world


class AliceAgent(Agent):
    def __init__(self, jid, password):
        super().__init__(jid, password)
        self.inbox = []
        self.outbox = []
        self.current_fsm_message = None

    async def setup(self):
        print(f"{self.name} running!")
        # Set template for receiving messages: only 'inform' or 'request'
        template_1 = Template()
        template_1.metadata = {"performative": "inform"}
        template_2 = Template()
        template_2.metadata = {"performative": "request"}
        receive_b = ReceiveMessagesBehaviour()
        self.add_behaviour(receive_b, template_1 | template_2)

        # Set up FSM
        fsm = FSMBehaviour()
        fsm.add_state(name=READ_MESSAGE_STATE, state=ReadMessageState(), initial=True)
        fsm.add_state(name=ANALYSE_MESSAGE_STATE, state=AnalyseMessageState())
        fsm.add_transition(source=READ_MESSAGE_STATE, dest=ANALYSE_MESSAGE_STATE)
        fsm.add_transition(source=ANALYSE_MESSAGE_STATE, dest=READ_MESSAGE_STATE)
        fsm.add_transition(source=READ_MESSAGE_STATE, dest=READ_MESSAGE_STATE)
        # self.current_fsm_message is used internally by these states
        self.add_behaviour(fsm)

        # This one-shot behaviour is instantiated but not scheduled yet.
        # We'll call it when needed.
        self.send_behaviour = SendMessagesBehaviour()

        self.ontology, self.world = load_ontology_world("https://github.com/AILab-FOI/MAGO/raw/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx")

        print(self.__dict__)

        # Alice sends her agent object to Bob
        # await self.send_copy_object("bob@localhost")

    async def send_self_object(self, recipient_jid):
        # Pickle the agent object (self)
        pickled_data = pickle.dumps(self)
        print(f"{self.name}: Pickled object {pickled_data}")

        # Compute SHA-256 hash
        sha256_hash = hashlib.sha256(pickled_data).hexdigest()
        print(f"{self.name}: Hash {sha256_hash}")

        # Create a message with pickled data and hash
        msg = Message(to=recipient_jid)
        msg.set_metadata("performative", "inform")
        # We can store both as a dictionary or separate fields.
        # Let's just concatenate or create a simple protocol, e.g.:
        # body = PICKLED_DATA_BASE64::SHA256_HASH
        # However, since pickled data is binary, let's just send it as a base64 encoded string:
        import base64

        encoded_data = base64.b64encode(pickled_data).decode("utf-8")
        msg.body = f"{encoded_data}::{sha256_hash}"
        print(f"{self.name}: Sending message {msg}")

        # Add to outbox and trigger the sending behaviour
        self.outbox.append(msg)
        await self.add_behaviour(self.send_behaviour)

    async def send_agent_object(self, recipient_jid):
        # Pickle the agent object (self)
        agent_object = Agent(str(self.jid.bare()), self.password)

        # Transfer all attributes from source_obj to dest_obj
        for attr_name, attr_value in self.__dict__.items():
            setattr(agent_object, attr_name, attr_value)
        
        pickled_data = pickle.dumps(agent_object)
        print(f"{self.name}: Pickled object {pickled_data}")

        # Compute SHA-256 hash
        sha256_hash = hashlib.sha256(pickled_data).hexdigest()
        print(f"{self.name}: Hash {sha256_hash}")

        # Create a message with pickled data and hash
        msg = Message(to=recipient_jid)
        msg.set_metadata("performative", "inform")
        # We can store both as a dictionary or separate fields.
        # Let's just concatenate or create a simple protocol, e.g.:
        # body = PICKLED_DATA_BASE64::SHA256_HASH
        # However, since pickled data is binary, let's just send it as a base64 encoded string:
        import base64

        encoded_data = base64.b64encode(pickled_data).decode("utf-8")
        msg.body = f"{encoded_data}::{sha256_hash}"
        print(f"{self.name}: Sending message {msg}")

        # Add to outbox and trigger the sending behaviour
        self.outbox.append(msg)
        await self.add_behaviour(self.send_behaviour)

    async def send_copy_object(self, recipient_jid):
        # Pickle the agent object (self)
        agent_object = Agent(str(self.jid.bare()), self.password)
        agent_copy = copy.deepcopy(agent_object)
        
        pickled_data = pickle.dumps(agent_copy)
        print(f"{self.name}: Pickled object {pickled_data}")

        # Compute SHA-256 hash
        sha256_hash = hashlib.sha256(pickled_data).hexdigest()
        print(f"{self.name}: Hash {sha256_hash}")

        # Create a message with pickled data and hash
        msg = Message(to=recipient_jid)
        msg.set_metadata("performative", "inform")
        # We can store both as a dictionary or separate fields.
        # Let's just concatenate or create a simple protocol, e.g.:
        # body = PICKLED_DATA_BASE64::SHA256_HASH
        # However, since pickled data is binary, let's just send it as a base64 encoded string:
        import base64

        encoded_data = base64.b64encode(pickled_data).decode("utf-8")
        msg.body = f"{encoded_data}::{sha256_hash}"
        print(f"{self.name}: Sending message {msg}")

        # Add to outbox and trigger the sending behaviour
        self.outbox.append(msg)
        await self.add_behaviour(self.send_behaviour)


class BobAgent(Agent):
    def __init__(self, jid, password):
        super().__init__(jid, password)
        self.inbox = []
        self.outbox = []
        self.current_fsm_message = None

    async def setup(self):
        print(f"{self.name} running!")
        template_1 = Template()
        template_1.metadata = {"performative": "inform"}
        template_2 = Template()
        template_2.metadata = {"performative": "request"}
        receive_b = ReceiveMessagesBehaviour()
        self.add_behaviour(receive_b, template_1 | template_2)

        # Set up FSM
        fsm = FSMBehaviour()
        fsm.add_state(name=READ_MESSAGE_STATE, state=ReadMessageState(), initial=True)
        fsm.add_state(name=ANALYSE_MESSAGE_STATE, state=AnalyseMessageState())
        fsm.add_transition(source=READ_MESSAGE_STATE, dest=ANALYSE_MESSAGE_STATE)
        fsm.add_transition(source=ANALYSE_MESSAGE_STATE, dest=READ_MESSAGE_STATE)
        fsm.add_transition(source=READ_MESSAGE_STATE, dest=READ_MESSAGE_STATE)
        self.add_behaviour(fsm)

        self.send_behaviour = SendMessagesBehaviour()

    async def run_received_agent(self, agent_obj):
        # For demonstration, just print a message
        # In principle, you could start the received agent if it's a SPADE Agent instance.
        print(f"[{self.jid}] Received a valid agent object: {agent_obj.jid}")
        print(f"[{self.jid}] I can now run it or do something with it.")

    async def check_message_if_agent(self, msg):
        if msg.metadata.get("performative") == "inform" and "::" in msg.body:
            encoded_data, received_hash = msg.body.split("::")
            import base64

            pickled_data = base64.b64decode(encoded_data)

            # Verify hash
            calculated_hash = hashlib.sha256(pickled_data).hexdigest()
            if calculated_hash == received_hash:
                print(f"[{self.jid}] Hash verified successfully!")
                # Unpickle and check if it's a SPADE agent
                obj = pickle.loads(pickled_data)
                if isinstance(obj, Agent):
                    await self.run_received_agent(obj)
                else:
                    print(f"[{self.jid}] Received object is not an Agent.")
            else:
                print(f"[{self.jid}] Hash mismatch! Data corrupted.")

    async def on_message(self, msg):
        # This hook can be used instead of relying only on FSM for demonstration
        # Let's say if we get a message with pickled data, we verify and unpickle it here
        self.check_message_if_agent(msg)


async def main():
    # Replace these with your valid credentials and server
    alice = AliceAgent("alice@localhost", "password")
    bob = BobAgent("bob@localhost", "password")

    await bob.start()
    await alice.start()

    # Wait to see what happens
    await asyncio.sleep(10)

    await alice.stop()
    await bob.stop()


if __name__ == "__main__":
    asyncio.run(main())
