<?xml version="1.0"?>
<rdf:RDF xmlns="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#"
     xml:base="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:owlr="http://www.lesfleursdunormal.fr/static/_downloads/owlready_ontology.owl#"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#">
    <owl:Ontology rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#">
        <owl:imports rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Previous%20Work/MAMbO5.owl"/>
        <rdfs:comment>The ontology for MAGO research visit.</rdfs:comment>
    </owl:Ontology>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Annotation properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://www.lesfleursdunormal.fr/static/_downloads/owlready_ontology.owl#python_name -->

    <owl:AnnotationProperty rdf:about="http://www.lesfleursdunormal.fr/static/_downloads/owlready_ontology.owl#python_name"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Object Properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_691196cf_4ee3_4e4f_b8f0_7ee4aecc248c -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_691196cf_4ee3_4e4f_b8f0_7ee4aecc248c">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8nS5zlvvUfE9N1xZBYTpcG"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:label xml:lang="en-gb">is achieved by activity</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_78dd3dec_c844_450a_8bd0_412f69614517 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_78dd3dec_c844_450a_8bd0_412f69614517">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCWrWcZCQ3lxj6jFw9zRJ6m"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:label xml:lang="en-gb">is provided by</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_ca58846c_df5f_4584_9375_8e686fdbb3fe -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_ca58846c_df5f_4584_9375_8e686fdbb3fe">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R84IuDzXJ7QCZi1bY6fzFxZ"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:label xml:lang="en-gb">is required in plan</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_d04b28e8_062e_4b0d_82c6_a6b9f6b31226 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLObjectProperty_d04b28e8_062e_4b0d_82c6_a6b9f6b31226">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RpmDQcEqabFOwsZHv82QuP"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCdOsGdA0yrGZdnzgQesQx7"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
        <rdfs:label xml:lang="en-gb">hosts</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7MvUIWpnOdfB3dxpRXTUAK -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7MvUIWpnOdfB3dxpRXTUAK">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCkUIZx8f7TQwTrh3wmfyBD"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:label xml:lang="en-gb">implements activity</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7NTCPsMvJdxinMk2P28ppg -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7NTCPsMvJdxinMk2P28ppg">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">has final objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7o5TdA2HQOQxwVPrUJrLpq -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7o5TdA2HQOQxwVPrUJrLpq">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBntfnmvB9JVuK1xvAe6Hm8"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">is before objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R84IuDzXJ7QCZi1bY6fzFxZ -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R84IuDzXJ7QCZi1bY6fzFxZ">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <owlr:python_name>requires_activity</owlr:python_name>
        <rdfs:label xml:lang="en-gb">requires activity</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8ZWdA7HWDzIIwhQZrSwAIj -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8ZWdA7HWDzIIwhQZrSwAIj">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDGoFrovO0PhBCQGPkFeUuR"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#IrreflexiveProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <owlr:python_name>is_after_state</owlr:python_name>
        <rdfs:label xml:lang="en-gb">is after state</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8ZXgNqWa8M3RnwigsFBxvc -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8ZXgNqWa8M3RnwigsFBxvc">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:label xml:lang="en-gb">features role</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8nS5zlvvUfE9N1xZBYTpcG -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8nS5zlvvUfE9N1xZBYTpcG">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <owlr:python_name>has_objective</owlr:python_name>
        <rdfs:label xml:lang="en-gb">has objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9QlFkpwG4P6QT3YaFGdAPM -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9QlFkpwG4P6QT3YaFGdAPM">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9wYaBAe5LQpMMDI7zKHWhc"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:label xml:lang="en-gb">is after activity</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9Y2982rI3hZL4oBhv8Ohhv -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9Y2982rI3hZL4oBhv8Ohhv">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:label xml:lang="en-gb">is part of process</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9wYaBAe5LQpMMDI7zKHWhc -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9wYaBAe5LQpMMDI7zKHWhc">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:label xml:lang="en-gb">is before activity</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RB8ofKEO8zM8jcK0WetPpLI -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RB8ofKEO8zM8jcK0WetPpLI">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:domain>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <owlr:python_name>can_access_artefact</owlr:python_name>
        <rdfs:label xml:lang="en-gb">can access artefact</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBOL8y2xs4VvKc6D0kikOup -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBOL8y2xs4VvKc6D0kikOup">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <owlr:python_name>is_part_of_role</owlr:python_name>
        <rdfs:label xml:lang="en-gb">is part of role</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBmpSay8yjDZmklOv6Mw1rc -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBmpSay8yjDZmklOv6Mw1rc">
        <rdfs:subPropertyOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCcC3SXJ5MvuHMJyzs8OpU6"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <owlr:python_name>plays_role</owlr:python_name>
        <rdfs:label xml:lang="en-gb">plays role</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBntfnmvB9JVuK1xvAe6Hm8 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBntfnmvB9JVuK1xvAe6Hm8">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">is after objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RC6rldf2VsWBaHtUobIELQL -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RC6rldf2VsWBaHtUobIELQL">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDMv7ayhi9xuRAzbfALLhKS"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <owlr:python_name>has_initial_state</owlr:python_name>
        <rdfs:label xml:lang="en-gb">has initial state</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCAv5UwFPy5OUQdjN61nXbw -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCAv5UwFPy5OUQdjN61nXbw">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <owl:inverseOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RY56GiCpqJHON677qnE5sT"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <owlr:python_name>is_provided_by_role</owlr:python_name>
        <rdfs:label xml:lang="en-gb">is provided by role</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCPuCz8AunmmX3PS6A0U0XL -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCPuCz8AunmmX3PS6A0U0XL">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDMv7ayhi9xuRAzbfALLhKS"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <owlr:python_name>has_final_state</owlr:python_name>
        <rdfs:label xml:lang="en-gb">has final state</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCWrWcZCQ3lxj6jFw9zRJ6m -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCWrWcZCQ3lxj6jFw9zRJ6m">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq"/>
        <owlr:python_name>provides_resource</owlr:python_name>
        <rdfs:label xml:lang="en-gb">provides resource</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCcC3SXJ5MvuHMJyzs8OpU6 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCcC3SXJ5MvuHMJyzs8OpU6">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <owlr:python_name>can_play_role</owlr:python_name>
        <rdfs:label xml:lang="en-gb">can play role</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCkUIZx8f7TQwTrh3wmfyBD -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCkUIZx8f7TQwTrh3wmfyBD">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <owlr:python_name>has_behaviour</owlr:python_name>
        <rdfs:label xml:lang="en-gb">is implemented using behaviour</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCsnmcyqIe70G3lagH4y3bE -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCsnmcyqIe70G3lagH4y3bE">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">is part of objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDGoFrovO0PhBCQGPkFeUuR -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDGoFrovO0PhBCQGPkFeUuR">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#IrreflexiveProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
        <owlr:python_name>is_before_state</owlr:python_name>
        <rdfs:label xml:lang="en-gb">is before state</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDrm00K6AJVd5OJmoVCqPy6 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDrm00K6AJVd5OJmoVCqPy6">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">has initial objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RY56GiCpqJHON677qnE5sT -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RY56GiCpqJHON677qnE5sT">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Role"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <owlr:python_name>provides_behaviour</owlr:python_name>
        <rdfs:label xml:lang="en-gb">provides behaviour</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#ReKEcwE01zu4hSOL1gSBu2 -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#ReKEcwE01zu4hSOL1gSBu2">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective"/>
        <rdfs:label xml:lang="en-gb">features objective</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RoDHPMWwB604EEv4qQbsGC -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RoDHPMWwB604EEv4qQbsGC">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:range rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan"/>
        <rdfs:label xml:lang="en-gb">features process</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RpmDQcEqabFOwsZHv82QuP -->

    <owl:ObjectProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RpmDQcEqabFOwsZHv82QuP">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topObjectProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
        <rdfs:range rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCdOsGdA0yrGZdnzgQesQx7"/>
        <owlr:python_name>lives_on_host</owlr:python_name>
        <rdfs:label xml:lang="en-gb">lives on host</rdfs:label>
    </owl:ObjectProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Data properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_13076287_43a5_47ba_80d4_aa0b2fcbf767 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_13076287_43a5_47ba_80d4_aa0b2fcbf767">
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <rdfs:label xml:lang="en-gb">is implemented as</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_15a4602e_47e2_4459_be8c_532c6e1062ab -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_15a4602e_47e2_4459_be8c_532c6e1062ab">
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain>
            <owl:Class>
                <owl:unionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq"/>
                </owl:unionOf>
            </owl:Class>
        </rdfs:domain>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#anyURI"/>
        <owlr:python_name>has_uri</owlr:python_name>
        <rdfs:label xml:lang="en-gb">has URI</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_1dd27442_1507_4890_8c6b_89ff9a5a4f49 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_1dd27442_1507_4890_8c6b_89ff9a5a4f49">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#boolean"/>
        <owlr:python_name>is_repeating</owlr:python_name>
        <rdfs:comment>This property defines whether the behaviour is repeating or not, designating cyclic or one-shot behaviour types.</rdfs:comment>
        <rdfs:label xml:lang="en-gb">is repeating</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_70bbe987_d9bc_4de9_b4ff_891e478f46a6 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_70bbe987_d9bc_4de9_b4ff_891e478f46a6">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <rdfs:label xml:lang="en">has additional attributes</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_8f71ec98_a3b3_4f5b_b39f_dab911dd0b9f -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_8f71ec98_a3b3_4f5b_b39f_dab911dd0b9f">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_27e9637c_fe4f_4875_8d16_087da4a1cb00"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <owlr:python_name>uses_input_template</owlr:python_name>
        <rdfs:comment>API Endpoint will accept input following the JSON template presented here.</rdfs:comment>
        <rdfs:label xml:lang="en-gb">uses input template</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_ee1f7846_6528_4e64_9e86_cc34af99f912 -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_ee1f7846_6528_4e64_9e86_cc34af99f912">
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_27e9637c_fe4f_4875_8d16_087da4a1cb00"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <owlr:python_name>uses_output_template</owlr:python_name>
        <rdfs:comment>API Endpoint will provide ouptut following the JSON template presented here.</rdfs:comment>
        <rdfs:label xml:lang="en-gb">uses output template</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8l3NnmqnvjfzJkS5rDNvFm -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8l3NnmqnvjfzJkS5rDNvFm">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topDataProperty"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:domain rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#positiveInteger"/>
        <owlr:python_name>has_period</owlr:python_name>
        <rdfs:comment xml:lang="en-gb">This property contains the information  about the length of the temporal designation of a behaviour. A cyclic behaviour that has a period is a periodic behaviour, and an acyclic behaviour that has a defined period is considered to be a time-out behaviour, i.e. a one-shot behaviour that runs its main loop only after a specific period of time passes.</rdfs:comment>
        <rdfs:label xml:lang="en-gb">has period</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBffMlV8TQxoNtbLRneUYsb -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBffMlV8TQxoNtbLRneUYsb">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topDataProperty"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <owlr:python_name>has_system_features</owlr:python_name>
        <rdfs:comment xml:lang="en-gb">JSON description of the basic features of the modelled system, e.g. {&quot;number of agents&quot;: 10}</rdfs:comment>
        <rdfs:label xml:lang="en-gb">has system features</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDxmweKrzFGij8P5HOOsXZK -->

    <owl:DatatypeProperty rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDxmweKrzFGij8P5HOOsXZK">
        <rdfs:subPropertyOf rdf:resource="http://www.w3.org/2002/07/owl#topDataProperty"/>
        <rdf:type rdf:resource="http://www.w3.org/2002/07/owl#FunctionalProperty"/>
        <rdfs:range rdf:resource="http://www.w3.org/2001/XMLSchema#string"/>
        <owlr:python_name>has_name</owlr:python_name>
        <rdfs:label xml:lang="en-gb">has name</rdfs:label>
    </owl:DatatypeProperty>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->

    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact -->

    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact">
        <owlr:python_name>Artefact</owlr:python_name>
        <rdfs:label xml:lang="en-gb">Artefact</rdfs:label>
    </rdf:Description>
    


    <!-- http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan -->

    <owl:Class rdf:about="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Plan">
        <rdfs:label xml:lang="en-gb">Plan</rdfs:label>
    </owl:Class>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Objective -->

    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Objective">
        <owlr:python_name>Objective</owlr:python_name>
        <rdfs:label xml:lang="en-gb">Objective</rdfs:label>
    </rdf:Description>
    


    <!-- http://personales.upv.es/ccarrasc/ooooaflsmas#Role -->

    <rdf:Description rdf:about="http://personales.upv.es/ccarrasc/ooooaflsmas#Role">
        <owlr:python_name>Role</owlr:python_name>
        <rdfs:label xml:lang="en-gb">Role</rdfs:label>
    </rdf:Description>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_27e9637c_fe4f_4875_8d16_087da4a1cb00 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_27e9637c_fe4f_4875_8d16_087da4a1cb00">
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq"/>
        <rdfs:label xml:lang="en-gb">RestAPI Endpoint</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_30630ea0_e2d1_4057_8419_5ec603426309 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_30630ea0_e2d1_4057_8419_5ec603426309">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8EpacdsHKWIyPDRwsmkSta"/>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8l3NnmqnvjfzJkS5rDNvFm"/>
                        <owl:someValuesFrom rdf:resource="http://www.w3.org/2001/XMLSchema#positiveInteger"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8EpacdsHKWIyPDRwsmkSta"/>
        <rdfs:label xml:lang="en-gb">Time-out Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLClass_b2094825_e79e_4d64_8f85_bb17d5e892db">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Activity"/>
        <owlr:python_name>Activity</owlr:python_name>
        <rdfs:comment>Activity that will be translated into an implementation template.</rdfs:comment>
        <rdfs:label xml:lang="en-gb">Activity</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#Behavior"/>
        <rdfs:label xml:lang="en-gb">Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8EpacdsHKWIyPDRwsmkSta -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8EpacdsHKWIyPDRwsmkSta">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_1dd27442_1507_4890_8c6b_89ff9a5a4f49"/>
                        <owl:hasValue rdf:datatype="http://www.w3.org/2001/XMLSchema#boolean">false</owl:hasValue>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:label xml:lang="en-gb">One Shot Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R9lwUrkbSftwsmlPcopLgzT">
        <owlr:python_name>Agent</owlr:python_name>
        <rdfs:comment>MAGO-Ag agent that will be translated into an implementation template</rdfs:comment>
        <rdfs:label xml:lang="en-gb">Agent</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBzqbNmJP5lfpPIgYCvLGDa -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBzqbNmJP5lfpPIgYCvLGDa">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/JaCalIVE_Ontology#Artifact"/>
        <rdfs:label xml:lang="en-gb">Software Artefact</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCdOsGdA0yrGZdnzgQesQx7 -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCdOsGdA0yrGZdnzgQesQx7">
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RBzqbNmJP5lfpPIgYCvLGDa"/>
        <rdfs:label xml:lang="en-gb">Agent Host Server</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RChP69av2xqRwh6LALfwsKq">
        <rdfs:label xml:lang="en-gb">Artefact Resource</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCjIVQ3sTEfJR8m9yEY3Mhe -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCjIVQ3sTEfJR8m9yEY3Mhe">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#OWLDataProperty_1dd27442_1507_4890_8c6b_89ff9a5a4f49"/>
                        <owl:hasValue rdf:datatype="http://www.w3.org/2001/XMLSchema#boolean">true</owl:hasValue>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:label xml:lang="en-gb">Cyclic Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDMv7ayhi9xuRAzbfALLhKS -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDMv7ayhi9xuRAzbfALLhKS">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RC6rldf2VsWBaHtUobIELQL"/>
                        <owl:someValuesFrom rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
                    </owl:Restriction>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCPuCz8AunmmX3PS6A0U0XL"/>
                        <owl:someValuesFrom rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
        <rdfs:label xml:lang="en-gb">Finite State Machine Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDgoxtmVWC61IdqtaNK9QBf">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                    <owl:Class>
                        <owl:unionOf rdf:parseType="Collection">
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8ZWdA7HWDzIIwhQZrSwAIj"/>
                                <owl:someValuesFrom rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                            </owl:Restriction>
                            <owl:Restriction>
                                <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDGoFrovO0PhBCQGPkFeUuR"/>
                                <owl:someValuesFrom rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R7dQDUF81SOJC29Sctpy6aP"/>
                            </owl:Restriction>
                        </owl:unionOf>
                    </owl:Class>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:subClassOf rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8EpacdsHKWIyPDRwsmkSta"/>
        <rdfs:label xml:lang="en-gb">State Behaviour</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDl2yHd2gEWyEDMtUcL67d -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RDl2yHd2gEWyEDMtUcL67d">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
        <rdfs:label xml:lang="en-gb">Individual Knowledge Artefact</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RWOhadIHzH5yfBTDwuDICD -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RWOhadIHzH5yfBTDwuDICD">
        <rdfs:subClassOf rdf:resource="http://personales.upv.es/ccarrasc/ooooaflsmas#KnowledgeArtifact"/>
        <rdfs:label xml:lang="en-gb">Organisational Knowledge Artefact</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#Rce2iHbgKH3gy3TygYasFi -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#Rce2iHbgKH3gy3TygYasFi">
        <rdfs:label xml:lang="en-gb">System</rdfs:label>
    </owl:Class>
    


    <!-- https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#Rip7CRo5Sv8C6NFZrhvEDH -->

    <owl:Class rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#Rip7CRo5Sv8C6NFZrhvEDH">
        <owl:equivalentClass>
            <owl:Class>
                <owl:intersectionOf rdf:parseType="Collection">
                    <rdf:Description rdf:about="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#RCjIVQ3sTEfJR8m9yEY3Mhe"/>
                    <owl:Restriction>
                        <owl:onProperty rdf:resource="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx#R8l3NnmqnvjfzJkS5rDNvFm"/>
                        <owl:someValuesFrom rdf:resource="http://www.w3.org/2001/XMLSchema#positiveInteger"/>
                    </owl:Restriction>
                </owl:intersectionOf>
            </owl:Class>
        </owl:equivalentClass>
        <rdfs:label xml:lang="en-gb">Periodic Behaviour</rdfs:label>
    </owl:Class>
</rdf:RDF>



<!-- Generated by the OWL API (version 4.5.29.2024-05-13T12:11:03Z) https://github.com/owlcs/owlapi -->

