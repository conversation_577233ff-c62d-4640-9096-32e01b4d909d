<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<catalog prefer="public" xmlns="urn:oasis:names:tc:entity:xmlns:xml:catalog">
    <uri id="Imports Wizard Entry" name="http://www.semanticweb.org/bogdan/ontologies/2016/11/MAMbO5" uri="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Previous%20Work/MAMbO5.owl"/>
    <group id="Folder Repository, directory=, recursive=true, Auto-Update=true, version=2" prefer="public" xml:base="">
        <uri id="Automatically generated entry, Timestamp=1740153513074" name="duplicate:http://dragon.foi.hr/mago-a.owx" uri="MAGO-Ag%20copy.owx"/>
        <uri id="Automatically generated entry, Timestamp=1740153513074" name="duplicate:http://dragon.foi.hr/mago-a.owx" uri="MAGO-Ag%20with%20data.owx"/>
        <uri id="Automatically generated entry, Timestamp=1740153513074" name="http://www.semanticweb.org/bogdan/ontologies/2024/11/untitled-ontology-45" uri="simple_onto.owx"/>
        <uri id="Automatically generated entry, Timestamp=1740153513074" name="https://raw.githubusercontent.com/AILab-FOI/MAGO/refs/heads/main/Deliverables/Phase%201/Implementation/MAGO-Ag.owx" uri="MAGO-Ag.owx"/>
    </group>
</catalog>
