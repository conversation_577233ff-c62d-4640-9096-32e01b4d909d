This is LuaHBTeX, Version 1.17.0 (TeX Live 2023/Debian)  (format=lualatex 2024.12.12)  13 DEC 2024 10:44
 restricted system commands enabled.
**index.tex
(./index.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
Lua module: luaotfload 2023-08-31 3.26 Lua based OpenType font support
Lua module: lualibs 2023-07-13 2.76 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2023-07-13 2.76 ConTeXt Lua libraries -- extended c
ollection.
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2023/texmf-var
/luatex-cache/generic/names".
luaotfload | init : Loading fontloader "fontloader-2023-08-19.lua" from kpse-res
olved path "/usr/share/texlive/texmf-dist/tex/luatex/luaotfload/fontloader-2023-
08-19.lua".
Lua-only attribute luaotfload@noligature = 1
luaotfload | init : Context OpenType loader version 3.133
Inserting `luaotfload.node_processor' in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' in `hpack_filter'.
Inserting `luaotfload.glyph_stream' in `glyph_stream_provider'.
Inserting `luaotfload.define_font' in `define_font'.
Lua-only attribute luaotfload_color_attribute = 2
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2023/texmf-var
/luatex-cache/generic/names".
Inserting `luaotfload.harf.strip_prefix' in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' in `find_truetype_file'.
Removing  `luaotfload.glyph_stream' from `glyph_stream_provider'.
Inserting `luaotfload.harf.glyphstream' in `glyph_stream_provider'.
Inserting `luaotfload.harf.finalize_vlist' in `post_linebreak_filter'.
Inserting `luaotfload.harf.finalize_hlist' in `hpack_filter'.
Inserting `luaotfload.cleanup_files' in `wrapup_run'.
Inserting `luaotfload.harf.finalize_unicode' in `finish_pdffile'.
Inserting `luaotfload.glyphinfo' in `glyph_info'.
Lua-only attribute luaotfload.letterspace_done = 3
Inserting `luaotfload.aux.set_sscale_dimens' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_font_index' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.patch_cambria_domh' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.fixup_fontdata' in `luaotfload.patch_font_unsafe'.
Inserting `luaotfload.aux.set_capheight' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_xheight' in `luaotfload.patch_font'.
Inserting `luaotfload.rewrite_fontname' in `luaotfload.patch_font'.
Inserting `tracingstacklevels' in `input_level_string'.

! LaTeX Error: File `LaTeX Templates/Templates/Document.tex' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: tex)

Enter file name: X

! Emergency stop.
<read *> 
   
l.1 \input{LaTeX Templates/Templates/Document}
                                            
*** (cannot \read from terminal in nonstop modes)



Here is how much of LuaTeX's memory you used:
 21 strings out of 476553
 100000,1977958 words of node,token memory allocated 305 words of node memory still in use:
   1 hlist, 1 dir, 3 kern, 1 glyph, 1 attribute, 39 glue_spec, 1 attribute_list,
 2 if_stack nodes
   avail lists: 2:8,3:3,4:1
 22129 multiletter control sequences out of 65536+600000
 14 fonts using 591679 bytes
 18i,0n,26p,102b,17s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
