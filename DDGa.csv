ConceptName;Description
Gamification mechanics;A functional game design element that provides actions or control in a system, e.g. points, leaderboards, levels, challenges. Mechanics make up the functioning components of the game and allow the designer control over player actions.
Gamification dynamics;The reactions and behaviours that emerge from gameplay as a response to mechanics. For example, competition among players arises from certain mechanics such as leaderboards, while team-based challenges might result in cooperation. Dynamics are individuals' reactions as a response to implemented mechanics.
Game component;A specific gamification element or token in the system, e.g. a badge awarded for achievements, a ranked list of players by score, a numerical score that can be earned, etc. These are the visible artefacts of gamification given to or seen by players.
Player;The actor or agent who participates in the gamified system. Players are classified by type and can have a profile.
Player type;A categorisation of player/users by motivational or behavioural profile. OntoGamif's user typology includes 10 types combining <PERSON><PERSON>'s and Hexad models. GaDO includes player types from the BrainHex model. A player type describes how a player tends to engage with the system (e.g. achievers seek competency and challenges, socialisers seek social interaction etc.).
Personality trait;A personality dimension of an actor (e.g. user or agent) defined by the Five Factor model: openness, conscientiousness, extraversion, agreeableness, neuroticism.
Gamification objective;The goal or purpose of implementing gamification in a system - essentially why we are gamifying. For example: increase motivation, improve productivity, or encourage desired behaviour.
Target behaviour;A specific user behaviour to encourage or change via gamification. GaTO defines target behaviour categories like participation, performance, exploration, enjoyment, effectiveness, and competition. For example, \enquote{increase forum posts} might be a target behaviour under participation.
Engagement loop;A cyclical interaction loop consisting of motivation \(\rightarrow\) action \(\rightarrow\) feedback, which sustains engagement over time. In GaTO, an engagement loop is a type of activity loop, and it ties together game elements providing motivation, the player's action, and feedback (a game mechanic delivering response). Essentially, it models how the system continually engages the player.
