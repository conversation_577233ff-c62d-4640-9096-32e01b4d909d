\NeedsTeXFormat{LaTeX2e}
\ProvidesClass{LaTeX Templates/Templates/magobook}[MAGO Book Document Class]

% \RequirePackage[fulladjust]{marginnote}

% Handle margin notes
% \let\marginpar\marginnote
% \let\marginnoted\marginnote
% \let\marginnote\undefined


% Base class
\LoadClass[a4paper,notoc,nobib,british]{tufte-book}
% Required packages
% \usepackage{savesym}


% \RequirePackage{aurical}
\RequirePackage[T1]{fontenc}
\RequirePackage[british]{babel}
% \RequirePackage{fontspec}
\RequirePackage{fontawesome}
\RequirePackage{csvsimple}
\RequirePackage{xifthen}
\RequirePackage{xparse}
\RequirePackage{xspace}
\RequirePackage{pgffor}
\RequirePackage{hyperref}
\RequirePackage{multicol}
\RequirePackage{amsmath,amsfonts,amssymb}
\RequirePackage{mathtools}
\RequirePackage{wasysym}
\RequirePackage{caption}
\RequirePackage{booktabs}
\RequirePackage{longtable}
\RequirePackage{array}
\RequirePackage{diagbox}
\RequirePackage{placeins}
\RequirePackage{qrcode}
\RequirePackage{epigraph}
\RequirePackage{imakeidx}
\RequirePackage{enotez}
\RequirePackage{amssymb}
\RequirePackage{rotating}

% \input{LaTeX Templates/Preambles/Marginnote}

% Make index
\makeindex

% Use endnotes instead of footnotes
\let\footnote=\endnote

% Include preamble files
\input{LaTeX Templates/Preambles/Listings}
\input{LaTeX Templates/Preambles/BibLaTeX}
\input{LaTeX Templates/Preambles/Colours}
\input{LaTeX Templates/Preambles/Tikz}
\input{LaTeX Templates/Preambles/Custom Commands}
\input{LaTeX Templates/Preambles/Acronyms}
\input{LaTeX Templates/Preambles/Whatevers Left Tufte Header}
\input{LaTeX Templates/Preambles/Cleveref}
\input{LaTeX Templates/Preambles/Content Boxes}
\input{LaTeX Templates/Preambles/Itemize}

% Set counters
\setcounter{secnumdepth}{2}
\setcounter{tocdepth}{2}

% Title-related commands
\makeatletter
\newcommand{\plainsubtitle}{}%     plain-text-only subtitle
\newcommand{\subtitle}[1]{%
  \gdef\@subtitle{#1}%
  \renewcommand{\plainsubtitle}{#1}% use provided plain-text title
  \ifthenelse{\isundefined{\hypersetup}}%
    {}% hyperref is not loaded; do nothing
    {\hypersetup{pdftitle={\plaintitle: \plainsubtitle{}}}}% set the PDF metadata title
}
\newcommand{\setpretitle}[1]{%
    \newcommand{\pretitle}{#1}%
}

% Custom title page
\renewcommand{\maketitlepage}[0]{%
    \cleardoublepage%
    \AddToHook{shipout/background}{%
        \put (0in,-\paperheight){\includegraphics[width=\paperwidth, height=\paperheight]{LaTeX Templates/Common Figures/MAGO Background Cover.pdf}}%
    }
    {%
    \sffamily%
    \begin{fullwidth}%
        \fontsize{18}{20}\selectfont\par\noindent{\allcaps{\thanklessauthor}}%
        \vspace{11.5pc}%
        \newline%
        \begin{minipage}{\linewidth}
            \noindent
            \fontsize{10}{12}\textcolor{Colour Cover Title}{\textsc {\pretitle}}\vspace{.5em}
            
            \fontsize{30}{30}\textcolor{Colour Cover Title}{\allcaps{\plaintitle}}%
        \end{minipage}%
        \newline%
        \vspace{2em}%
        \fontsize{18}{20}\selectfont\noindent\textcolor{Colour Cover Subtitle}{\textsc{\plainsubtitle}}%
        \vfill%
        \fontsize{14}{16}\selectfont\par\noindent\textsc{\thanklesspublisher}%
    \end{fullwidth}%
    }
    \thispagestyle{empty}%
    \clearpage%
    \RemoveFromHook{shipout/background}
}
\makeatother

% Main document structure
\AtBeginDocument{%
    \frontmatter
    \maketitle
    \cleardoublepage
    \input{LaTeX Templates/Preambles/Copyright Page}
    {
    \hypersetup{linkcolor=titlered}
    \tableofcontents
    }
    \mainmatter
    \setcounter{page}{1}
}

\AtEndDocument{%
    \backmatter
    \begin{fullwidth}
        \printbibliography
        \printendnotes
        \printindex
    \end{fullwidth}
}