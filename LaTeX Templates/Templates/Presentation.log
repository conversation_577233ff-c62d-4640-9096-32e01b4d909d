This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.5.11)  24 JUN 2025 14:56
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**Presentation.tex
(./Presentation.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-04-14>
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamer.cls
Document Class: beamer 2025/02/04 v3.72 A class for typesetting presentations
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasemodes.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count272
)
\beamer@tempbox=\box53
\beamer@tempcount=\count273
\c@beamerpauses=\count274

(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasedecode.sty
\beamer@slideinframe=\count275
\beamer@minimum=\count276
\beamer@decode@box=\box54
)
\beamer@commentbox=\box55
\beamer@modecount=\count277
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
\headdp=\dimen146
\footheight=\dimen147
\sidebarheight=\dimen148
\beamer@tempdim=\dimen149
\beamer@finalheight=\dimen150
\beamer@animht=\dimen151
\beamer@animdp=\dimen152
\beamer@animwd=\dimen153
\beamer@leftmargin=\dimen154
\beamer@rightmargin=\dimen155
\beamer@leftsidebar=\dimen156
\beamer@rightsidebar=\dimen157
\beamer@boxsize=\dimen158
\beamer@vboxoffset=\dimen159
\beamer@descdefault=\dimen160
\beamer@descriptionwidth=\dimen161
\beamer@lastskip=\skip49
\beamer@areabox=\box56
\beamer@animcurrent=\box57
\beamer@animshowbox=\box58
\beamer@sectionbox=\box59
\beamer@logobox=\box60
\beamer@linebox=\box61
\beamer@sectioncount=\count278
\beamer@subsubsectionmax=\count279
\beamer@subsectionmax=\count280
\beamer@sectionmax=\count281
\beamer@totalheads=\count282
\beamer@headcounter=\count283
\beamer@partstartpage=\count284
\beamer@sectionstartpage=\count285
\beamer@subsectionstartpage=\count286
\beamer@animationtempa=\count287
\beamer@animationtempb=\count288
\beamer@xpos=\count289
\beamer@ypos=\count290
\beamer@ypos@offset=\count291
\beamer@showpartnumber=\count292
\beamer@currentsubsection=\count293
\beamer@coveringdepth=\count294
\beamer@sectionadjust=\count295
\beamer@toclastsection=\count296
\beamer@tocsectionnumber=\count297

(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseoptions.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip50
\beamer@paperheight=\skip51

(/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count298
\Gm@cntv=\count299
\c@Gm@tempcnt=\count300
\Gm@bindingoffset=\dimen162
\Gm@wd@mp=\dimen163
\Gm@odd@mp=\dimen164
\Gm@even@mp=\dimen165
\Gm@layoutwidth=\dimen166
\Gm@layoutheight=\dimen167
\Gm@layouthoffset=\dimen168
\Gm@layoutvoffset=\dimen169
\Gm@dimlist=\toks18
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.te
x
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen170
\pgfutil@tempdimb=\dimen171
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box62
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfil
tered.code.tex
\pgfkeys@tmptoks=\toks22
)))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex
\pgf@x=\dimen172
\pgf@xa=\dimen173
\pgf@xb=\dimen174
\pgf@xc=\dimen175
\pgf@y=\dimen176
\pgf@ya=\dimen177
\pgf@yb=\dimen178
\pgf@yc=\dimen179
\c@pgf@counta=\count301
\c@pgf@countb=\count302
\c@pgf@countc=\count303
\c@pgf@countd=\count304
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen180
\pgfmath@count=\count305
\pgfmath@box=\box63
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.
tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic
.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigo
nometric.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.rando
m.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.compa
rison.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.
code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round
.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.
code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integ
erarithmetics.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count306
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen181
\Gin@req@width=\dimen182
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen183
\pgf@y=\dimen184
\pgf@xa=\dimen185
\pgf@ya=\dimen186
\pgf@xb=\dimen187
\pgf@yb=\dimen188
\pgf@xc=\dimen189
\pgf@yc=\dimen190
\pgf@xd=\dimen191
\pgf@yd=\dimen192
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count307
\c@pgf@countb=\count308
\c@pgf@countc=\count309
\c@pgf@countd=\count310
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count311
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.d
ef
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-p
df.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.
code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count312
\pgfsyssoftpath@bigbuffer@items=\count313
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.
code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.co
de.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen193
\pgf@picmaxx=\dimen194
\pgf@picminy=\dimen195
\pgf@picmaxy=\dimen196
\pgf@pathminx=\dimen197
\pgf@pathmaxx=\dimen198
\pgf@pathminy=\dimen199
\pgf@pathmaxy=\dimen256
\pgf@xx=\dimen257
\pgf@xy=\dimen258
\pgf@yx=\dimen259
\pgf@yy=\dimen260
\pgf@zx=\dimen261
\pgf@zy=\dimen262
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconst
ruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen263
\pgf@path@lasty=\dimen264
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage
.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen265
\pgf@shorten@start@additional=\dimen266
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.co
de.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box64
\pgf@hbox=\box65
\pgf@layerbox@main=\box66
\pgf@picture@serial@count=\count314
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicst
ate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen267
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransform
ations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen268
\pgf@pt@y=\dimen269
\pgf@pt@temp=\dimen270
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.cod
e.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.c
ode.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathproce
ssing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.co
de.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen271
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.cod
e.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen272
\pgf@sys@shading@range@num=\count315
\pgf@shadingcount=\count316
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.cod
e.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.
code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box67
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.co
de.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretranspare
ncy.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.
code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.
tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count317
\XC@countmixins=\count318
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.s
ty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count319
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen273
\Hy@linkcounter=\count320
\Hy@pagecounter=\count321

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count322

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4040.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4040.
Package hyperref Info: Option `implicit' set `false' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count323

(/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen274

(/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count324
\Field@Width=\dimen275
\Fld@charsize=\dimen276
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
\Hy@abspage=\count325


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count326
\c@bookmark@seq@number=\count327

(/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaserequires.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasecompatibility.st
y) (/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasefont.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/sansmathaccent/sansmathaccent.sty
Package: sansmathaccent 2020/01/31
(/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)

(/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)


(/usr/local/texlive/2025/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))))
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetranslator.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/translator/translator.sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasemisc.sty)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetwoscreens.sty)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseoverlay.sty
\beamer@argscount=\count328
\beamer@lastskipcover=\skip52
\beamer@trivlistdepth=\count329
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetitle.sty)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasesection.sty
\c@lecture=\count330
\c@part=\count331
\c@section=\count332
\c@subsection=\count333
\c@subsubsection=\count334
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseframe.sty
\beamer@framebox=\box68
\beamer@frametitlebox=\box69
\beamer@zoombox=\box70
\beamer@zoomcount=\count335
\beamer@zoomframecount=\count336
\beamer@frametextheight=\dimen277
\c@subsectionslide=\count337
\beamer@frametopskip=\skip53
\beamer@framebottomskip=\skip54
\beamer@frametopskipautobreak=\skip55
\beamer@framebottomskipautobreak=\skip56
\beamer@envbody=\toks30
\framewidth=\dimen278
\c@framenumber=\count338
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseverbatim.sty
\beamer@verbatimfileout=\write4
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseframesize.sty
\beamer@splitbox=\box71
\beamer@autobreakcount=\count339
\beamer@autobreaklastheight=\dimen279
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseframecomponents.
sty
\beamer@footins=\box72
) (/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasecolor.sty)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasenotes.sty
\beamer@frameboxcopy=\box73
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetoc.sty)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetemplates.sty
\beamer@sbttoks=\toks33

(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseauxtemplates.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaseboxes.sty
\bmb@box=\box74
\bmb@colorbox=\box75
\bmb@boxwidth=\dimen280
\bmb@boxheight=\dimen281
\bmb@prevheight=\dimen282
\bmb@temp=\dimen283
\bmb@dima=\dimen284
\bmb@dimb=\dimen285
\bmb@prevheight=\dimen286
)
\beamer@blockheadheight=\dimen287
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbaselocalstructure.s
ty (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip57
\c@figure=\count340
\c@table=\count341
\abovecaptionskip=\skip58
\belowcaptionskip=\skip59
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasenavigation.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasenavigationsymbol
s.tex)
\beamer@section@min@dim=\dimen288
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasetheorems.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip60

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen289
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen290
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count342
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count343
\leftroot@=\count344
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count345
\DOTSCASE@=\count346
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box76
\strutbox@=\box77
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen291
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count347
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count348
\dotsspace@=\muskip18
\c@parentequation=\count349
\dspbrk@lvl=\count350
\tag@help=\toks36
\row@=\count351
\column@=\count352
\maxfields@=\count353
\andhelp@=\toks37
\eqnshift@=\dimen292
\alignsep@=\dimen293
\tagshift@=\dimen294
\tagwidth@=\dimen295
\totwidth@=\dimen296
\lineht@=\dimen297
\@envbody=\toks38
\multlinegap=\skip61
\multlinetaggap=\skip62
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip63
\thm@postskip=\skip64
\thm@headsep=\skip65
\dth@everypar=\toks45
)
\c@theorem=\count354
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerbasethemes.sty))
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerthemedefault.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerfontthemedefault.sty
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamercolorthemedefault.st
y)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerinnerthemedefault.st
y
\beamer@dima=\dimen298
\beamer@dimb=\dimen299
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/beamer/beamerouterthemedefault.st
y))) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
))
! Emergency stop.
<*> Presentation.tex
                    
*** (job aborted, no legal \end found)

 
Here is how much of TeX's memory you used:
 20249 strings out of 469608
 383117 string characters out of 5470606
 744270 words of memory out of 5000000
 46722 multiletter control sequences out of 15000+600000
 628203 words of font info for 43 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 128i,0n,123p,411b,450s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
