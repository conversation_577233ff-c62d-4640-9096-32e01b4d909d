% % % % % % % % % %
% This template is based on https://github.com/matze/mtheme
% % % % % % % % % %

\documentclass[11pt, aspectratio=169]{beamer}
\usepackage[T1]{fontenc}

\newcommand{\makePreface}[1]{%

    \usepackage{fontspec}
    \usepackage{fontawesome}

    \usepackage{FiraSans}

    \setmainfont{Fira Sans Light}

    \setsansfont[
      ItalicFont={Fira Sans Light Italic},
      BoldFont={Fira Sans},
      BoldItalicFont={Fira Sans Italic}
    ]{Fira Sans Light}
    \setmonofont[BoldFont={Fira Mono Medium}]{Fira Mono}
    
    \AtBeginEnvironment{tabular}{%
      \addfontfeature{Numbers={Monospaced}}
    }
    
    
    \input{LaTeX Templates/Preambles/Colours}

    #1

    \mode<presentation>
    {
        \usetheme[
            progressbar=frametitle,
            numbering=fraction,
            subsectionpage=simple,
            sectionpage=progressbar,
            block=fill
        ]{moloch}
    }

    \setbeamercolor{block title}{bg=blockcolour, fg=maincolour}
    \setbeamercolor{block body}{bg=blockcolour!10!maincolour, fg=frontcolour}

    \setbeamercolor{block title example}{bg=examplecolour, fg=maincolour}
    \setbeamercolor{block body example}{bg=examplecolour!10!maincolour, fg=frontcolour}
    
    \input{LaTeX Templates/Preambles/Beamer Settings}
    % \setsansfont{Ubuntu}
    % \setmonofont{Ubuntu Mono}
    
    % \setbeamertemplate{caption}{\raggedright\textsc\insertcaption\par} % suppress figure numbering in captions
    %\numberwithin{figure}{section} % count captions by sections


    \input{LaTeX Templates/Preambles/Tikz}
    \input{LaTeX Templates/Preambles/Custom Commands}
    \input{LaTeX Templates/Preambles/Acronyms}
    \input{LaTeX Templates/Preambles/Itemize}
    \input{LaTeX Templates/Preambles/Listings}
    \input{LaTeX Templates/Preambles/Cleveref}
    \input{LaTeX Templates/Preambles/BibLaTeX}
    \renewcommand*{\bibfont}{\tiny}
    \input{LaTeX Templates/Preambles/Custom Sequence Items}
    \input{LaTeX Templates/Preambles/Chart Donut}
            
    \usepackage{booktabs}
    \usepackage[scale=1]{ccicons}
    
    \usepackage{pgfplots}
    \usepgfplotslibrary{dateplot}
    
    \usepackage{appendixnumberbeamer}
    \usepackage{subcaption}
    % \usepackage{subfigure}
    \usepackage{graphicx}
    \usepackage{qrcode}
    \usepackage{tabularx}
    \usepackage{hyperref}
    \usepackage{multicol}
    \setlength{\columnsep}{1em}
    \usepackage{pifont}
    \usepackage{setspace}

    \begin{document}    
}


\newcommand{\makeTableOfContents}{
    %footer
    \setbeamertemplate{footline}{%
        \leavevmode%
        \vspace{2pt}
        \hbox{%
            \begin{beamercolorbox}[wd=\paperwidth,ht=3ex,dp=1.125ex]{palette quaternary}%
                \begin{columns}[T,onlytextwidth]
                    \column{0.33\paperwidth}
                    
                    \column{0.33\paperwidth}
                    \centering
                    \textcolor{footertext}{\insertshortauthor~@~\insertshortsubtitle}
                    
                    \column{0.33\paperwidth}
                \end{columns}
            \end{beamercolorbox}%
        }
    }
    
    \begin{frame}{\prijevod{Sadržaj}{Contents}}
        \setbeamertemplate{section in toc}[sections numbered]
        \tableofcontents[hideallsubsections]
        % \begin{columns}[t]
        %     \begin{column}{.3\textwidth}
           %      \tableofcontents[hideallsubsections,sections={1-5}]
        %     \end{column}
        %     \begin{column}{.3\textwidth}
           %      \tableofcontents[hideallsubsections,sections={6-10}]
        %     \end{column}
        %     \begin{column}{.3\textwidth}
           %      \tableofcontents[hideallsubsections,sections={11-16}]
        %     \end{column}
        % \end{columns}
    \end{frame}
    
    %redefine footer
    \setbeamertemplate{footline}{%
        \leavevmode%
        \vspace{2pt}
        \hbox{%
            \begin{beamercolorbox}[wd=\paperwidth,ht=3ex,dp=1.125ex]{palette quaternary}%
                \begin{columns}[T,onlytextwidth]
                    \column{0.33\paperwidth}
                    \hspace{1pt}
                    \textcolor{footertext}{\insertshorttitle~>~\insertsectionhead}
                    
                    \column{0.33\paperwidth}
                    \centering
                    \textcolor{footertext}{\insertshortauthor~@~\insertshortsubtitle}
                    
                    \column{0.33\paperwidth}
                    \textcolor{footertext}{\hfill\insertframenumber{}~/~\inserttotalframenumber\hspace{0.2cm}}
                \end{columns}
            \end{beamercolorbox}%
        }
    }
    \setcounter{framenumber}{0}
}



% 
% MAIN PART
% 

% \input{#2}

\newcommand{\makeBibliography}{
    \section*{\prijevod{Popis literature}{Bibliography}}
    
    \begin{frame}[allowframebreaks]{\insertsection}
    
        \printbibliography[
            heading=none]
    
    \end{frame}
}

% 
% CLOSING
% 

\newcommand{\makeBack}{
    \section*{Acknowledgement}

    \begin{frame}{\insertsection}

        \begin{multicols}{2}

            \null\vfill

            \prijevod{
                \textbf{MOBODL-2023-08-5618}
                
                Ovaj projekt financirale su Europska unija i Hrvatska zaklada za znanost.
            }{
                \textbf{MOBODL-2023-08-5618}
                
                This project was funded by the European Union and the Croatian Science Foundation.
            }

            \columnbreak

            \centering
        
            \prijevod{
                \includegraphics[width=4cm]{LaTeX Templates/Common Figures/EU logo HR.pdf}
                
                \includegraphics[width=4cm]{LaTeX Templates/Common Figures/HRZZ logo HR.pdf}
            }{
                \includegraphics[width=4cm]{LaTeX Templates/Common Figures/EU logo EN.pdf}
                
                \includegraphics[width=4cm]{LaTeX Templates/Common Figures/HRZZ logo EN.pdf}
            }
        \end{multicols}
    
    \end{frame}

    \begin{frame}[standout]	
        \centering
        \large{\insertauthor}
        
        <EMAIL>

        \begin{multicols}{3}
            \includegraphics[width=4cm]{LaTeX Templates/Common Figures/QR Contact Signal.pdf}
            
            \includegraphics[width=4cm]{LaTeX Templates/Common Figures/QR Contact All.pdf}
            
            \includegraphics[width=4cm]{LaTeX Templates/Common Figures/QR Contact Telegram.pdf}
        \end{multicols}
        
        \scriptsize \insertinstitute
        
        \large \url{ai.foi.hr}
    \end{frame}
    
    \end{document}
}