\cleardoublepage

\begin{fullwidth}
\thispagestyle{empty}
\setlength{\parindent}{0pt}

\prijevod{
    Ovaj dokument sadrži dio rezultata i isporuka projekta broj MOBODL-2023-08-5618 kojeg financiraju Europska unija i Hrvatska zaklada za znanost.
}{
    This document contains some of the results and deliverables of the project ID \textbf{MOBODL-2023-08-5618} funded by the European Union and the Croatian Science Foundation.
}

\setlength{\parskip}{\baselineskip}

\vfill

\begin{multicols}{2}
    \centering
    \includegraphics[width=0.7\linewidth]{LaTeX Templates/Common Figures/EU logo EN.pdf}

    \includegraphics[width=0.7\linewidth]{LaTeX Templates/Common Figures/HRZZ logo EN.pdf}
\end{multicols}

\vfill

Authors: <AUTHORS>

Editor: <PERSON><PERSON><PERSON>, \href{mailto:<PERSON><PERSON><PERSON> <<EMAIL>>}{dokresa [at] foi.unizg.hr}.

Cover graphic generated by ChatGPT and DALL·E.

\vspace{50pt}

\par\smallcaps{\thanklesspublisher}
% \par\smallcaps{Published by \thanklesspublisher}

\par\href{http://ai.foi.hr}{\smallcaps{ai.foi.hr}}

\prijevod{
    \par Ovaj dokument licenciran je pod licencom \textit{Imenovanje-Nekomercijalno-Dijeli pod istim uvjetima 4.0 međunarodna} (CC BY-NC-SA 4.0). Potpun tekst licence dostupan je na sljedećoj adresi: \url{https://creativecommons.org/licenses/by-nc-sa/4.0/}.    
    }{
    \par This work is licensed under Creative Commons \textit{Attribution-NonCommercial-ShareAlike 4.0 International} (CC BY-NC-SA 4.0). To view a copy of this license, visit \url{https://creativecommons.org/licenses/by-nc-sa/4.0/}.
}


\begin{center}
    \href{https://creativecommons.org/licenses/by-nc-sa/4.0/deed.hr}{\includegraphics[width=84pt]{LaTeX Templates/Common Figures/Cc-by-nc-sa_icon.png}}
\end{center}

\par\textit{\prijevod{Verzija}{Version}: \monthyear}

\prijevod{
    U slučaju da nađete greške, pozivamo vas da ih slobodno prijavite na adresu e-pošte <EMAIL>.
}{
    In case you find any errors, do not hesitate to report them to \href{mailto:Bogdan Okresa Duric <<EMAIL>>}{dokresa [at] foi.unizg.hr}.
}
\end{fullwidth}