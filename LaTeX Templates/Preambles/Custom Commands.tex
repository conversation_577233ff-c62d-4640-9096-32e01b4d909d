\usepackage[linesnumbered]{algorithm2e}
\usepackage{newfloat}

\newcommand{\magoontologyname}{\mbox{MAGO-Ag}\xspace}
\newcommand{\magoag}{\mbox{MAGO-Ag}\xspace}
\newcommand{\magoga}{\mbox{MAGO-Ga}\xspace}
\newcommand{\given}{\mbox{GIVEn}\xspace}
\newcommand{\mambo}{\mbox{MAMbO5}\xspace}

\newcommand{\margina}[1]{
    \makebox[0pt][r]{\tiny#1\hspace*{-\linewidth}}%
}

\newcommand{\fusnota}[1]{%
    \footnote[frame]{\tiny#1}%
}

\newcommand{\customFigure}[4][1]{
    \begin{figure}
        \begin{minipage}[b]{0.7\linewidth}
            \centering
            \resizebox{#1\linewidth}{!}{#2}
        \end{minipage}
        \begin{minipage}[b]{0.05\linewidth}
        \hspace{0.1cm}
        \end{minipage}
        \begin{minipage}[b]{0.2\linewidth}
            \begin{tikzpicture}
                \node [text width = 3cm, anchor = south west, align=left] (caption) at (-0.1,-0.2){\caption{\newline#3}};
                \draw[very thick](0,-0.1)--(3,-0.1);
            \end{tikzpicture}
        \end{minipage}
        \label{fig:#4}
    \end{figure}
}

% Use example:
% \customFigure[1]{% how wide is the figure?
%     \includegraphics{path/to/the/figure.png}
% }{Caption to the figure}{figure-reference}

% this LaTeX code snippet defines a versatile colored box environment box that can be customized with different colors and titles. It also introduces two commands, \boxExample for examples and \boxTask for tasks, which simplify the creation of these boxes by predefining certain parameters like the color and title prefix, making it easier to maintain a consistent look for examples and tasks throughout a document.


\usepackage{tcolorbox}
\newtcolorbox{customBox}[3][]{
    colframe = #2!100,
    colback = #2!10,
    coltitle = #2!20,
    title = {\textbf{#3}},
    #1,
}

\newcommand{\boxExample}[2]{
    \begin{customBox}{maincolour}{\texttt{\prijevod{P.}{E.g.}}~#1}#2\end{customBox}
}
\newcommand{\boxTask}[2]{
    \begin{customBox}{taskcolour}{\texttt{\prijevod{Z.}{T.}}~#1}#2\end{customBox}
}



\newcommand{\prijevod}[2]{%
    \iflanguage{croatian}{#1}{%
    \iflanguage{british}{#2}{%
    \iflanguage{english}{#2}{}}}%
}

% O_HAI 
\newcommand*\circled[1]{\tikz[baseline=(char.base)]{%
            \node[shape=circle,draw,inner sep=0.5pt] (char) {#1};}}

\newcommand{\ohai}{O\_HAI \circled{4} Games}



\newcommand{\lookAt}[2][0pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-1pt}{\includegraphics[width=8pt]{LaTeX Templates/Common Figures/eye.pdf}}%
        \end{minipage}%
        \prijevod{vidi}{see}~#2%
    }%
}

\newcommand{\linktext}[3][0pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-0.5pt}{\includegraphics[width=6pt]{LaTeX Templates/Common Figures/link.pdf}}%
        \end{minipage}%
        \href{#2}{#3}%
    }%
}

\newcommand{\link}[2][3pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-1pt}{\includegraphics[width=6pt]{LaTeX Templates/Common Figures/link.pdf}}%
        \end{minipage}%
        \tiny\url{#2}%
    }%
}

\newcommand{\lstLineNumberRef}[3][0pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-2pt}{\includegraphics[width=8pt]{LaTeX Templates/Common Figures/code.png}}%
        \end{minipage}%
        \texttt{#2:#3}%
    }%
}

\newcommand{\file}[3][0pt]{\marginnote[#1]{\!\hspace{3.5pt}\raisebox{-2.5pt}{\href{#2}{\includegraphics[width=8pt]{LaTeX Templates/Common Figures/file.png}}}\enspace\prijevod{preuzmi}{download}~\href{#2}{#3}}}

\newcommand{\video}[2][3pt]{\marginnote[#1]{\,\hspace{2pt}\raisebox{-1.5pt}{\includegraphics[width=7pt]{LaTeX Templates/Common Figures/play}}\enspace\tiny\url{#2}}}

\newcommand{\videoNaziv}[3][3pt]{\marginnote[#1]{\,\hspace{2pt}\raisebox{-1.5pt}{\includegraphics[width=7pt]{LaTeX Templates/Common Figures/play}}\enspace\tiny\href{#2}{#3}}}

\newcommand{\customQuoteReferenced}[2]{%
    \begin{quotation}
        \raisebox{-4pt}{\Huge "}\!\marginnote{
            \begin{minipage}[t]{0.1\linewidth}\centering$\triangleleft$\end{minipage}\MakeLowercase{\prijevod{citat iz}{cited from}} \citeauthor{#1}: \citetitle{#1} \autocite{#1}}
            % $\triangleleft$\space citat iz \citeauthor{#1}: \citetitle{#1} \autocite{#1}}
        #2
    \end{quotation}
}

\newcommand{\customQuote}[1]{\enquote{\color{customQuote}{#1}}}

% \renewcommand{\alert}[1]{\textcolor{alertTextColour}{#1}}

% \newcommand{\tocnaslovdijela}{\addvspace{20pt}\begin{center}\textsc{\prijevod{hrvatski}{english}}\end{center}\addvspace{10pt}}

% Prints the month name (e.g., January) and the year (e.g., 2008)
\newcommand{\monthyear}{%
    \prijevod{
        \ifcase\month\or siječanj\or veljača\or ožujak\or travanj\or svibanj\or lipanj\or srpanj\or kolovoz\or rujan\or listopad\or studeni\or prosinac\fi\space\number\year.
    }{
        \ifcase\month\or January\or February\or March\or April\or May\or June\or July\or August\or September\or October\or November\or December\fi\space\number\year
    }
}

\newcommand{\activityFrame}[2]{
    \begin{frame}{\insertsection}
        \begin{tikzpicture}[overlay, remember picture]
            \draw [line width=1.5em, line join=round, line cap=round]
            ([yshift=-1.77cm] current page.west) --
            ([xshift=2cm, yshift=-1.77cm] current page.west) -- 
            ([xshift=3.25cm, yshift=1.75cm] current page.west) -- 
            ([xshift=-2.25cm, yshift=-0.75cm] current page.south) -- 
            ([xshift=-1cm, yshift=-1.77cm] current page.center) -- 
            ([xshift=1.25cm, yshift=-1.77cm] current page.center);
            
            \node[text=frontcolour, align=left] (A) 
            at ([xshift=3cm, yshift=-1.77cm] current page.center) 
            {\textbf{\textsc{\huge{Activity}}}};
            
            \node[text=frontcolour, align=left, below = 0.3em of A.south west, anchor=north west, text width=6cm] {#1};

            \ifthenelse{\equal{#2}{}}{}{
                \node[text=frontcolour] (I) 
                at ([xshift=0.5cm] A.east) 
                {\href{#2}{\faExternalLink}};
            }%
        \end{tikzpicture}
    \end{frame}
}
