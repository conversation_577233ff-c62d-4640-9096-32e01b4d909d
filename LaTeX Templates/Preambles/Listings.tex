\usepackage{caption}
\usepackage[newfloat=true]{minted}
\setminted{
    frame=lines,
    autogobble,
    stripall,
    breaklines,
    linenos,
    numbersep=1em,
    % numberblanklines=false,
    % fontsize=\tiny,
    highlightcolor=maincolour!20,
    style=arduino,
    tabsize=2,
    escapeinside=××,
}

\newminted[mintedPython]{python}{fontsize=\tiny}
\newminted[mintedBash]{bash}{fontsize=\tiny}
\newminted[mintedXML]{xml}{fontsize=\tiny}
\newminted[mintedText]{text}{fontsize=\tiny}

\newmintedfile[mintedFilePython]{python}{fontsize=\tiny}
\newmintedfile[mintedFileJS]{js}{fontsize=\tiny}
\newmintedfile[mintedFileHTML]{html}{fontsize=\tiny}
\newmintedfile[mintedFileBash]{bash}{fontsize=\tiny}
\newmintedfile[mintedFileText]{text}{fontsize=\tiny}

\newmintedfile[mintedFilePythonBlack]{python}{fontsize=\tiny, style=monokai}

\newmintinline[mintedInline]{text}{style=bw,bgcolor=minted-inline-text, fontsize=\small}
\newmintinline[mintedInlinePython]{python}{style=bw}
\newmintinline[mintedInlineBash]{bash}{style=bw}

\SetupFloatingEnvironment{listing}{name=\prijevod{Isječak koda}{Listing}}
\SetupFloatingEnvironment{listing}{listname=\prijevod{Popis isječaka koda}{List of Listings}}

\newenvironment{longlisting}{\captionsetup{type=listing}}{}